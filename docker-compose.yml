version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: gpulabs-postgres
    environment:
      POSTGRES_USER: gpulabs
      POSTGRES_PASSWORD: gpulabs123
      POSTGRES_DB: gpulabs
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: gpulabs-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data: