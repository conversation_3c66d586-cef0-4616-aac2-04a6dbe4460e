// GPU Type Definitions for GPULabs Platform

export interface GPUSpecifications {
  // Core GPU specifications
  memory: {
    size: number; // GB
    type: string; // GDDR6X, GDDR6, HBM2, etc.
    bandwidth: number; // GB/s
    busWidth: number; // bits
  };
  
  // Processing units
  cores: {
    cudaCores?: number; // NVIDIA
    streamProcessors?: number; // AMD
    executionUnits?: number; // Intel
    rtCores?: number; // Ray tracing cores
    tensorCores?: number; // AI/ML cores
  };
  
  // Clock speeds (MHz)
  clocks: {
    baseClock: number;
    boostClock: number;
    memoryClock: number;
  };
  
  // Manufacturing
  process: string; // 4nm, 5nm, 7nm, etc.
  transistors?: number; // billions
  dieSize?: number; // mm²
}

export interface PhysicalSpecifications {
  dimensions: {
    length: number; // mm
    width: number; // mm
    height: number; // mm (slot height)
  };
  weight?: number; // grams
  slots: number; // PCIe slots occupied
  cooling: {
    type: string; // Air, Liquid, Hybrid
    fans: number;
    heatpipes?: number;
  };
  ports: {
    displayPort?: number;
    hdmi?: number;
    dvi?: number;
    usbc?: number;
  };
}

export interface PowerRequirements {
  tdp: number; // watts
  recommendedPSU: number; // watts
  connectors: string[]; // ["8-pin", "6-pin"] or ["12VHPWR"]
  peakPower?: number; // watts
  idlePower?: number; // watts
}

export interface GPUFilters {
  manufacturer?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  memorySize?: number[];
  architecture?: string[];
  launchDateRange?: {
    start: Date;
    end: Date;
  };
  workload?: 'gaming' | 'content-creation' | 'ai-ml' | 'mining';
  performanceTarget?: {
    resolution: '1080p' | '1440p' | '4K';
    fps: number;
  };
}

export interface RecommendationCriteria {
  workload: 'gaming' | 'content-creation' | 'ai-ml' | 'mining';
  budget: {
    min: number;
    max: number;
  };
  currentSystem?: SystemSpecs;
  performanceTargets?: PerformanceTargets;
  futureProofing?: number; // years
  priorities?: {
    performance: number; // 1-10
    value: number; // 1-10
    efficiency: number; // 1-10
    features: number; // 1-10
  };
}

export interface SystemSpecs {
  cpu: {
    model: string;
    cores: number;
    threads: number;
    baseClock: number;
  };
  motherboard: {
    model: string;
    chipset: string;
    pcieSlots: string[]; // ["PCIe 4.0 x16", "PCIe 3.0 x8"]
  };
  psu: {
    wattage: number;
    efficiency: string; // 80+ Bronze, Gold, etc.
    connectors: string[];
  };
  case: {
    model?: string;
    maxGPULength: number; // mm
    maxGPUWidth: number; // mm
    maxGPUHeight: number; // mm
  };
  ram: {
    size: number; // GB
    speed: number; // MHz
    type: string; // DDR4, DDR5
  };
}

export interface PerformanceTargets {
  resolution: '1080p' | '1440p' | '4K' | '8K';
  targetFPS: number;
  rayTracing: boolean;
  dlss?: boolean;
  fsr?: boolean;
  gameSettings: 'Low' | 'Medium' | 'High' | 'Ultra';
}

export interface CompatibilityResult {
  overall: 'compatible' | 'warning' | 'incompatible';
  power: {
    status: 'compatible' | 'warning' | 'incompatible';
    required: number;
    available: number;
    connectors: {
      required: string[];
      available: string[];
      missing: string[];
    };
  };
  physical: {
    status: 'compatible' | 'warning' | 'incompatible';
    clearance: {
      length: { required: number; available: number };
      width: { required: number; available: number };
      height: { required: number; available: number };
    };
  };
  bottleneck: {
    cpuBottleneck: number; // percentage
    severity: 'none' | 'mild' | 'moderate' | 'severe';
    recommendation: string;
  };
  recommendations: string[];
}

export interface RetailerPrice {
  retailer: string;
  price: number;
  availability: 'in-stock' | 'limited' | 'out-of-stock';
  shipping: number;
  url: string;
  lastUpdated: Date;
  condition: 'new' | 'refurbished' | 'used';
}

export interface PriceHistory {
  gpuId: string;
  data: {
    date: Date;
    price: number;
    retailer: string;
  }[];
  trend: 'increasing' | 'decreasing' | 'stable';
  lowestPrice: {
    price: number;
    date: Date;
    retailer: string;
  };
  averagePrice: number;
}

export interface BenchmarkResult {
  testName: string;
  resolution: string;
  settings: string;
  fps?: number;
  score?: number;
  rayTracing?: boolean;
  dlss?: boolean;
  powerConsumption?: number;
  temperature?: number;
}

export interface GPUComparison {
  gpus: string[]; // GPU IDs
  specifications: Record<string, any>;
  performance: Record<string, BenchmarkResult[]>;
  pricing: Record<string, RetailerPrice[]>;
  recommendations: {
    best: string; // GPU ID
    value: string; // GPU ID
    performance: string; // GPU ID
    efficiency: string; // GPU ID
  };
}
// Addit
ional compatibility types
export interface CompatibilityCheck {
  gpuId: string
  system: SystemSpecs
  timestamp: Date
  result: CompatibilityResult
}

export interface PSUSpecs {
  wattage: number
  efficiency: string // 80+ Bronze, Gold, etc.
  connectors: string[]
  modular?: boolean
  brand?: string
  model?: string
}

export interface CaseSpecs {
  model?: string
  maxGPULength: number // mm
  maxGPUWidth: number // mm
  maxGPUHeight: number // mm
  airflow?: 'front-to-back' | 'bottom-to-top' | 'mixed'
  fanSupport?: {
    front: number
    top: number
    rear: number
  }
}

export interface CPUSpecs {
  model: string
  cores: number
  threads: number
  baseClock: number // MHz
  boostClock?: number // MHz
  architecture?: string
  socket?: string
  tdp?: number
}

export interface MotherboardSpecs {
  model: string
  chipset: string
  socket: string
  pcieSlots: PCIeSlot[]
  ramSlots: number
  maxRam: number
  formFactor: 'ATX' | 'mATX' | 'ITX' | 'E-ATX'
}

export interface PCIeSlot {
  version: string // PCIe 3.0, 4.0, 5.0
  lanes: number // x16, x8, x4, x1
  physical: string // x16, x8, x4, x1
  position: number
}

export interface PowerCompatibility {
  status: 'compatible' | 'warning' | 'incompatible'
  required: number
  available: number
  connectors: {
    required: string[]
    available: string[]
    missing: string[]
  }
  efficiency?: {
    load: number // percentage
    efficiency: number // percentage
    recommendation: string
  }
}

export interface PhysicalCompatibility {
  status: 'compatible' | 'warning' | 'incompatible'
  clearance: {
    length: { required: number; available: number }
    width: { required: number; available: number }
    height: { required: number; available: number }
  }
  cooling?: {
    airflow: 'adequate' | 'marginal' | 'insufficient'
    recommendation: string
  }
}

export interface BottleneckAnalysis {
  cpuBottleneck: number // percentage
  severity: 'none' | 'mild' | 'moderate' | 'severe'
  recommendation: string
  resolution?: {
    '1080p': number
    '1440p': number
    '4K': number
  }
  workload?: {
    gaming: number
    productivity: number
    rendering: number
  }
}

export interface CompatibilityReport {
  overall: 'compatible' | 'warning' | 'incompatible'
  score: number // 0-100
  issues: CompatibilityIssue[]
  recommendations: string[]
  estimatedPerformance?: {
    gaming: number // percentage of GPU potential
    productivity: number
    rendering: number
  }
}

export interface CompatibilityIssue {
  type: 'power' | 'physical' | 'bottleneck' | 'thermal' | 'other'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  solution: string
  cost?: 'low' | 'medium' | 'high'
}