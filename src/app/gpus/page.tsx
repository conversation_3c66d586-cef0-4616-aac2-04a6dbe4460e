'use client';

import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/layout/Layout';
import { GPUCard } from '@/components/ui/GPUCard';
import { Button } from '@/components/ui/Button';
import { LoadingCard } from '@/components/ui/Loading';
import { cn } from '@/lib/utils';

interface GPU {
  id: string;
  name: string;
  manufacturer: string;
  architecture: string;
  specifications: {
    memory: { size: number; type: string };
    power: { tdp: number };
    cores: { cuda?: number; stream?: number };
  };
  pricing?: {
    price: number;
    retailer: string;
  };
}

interface Filters {
  manufacturer: string[];
  memorySize: string[];
  priceRange: { min: number; max: number };
  architecture: string[];
}

const initialFilters: Filters = {
  manufacturer: [],
  memorySize: [],
  priceRange: { min: 0, max: 5000 },
  architecture: []
};

export default function GPUsPage() {
  const [gpus, setGpus] = useState<GPU[]>([]);
  const [filteredGpus, setFilteredGpus] = useState<GPU[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<Filters>(initialFilters);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'performance' | 'newest'>('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedForComparison, setSelectedForComparison] = useState<string[]>([]);
  const [expandedGpus, setExpandedGpus] = useState<Set<string>>(new Set());

  // Fetch GPUs from API
  useEffect(() => {
    const fetchGPUs = async () => {
      try {
        setLoading(true);
        // Fetch both hierarchical and legacy GPUs
        const [hierarchicalResponse, legacyResponse] = await Promise.all([
          fetch('/api/gpus/hierarchical?limit=500'),
          fetch('/api/gpus?limit=500')
        ]);
        if (hierarchicalResponse.ok && legacyResponse.ok) {
          const [hierarchicalData, legacyData] = await Promise.all([
            hierarchicalResponse.json(),
            legacyResponse.json()
          ]);

          console.log('Hierarchical API response:', hierarchicalData);
          console.log('Legacy API response:', legacyData);

          // Transform hierarchical data to hierarchical structure (NOT flat)
          const hierarchicalGPUs: any[] = [];
          const migratedGpuIds = new Set();

          // Add base GPUs with their variants nested
          hierarchicalData.data.baseGpus.forEach((baseGpu: any) => {
            // Track this GPU as migrated
            migratedGpuIds.add(baseGpu.name);

            // Transform variants
            const variants = baseGpu.variants?.map((variant: any) => ({
              id: variant.id,
              name: `${variant.modelName}`,
              manufacturer: variant.boardPartner,
              architecture: baseGpu.architecture,
              launchDate: baseGpu.launchDate,
              originalMsrp: baseGpu.originalMsrp ? Number(baseGpu.originalMsrp) : null,
              specifications: {
                memory: {
                  size: baseGpu.specifications?.memory?.size || 0,
                  type: baseGpu.specifications?.memory?.type || 'Unknown'
                },
                power: {
                  tdp: variant.powerRequirements?.tdp || baseGpu.powerRequirements?.tdp || 0
                },
                cores: {
                  cuda: baseGpu.specifications?.cores?.cudaCores || 0,
                  stream: baseGpu.specifications?.cores?.streamProcessors || 0
                }
              },
              powerRequirements: variant.powerRequirements || baseGpu.powerRequirements,
              pricing: variant.pricing?.length > 0 ? {
                price: Math.min(...variant.pricing.map((p: any) => Number(p.price))),
                retailer: 'Various'
              } : null,
              isVariant: true,
              baseGpuId: variant.baseGpuId,
              boardPartner: variant.boardPartner,
              sku: variant.sku,
              features: variant.features
            })) || [];

            // Add base GPU with nested variants
            hierarchicalGPUs.push({
              id: baseGpu.id,
              name: baseGpu.name,
              manufacturer: baseGpu.chipManufacturer,
              architecture: baseGpu.architecture,
              launchDate: baseGpu.launchDate,
              originalMsrp: baseGpu.originalMsrp ? Number(baseGpu.originalMsrp) : null,
              specifications: {
                memory: {
                  size: baseGpu.specifications?.memory?.size || 0,
                  type: baseGpu.specifications?.memory?.type || 'Unknown'
                },
                power: {
                  tdp: baseGpu.powerRequirements?.tdp || 0
                },
                cores: {
                  cuda: baseGpu.specifications?.cores?.cudaCores || 0,
                  stream: baseGpu.specifications?.cores?.streamProcessors || 0
                }
              },
              powerRequirements: baseGpu.powerRequirements,
              pricing: variants.length > 0 && variants.some(v => v.pricing) ? {
                price: Math.min(...variants.filter(v => v.pricing).map(v => v.pricing.price)),
                retailer: 'Various'
              } : null,
              isBaseGPU: true,
              variantCount: variants.length,
              variants: variants
            });
          });

          // Add legacy GPUs that haven't been migrated yet (as base GPUs without variants)
          legacyData.gpus.forEach((legacyGpu: any) => {
            // Check if this exact GPU name is already in hierarchical data
            if (!migratedGpuIds.has(legacyGpu.name)) {
              hierarchicalGPUs.push({
                id: legacyGpu.id,
                name: legacyGpu.name,
                manufacturer: legacyGpu.manufacturer,
                architecture: legacyGpu.architecture,
                launchDate: legacyGpu.launchDate,
                originalMsrp: legacyGpu.originalMsrp,
                specifications: {
                  memory: {
                    size: legacyGpu.specifications?.memory?.size || 0,
                    type: legacyGpu.specifications?.memory?.type || 'Unknown'
                  },
                  power: {
                    tdp: legacyGpu.powerRequirements?.tdp || 0
                  },
                  cores: {
                    cuda: legacyGpu.specifications?.cores?.cudaCores || 0,
                    stream: legacyGpu.specifications?.cores?.streamProcessors || 0
                  }
                },
                powerRequirements: legacyGpu.powerRequirements,
                pricing: legacyGpu.currentPrice ? {
                  price: legacyGpu.currentPrice,
                  retailer: 'Various'
                } : null,
                isLegacy: true,
                isBaseGPU: true,
                variantCount: 0,
                variants: []
              });
            }
          });

          console.log('Processed GPUs:', hierarchicalGPUs.length, 'total base GPUs');
          console.log('Migrated GPUs:', migratedGpuIds.size);
          console.log('Migrated GPU names:', Array.from(migratedGpuIds));
          console.log('Sample GPU:', hierarchicalGPUs[0]);
          console.log('RTX 4090 GPUs:', hierarchicalGPUs.filter(gpu => gpu.name.includes('4090')).map(gpu => gpu.name));
          setGpus(hierarchicalGPUs);
          setFilteredGpus(hierarchicalGPUs);
        } else {
          setError('Failed to fetch GPUs from one or more sources');
        }
      } catch (err) {
        setError('Error loading GPUs');
        console.error('Error fetching GPUs:', err);
        console.error('Full error details:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchGPUs();
  }, []);

  // Toggle GPU expansion
  const toggleGpuExpansion = (gpuId: string) => {
    setExpandedGpus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(gpuId)) {
        newSet.delete(gpuId);
      } else {
        newSet.add(gpuId);
      }
      return newSet;
    });
  };

  // Filter and sort GPUs
  useEffect(() => {
    let filtered = [...gpus];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(gpu => {
        const query = searchQuery.toLowerCase();

        // Search in base GPU
        const baseMatch = gpu.name.toLowerCase().includes(query) ||
          gpu.manufacturer.toLowerCase().includes(query) ||
          gpu.architecture?.toLowerCase().includes(query);

        // Search in variants
        const variantMatch = gpu.variants?.some((variant: any) =>
          variant.name.toLowerCase().includes(query) ||
          variant.manufacturer.toLowerCase().includes(query) ||
          variant.boardPartner?.toLowerCase().includes(query) ||
          variant.sku?.toLowerCase().includes(query)
        );

        return baseMatch || variantMatch;
      });
    }

    // Apply manufacturer filter
    if (filters.manufacturer.length > 0) {
      filtered = filtered.filter(gpu => filters.manufacturer.includes(gpu.manufacturer));
    }

    // Apply memory size filter
    if (filters.memorySize.length > 0) {
      filtered = filtered.filter(gpu => 
        filters.memorySize.includes(gpu.specifications.memory.size.toString())
      );
    }

    // Apply price range filter
    if (filters.priceRange.min > 0 || filters.priceRange.max < 5000) {
      filtered = filtered.filter(gpu => {
        const price = gpu.pricing?.price || 0;
        return price >= filters.priceRange.min && price <= filters.priceRange.max;
      });
    }

    // Apply architecture filter
    if (filters.architecture.length > 0) {
      filtered = filtered.filter(gpu => filters.architecture.includes(gpu.architecture));
    }



    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return (a.pricing?.price || 0) - (b.pricing?.price || 0);
        case 'performance':
          const aCores = a.specifications.cores.cuda || a.specifications.cores.stream || 0;
          const bCores = b.specifications.cores.cuda || b.specifications.cores.stream || 0;
          return bCores - aCores;
        case 'newest':
          return b.name.localeCompare(a.name); // Mock sorting by name for now
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredGpus(filtered);
  }, [gpus, filters, sortBy, searchQuery]);

  const handleFilterChange = (filterType: keyof Filters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setFilters(initialFilters);
    setSearchQuery('');
  };

  const handleCompareGPU = (gpuId: string) => {
    setSelectedForComparison(prev => {
      if (prev.includes(gpuId)) {
        // Remove from comparison
        return prev.filter(id => id !== gpuId);
      } else if (prev.length < 5) {
        // Add to comparison (max 5 GPUs)
        return [...prev, gpuId];
      } else {
        // Replace the first GPU if at max capacity
        return [...prev.slice(1), gpuId];
      }
    });
  };

  const startComparison = () => {
    if (selectedForComparison.length >= 2) {
      const compareUrl = `/benchmarks?compare=${selectedForComparison.join(',')}&viewMode=compare`;
      window.open(compareUrl, '_blank');
    }
  };

  const clearComparison = () => {
    setSelectedForComparison([]);
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <div className="h-8 bg-background-tertiary rounded w-48 mb-4 animate-pulse" />
            <div className="h-4 bg-background-tertiary rounded w-96 animate-pulse" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-red-500 text-xl mb-4">Error loading GPUs</div>
            <p className="text-foreground-muted mb-6">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Browse GPUs
          </h1>
          <p className="text-foreground-muted">
            Discover and compare graphics cards from top manufacturers
          </p>
        </div>

        {/* Search and Controls */}
        <div className="mb-6 space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search GPUs by name, manufacturer, or architecture..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-background-secondary border border-border rounded-lg px-4 py-3 pl-10 text-foreground placeholder-foreground-muted focus:border-primary focus:outline-none transition-colors duration-200"
            />
            <svg className="absolute left-3 top-3.5 h-4 w-4 text-foreground-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>

          {/* Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                </svg>
                Filters
              </Button>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="bg-background-secondary border border-border rounded-lg px-3 py-2 text-foreground focus:border-primary focus:outline-none"
              >
                <option value="name">Sort by Name</option>
                <option value="price">Sort by Price</option>
                <option value="performance">Sort by Performance</option>
                <option value="newest">Sort by Newest</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-foreground-muted">
                {filteredGpus.length} of {gpus.length} GPUs
              </span>
              
              <div className="flex border border-border rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={cn(
                    'p-2 transition-colors',
                    viewMode === 'grid' ? 'bg-primary text-black' : 'text-foreground-muted hover:text-foreground'
                  )}
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={cn(
                    'p-2 transition-colors',
                    viewMode === 'list' ? 'bg-primary text-black' : 'text-foreground-muted hover:text-foreground'
                  )}
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <FilterPanel
            filters={filters}
            onFilterChange={handleFilterChange}
            onClearFilters={clearFilters}
            gpus={gpus}
          />
        )}

        {/* Comparison Toolbar */}
        {selectedForComparison.length > 0 && (
          <div className="bg-card border border-border rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-foreground font-medium">
                  {selectedForComparison.length} GPU{selectedForComparison.length > 1 ? 's' : ''} selected for comparison
                </span>
                <div className="flex flex-wrap gap-2">
                  {selectedForComparison.map(gpuId => {
                    const gpu = filteredGpus.find(g => g.id === gpuId);
                    return gpu ? (
                      <span key={gpuId} className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                        {gpu.name}
                      </span>
                    ) : null;
                  })}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearComparison}
                >
                  Clear
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={startComparison}
                  disabled={selectedForComparison.length < 2}
                >
                  Compare ({selectedForComparison.length})
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* GPU Grid/List */}
        {filteredGpus.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-foreground-muted text-lg mb-4">No GPUs found</div>
            <p className="text-foreground-muted mb-6">
              Try adjusting your search criteria or filters
            </p>
            <Button onClick={clearFilters} variant="outline">
              Clear Filters
            </Button>
          </div>
        ) : (
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          )}>
            {filteredGpus.map((gpu) => (
              <div key={gpu.id} className="space-y-2">
                {/* Base GPU Card */}
                <div className="relative">
                  <GPUCard
                    gpu={gpu}
                    pricing={gpu.pricing}
                    onSelect={(id) => window.open(`/gpus/${id}`, '_blank')}
                    onCompare={handleCompareGPU}
                    compact={viewMode === 'list'}
                    isSelectedForComparison={selectedForComparison.includes(gpu.id)}
                  />

                  {/* Variants Toggle Button */}
                  {gpu.variantCount > 0 && (
                    <button
                      onClick={() => toggleGpuExpansion(gpu.id)}
                      className="absolute top-4 right-4 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors flex items-center gap-2"
                    >
                      <span>{gpu.variantCount} variants</span>
                      <svg
                        className={`w-4 h-4 transition-transform ${expandedGpus.has(gpu.id) ? 'rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Variants List */}
                {expandedGpus.has(gpu.id) && gpu.variants && gpu.variants.length > 0 && (
                  <div className="ml-6 space-y-2 border-l-2 border-gray-200 pl-4">
                    {gpu.variants.map((variant: any) => (
                      <div key={variant.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{variant.name}</h4>
                            <p className="text-sm text-gray-600">{variant.boardPartner}</p>
                            {variant.sku && (
                              <p className="text-xs text-gray-500">SKU: {variant.sku}</p>
                            )}
                          </div>
                          <div className="text-right">
                            {variant.pricing && (
                              <p className="font-bold text-green-600">${variant.pricing.price}</p>
                            )}
                            <button
                              onClick={() => window.open(`/gpus/${variant.id}`, '_blank')}
                              className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                              View Details
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
}

// Filter Panel Component
interface FilterPanelProps {
  filters: Filters;
  onFilterChange: (filterType: keyof Filters, value: any) => void;
  onClearFilters: () => void;
  gpus: GPU[];
}

function FilterPanel({ filters, onFilterChange, onClearFilters, gpus }: FilterPanelProps) {
  const manufacturers = [...new Set(gpus.map(gpu => gpu.manufacturer))];
  const memorySizes = [...new Set(gpus.map(gpu => gpu.specifications.memory.size.toString()))];
  const architectures = [...new Set(gpus.map(gpu => gpu.architecture))];

  const handleCheckboxChange = (filterType: keyof Filters, value: string, checked: boolean) => {
    const currentValues = filters[filterType] as string[];
    const newValues = checked
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);
    onFilterChange(filterType, newValues);
  };

  return (
    <div className="bg-card border border-border rounded-xl p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-foreground">Filters</h3>
        <Button variant="ghost" size="sm" onClick={onClearFilters}>
          Clear All
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Manufacturer Filter */}
        <div>
          <h4 className="font-medium text-foreground mb-3">Manufacturer</h4>
          <div className="space-y-2">
            {manufacturers.map(manufacturer => (
              <label key={manufacturer} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.manufacturer.includes(manufacturer)}
                  onChange={(e) => handleCheckboxChange('manufacturer', manufacturer, e.target.checked)}
                  className="mr-2 rounded border-border text-primary focus:ring-primary"
                />
                <span className="text-sm text-foreground">{manufacturer}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Memory Size Filter */}
        <div>
          <h4 className="font-medium text-foreground mb-3">Memory Size (GB)</h4>
          <div className="space-y-2">
            {memorySizes.sort((a, b) => parseInt(a) - parseInt(b)).map(size => (
              <label key={size} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.memorySize.includes(size)}
                  onChange={(e) => handleCheckboxChange('memorySize', size, e.target.checked)}
                  className="mr-2 rounded border-border text-primary focus:ring-primary"
                />
                <span className="text-sm text-foreground">{size}GB</span>
              </label>
            ))}
          </div>
        </div>

        {/* Architecture Filter */}
        <div>
          <h4 className="font-medium text-foreground mb-3">Architecture</h4>
          <div className="space-y-2">
            {architectures.map(architecture => (
              <label key={architecture} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.architecture.includes(architecture)}
                  onChange={(e) => handleCheckboxChange('architecture', architecture, e.target.checked)}
                  className="mr-2 rounded border-border text-primary focus:ring-primary"
                />
                <span className="text-sm text-foreground">{architecture}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Price Range Filter */}
        <div>
          <h4 className="font-medium text-foreground mb-3">Price Range</h4>
          <div className="space-y-3">
            <div>
              <label className="block text-xs text-foreground-muted mb-1">Min Price</label>
              <input
                type="number"
                value={filters.priceRange.min}
                onChange={(e) => onFilterChange('priceRange', { ...filters.priceRange, min: parseInt(e.target.value) || 0 })}
                className="w-full bg-background-secondary border border-border rounded px-3 py-2 text-sm text-foreground focus:border-primary focus:outline-none"
                placeholder="0"
              />
            </div>
            <div>
              <label className="block text-xs text-foreground-muted mb-1">Max Price</label>
              <input
                type="number"
                value={filters.priceRange.max}
                onChange={(e) => onFilterChange('priceRange', { ...filters.priceRange, max: parseInt(e.target.value) || 5000 })}
                className="w-full bg-background-secondary border border-border rounded px-3 py-2 text-sm text-foreground focus:border-primary focus:outline-none"
                placeholder="5000"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}