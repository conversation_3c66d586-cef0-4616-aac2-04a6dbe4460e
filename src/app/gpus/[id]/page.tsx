'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Loading } from '@/components/ui/Loading';
import { PriceChart } from '@/components/ui/PriceChart';
import { cn, formatPrice, formatDate } from '@/lib/utils';

interface GPUDetail {
  id: string;
  name: string;
  manufacturer: string;
  architecture: string;
  launchDate: string;
  originalMsrp: number;
  specifications: {
    memory: {
      size: number;
      type: string;
      bandwidth: number;
      busWidth: number;
    };
    cores: {
      cuda?: number;
      stream?: number;
      rt?: number;
      tensor?: number;
    };
    clocks: {
      base: number;
      boost: number;
      memory: number;
    };
    power: {
      tdp: number;
      recommendedPSU: number;
      connectors: string[];
    };
    physical: {
      length: number;
      width: number;
      height: number;
      slots: number;
    };
    features: {
      rayTracing: boolean;
      dlss: boolean;
      av1Encode: boolean;
      nvenc: boolean;
    };
  };
  pricing: Array<{
    retailer: string;
    price: number;
    url: string;
    lastUpdated: string;
  }>;
  priceHistory: Array<{
    date: string;
    price: number;
    retailer: string;
  }>;
  reviews: Array<{
    id: string;
    rating: number;
    title: string;
    content: string;
    author: string;
    date: string;
  }>;
}

export default function GPUDetailPage() {
  const params = useParams();
  const gpuId = params.id as string;
  
  const [gpu, setGpu] = useState<GPUDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'specs' | 'pricing' | 'reviews'>('overview');

  // Mock data for development
  useEffect(() => {
    const mockGpu: GPUDetail = {
      id: gpuId,
      name: 'GeForce RTX 4090',
      manufacturer: 'NVIDIA',
      architecture: 'Ada Lovelace',
      launchDate: '2022-10-12',
      originalMsrp: 1599,
      specifications: {
        memory: {
          size: 24,
          type: 'GDDR6X',
          bandwidth: 1008,
          busWidth: 384
        },
        cores: {
          cuda: 16384,
          rt: 128,
          tensor: 512
        },
        clocks: {
          base: 2230,
          boost: 2520,
          memory: 21000
        },
        power: {
          tdp: 450,
          recommendedPSU: 850,
          connectors: ['16-pin PCIe 5.0']
        },
        physical: {
          length: 304,
          width: 137,
          height: 61,
          slots: 3
        },
        features: {
          rayTracing: true,
          dlss: true,
          av1Encode: true,
          nvenc: true
        }
      },
      pricing: [
        {
          retailer: 'Newegg',
          price: 1599,
          availability: 'in-stock',
          url: 'https://newegg.com',
          lastUpdated: '2024-01-15T10:30:00Z'
        },
        {
          retailer: 'Amazon',
          price: 1649,
          availability: 'limited',
          url: 'https://amazon.com',
          lastUpdated: '2024-01-15T09:15:00Z'
        },
        {
          retailer: 'Best Buy',
          price: 1599,
          availability: 'out-of-stock',
          url: 'https://bestbuy.com',
          lastUpdated: '2024-01-15T08:45:00Z'
        }
      ],
      priceHistory: [
        { date: '2024-01-01', price: 1699, retailer: 'Average' },
        { date: '2024-01-08', price: 1649, retailer: 'Average' },
        { date: '2024-01-15', price: 1599, retailer: 'Average' }
      ],
      reviews: [
        {
          id: '1',
          rating: 5,
          title: 'Absolute Beast for 4K Gaming',
          content: 'This GPU handles everything I throw at it. 4K gaming with ray tracing is smooth as butter.',
          author: 'TechEnthusiast',
          date: '2024-01-10'
        },
        {
          id: '2',
          rating: 4,
          title: 'Great Performance, High Power Draw',
          content: 'Amazing performance but make sure you have a good PSU. Runs hot under load.',
          author: 'GamerPro',
          date: '2024-01-08'
        }
      ]
    };

    // Simulate API call
    setTimeout(() => {
      setGpu(mockGpu);
      setLoading(false);
    }, 1000);
  }, [gpuId]);

  if (loading) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Loading size="lg" text="Loading GPU details..." />
        </div>
      </Layout>
    );
  }

  if (error || !gpu) {
    return (
      <Layout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-red-500 text-xl mb-4">GPU not found</div>
            <p className="text-foreground-muted mb-6">
              The GPU you're looking for doesn't exist or has been removed.
            </p>
            <Button onClick={() => window.history.back()}>
              Go Back
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  const bestPrice = gpu.pricing.reduce((min, current) => 
    current.price < min.price ? current : min
  );



  const manufacturerColors = {
    'NVIDIA': 'text-green-400',
    'AMD': 'text-red-400',
    'INTEL': 'text-blue-400'
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-2">
            <span className={cn('text-sm font-medium', manufacturerColors[gpu.manufacturer as keyof typeof manufacturerColors])}>
              {gpu.manufacturer}
            </span>
            <span className="text-xs bg-background-tertiary px-2 py-1 rounded text-foreground-muted">
              {gpu.architecture}
            </span>
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-4">
            {gpu.name}
          </h1>
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="text-2xl font-bold text-primary">
                {formatPrice(bestPrice.price)}
              </div>
              <div className="text-sm text-foreground-muted">
                Best price at {bestPrice.retailer}
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="lg">
                Add to Wishlist
              </Button>
              <Button variant="outline" size="lg">
                Compare
              </Button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-border mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'specs', label: 'Specifications' },
              { id: 'pricing', label: 'Pricing' },
              { id: 'reviews', label: 'Reviews' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-foreground-muted hover:text-foreground hover:border-border'
                )}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-8">
          {activeTab === 'overview' && (
            <OverviewTab gpu={gpu} />
          )}
          
          {activeTab === 'specs' && (
            <SpecificationsTab gpu={gpu} />
          )}
          
          {activeTab === 'pricing' && (
            <PricingTab gpu={gpu} />
          )}
          
          {activeTab === 'reviews' && (
            <ReviewsTab gpu={gpu} />
          )}
        </div>
      </div>
    </Layout>
  );
}

// Overview Tab Component
function OverviewTab({ gpu }: { gpu: GPUDetail }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Key Specs */}
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-xl font-semibold text-foreground mb-4">Key Specifications</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-background-secondary rounded-lg">
              <div className="text-2xl font-bold text-primary mb-1">
                {gpu.specifications.memory.size}GB
              </div>
              <div className="text-xs text-foreground-muted">VRAM</div>
            </div>
            <div className="text-center p-4 bg-background-secondary rounded-lg">
              <div className="text-2xl font-bold text-primary mb-1">
                {gpu.specifications.cores.cuda?.toLocaleString() || gpu.specifications.cores.stream?.toLocaleString()}
              </div>
              <div className="text-xs text-foreground-muted">Cores</div>
            </div>
            <div className="text-center p-4 bg-background-secondary rounded-lg">
              <div className="text-2xl font-bold text-primary mb-1">
                {gpu.specifications.clocks.boost}
              </div>
              <div className="text-xs text-foreground-muted">Boost MHz</div>
            </div>
            <div className="text-center p-4 bg-background-secondary rounded-lg">
              <div className="text-2xl font-bold text-primary mb-1">
                {gpu.specifications.power.tdp}W
              </div>
              <div className="text-xs text-foreground-muted">TDP</div>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-xl font-semibold text-foreground mb-4">Features</h3>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(gpu.specifications.features).map(([feature, supported]) => (
              <div key={feature} className="flex items-center justify-between p-3 bg-background-secondary rounded-lg">
                <span className="text-foreground capitalize">
                  {feature.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <span className={cn(
                  'text-sm font-medium',
                  supported ? 'text-green-400' : 'text-red-400'
                )}>
                  {supported ? '✓ Yes' : '✗ No'}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Info */}
      <div className="space-y-6">
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Quick Info</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-foreground-muted">Launch Date</span>
              <span className="text-foreground">{formatDate(gpu.launchDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-foreground-muted">Original MSRP</span>
              <span className="text-foreground">{formatPrice(gpu.originalMsrp)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-foreground-muted">Architecture</span>
              <span className="text-foreground">{gpu.architecture}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-foreground-muted">Memory Type</span>
              <span className="text-foreground">{gpu.specifications.memory.type}</span>
            </div>
          </div>
        </div>

        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Physical Dimensions</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-foreground-muted">Length</span>
              <span className="text-foreground">{gpu.specifications.physical.length}mm</span>
            </div>
            <div className="flex justify-between">
              <span className="text-foreground-muted">Width</span>
              <span className="text-foreground">{gpu.specifications.physical.width}mm</span>
            </div>
            <div className="flex justify-between">
              <span className="text-foreground-muted">Height</span>
              <span className="text-foreground">{gpu.specifications.physical.height}mm</span>
            </div>
            <div className="flex justify-between">
              <span className="text-foreground-muted">Slots</span>
              <span className="text-foreground">{gpu.specifications.physical.slots}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Specifications Tab Component
function SpecificationsTab({ gpu }: { gpu: GPUDetail }) {
  const specs = [
    {
      category: 'Memory',
      items: [
        { label: 'Memory Size', value: `${gpu.specifications.memory.size}GB` },
        { label: 'Memory Type', value: gpu.specifications.memory.type },
        { label: 'Memory Bandwidth', value: `${gpu.specifications.memory.bandwidth} GB/s` },
        { label: 'Bus Width', value: `${gpu.specifications.memory.busWidth}-bit` }
      ]
    },
    {
      category: 'Processing Cores',
      items: [
        ...(gpu.specifications.cores.cuda ? [{ label: 'CUDA Cores', value: gpu.specifications.cores.cuda.toLocaleString() }] : []),
        ...(gpu.specifications.cores.stream ? [{ label: 'Stream Processors', value: gpu.specifications.cores.stream.toLocaleString() }] : []),
        ...(gpu.specifications.cores.rt ? [{ label: 'RT Cores', value: gpu.specifications.cores.rt.toString() }] : []),
        ...(gpu.specifications.cores.tensor ? [{ label: 'Tensor Cores', value: gpu.specifications.cores.tensor.toString() }] : [])
      ]
    },
    {
      category: 'Clock Speeds',
      items: [
        { label: 'Base Clock', value: `${gpu.specifications.clocks.base} MHz` },
        { label: 'Boost Clock', value: `${gpu.specifications.clocks.boost} MHz` },
        { label: 'Memory Clock', value: `${gpu.specifications.clocks.memory} MHz` }
      ]
    },
    {
      category: 'Power',
      items: [
        { label: 'Total Graphics Power (TGP)', value: `${gpu.specifications.power.tdp}W` },
        { label: 'Recommended PSU', value: `${gpu.specifications.power.recommendedPSU}W` },
        { label: 'Power Connectors', value: gpu.specifications.power.connectors.join(', ') }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {specs.map((section) => (
        <div key={section.category} className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-xl font-semibold text-foreground mb-4">{section.category}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {section.items.map((item) => (
              <div key={item.label} className="flex justify-between items-center p-3 bg-background-secondary rounded-lg">
                <span className="text-foreground-muted">{item.label}</span>
                <span className="text-foreground font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

// Pricing Tab Component
function PricingTab({ gpu }: { gpu: GPUDetail }) {
  return (
    <div className="space-y-6">
      {/* Current Pricing */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-xl font-semibold text-foreground mb-4">Price Comparison</h3>
        <div className="space-y-4">
          {gpu.pricing.map((price, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-background-secondary rounded-lg">
              <div className="flex items-center gap-4">
                <div className="font-medium text-foreground">{price.retailer}</div>
                <div className="text-sm text-foreground-muted">
                  Updated {new Date(price.lastUpdated).toLocaleDateString()}
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-xl font-bold text-primary">
                  {formatPrice(price.price)}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(price.url, '_blank')}
                >
                  View at {price.retailer}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Price History Chart */}
      <PriceChart data={gpu.priceHistory} height={300} />
    </div>
  );
}

// Reviews Tab Component
function ReviewsTab({ gpu }: { gpu: GPUDetail }) {
  const averageRating = gpu.reviews.reduce((sum, review) => sum + review.rating, 0) / gpu.reviews.length;

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-xl font-semibold text-foreground mb-4">Review Summary</h3>
        <div className="flex items-center gap-4">
          <div className="text-3xl font-bold text-primary">
            {averageRating.toFixed(1)}
          </div>
          <div>
            <div className="flex items-center gap-1 mb-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <svg
                  key={i}
                  className={cn('w-5 h-5', i < Math.floor(averageRating) ? 'text-primary' : 'text-border')}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <div className="text-sm text-foreground-muted">
              Based on {gpu.reviews.length} reviews
            </div>
          </div>
        </div>
      </div>

      {/* Individual Reviews */}
      <div className="space-y-4">
        {gpu.reviews.map((review) => (
          <div key={review.id} className="bg-card border border-border rounded-xl p-6">
            <div className="flex items-start justify-between mb-3">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="font-medium text-foreground">{review.author}</div>
                  <div className="flex items-center gap-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <svg
                        key={i}
                        className={cn('w-4 h-4', i < review.rating ? 'text-primary' : 'text-border')}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <div className="text-sm text-foreground-muted">{formatDate(review.date)}</div>
              </div>
            </div>
            <h4 className="font-semibold text-foreground mb-2">{review.title}</h4>
            <p className="text-foreground-muted">{review.content}</p>
          </div>
        ))}
      </div>
    </div>
  );
}