import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/benchmarks - Get benchmark data with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gpuId = searchParams.get('gpuId')
    const benchmarkType = searchParams.get('type') // gaming, professional, synthetic
    const testName = searchParams.get('test')
    const resolution = searchParams.get('resolution')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')

    const skip = (page - 1) * limit

    const where: any = {}

    if (gpuId) {
      where.gpuId = gpuId
    }

    if (benchmarkType) {
      where.benchmarkType = benchmarkType
    }

    if (testName) {
      where.testName = { contains: testName, mode: 'insensitive' }
    }

    if (resolution) {
      where.resolution = resolution
    }

    const [benchmarks, total] = await Promise.all([
      prisma.performanceBenchmark.findMany({
        where,
        orderBy: [
          { testDate: 'desc' },
          { fps: 'desc' }
        ],
        skip,
        take: limit,
        include: {
          gpu: {
            select: {
              id: true,
              name: true,
              manufacturer: true,
              architecture: true
            }
          }
        }
      }),
      prisma.performanceBenchmark.count({ where })
    ])

    return NextResponse.json({
      benchmarks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching benchmarks:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benchmarks' },
      { status: 500 }
    )
  }
}

// POST /api/benchmarks - Create new benchmark data (admin/trusted users only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // TODO: Add role-based access control for benchmark submission
    // For now, any authenticated user can submit benchmarks

    const body = await request.json()
    const {
      gpuId,
      benchmarkType,
      testName,
      resolution,
      settings,
      fps,
      score,
      source,
      benchmarkData
    } = body

    if (!gpuId || !benchmarkType || !testName || !resolution) {
      return NextResponse.json(
        { error: 'GPU ID, benchmark type, test name, and resolution are required' },
        { status: 400 }
      )
    }

    // Verify GPU exists
    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId }
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    const benchmark = await prisma.performanceBenchmark.create({
      data: {
        gpuId,
        benchmarkType,
        testName,
        resolution,
        settings: settings || 'Unknown',
        fps: fps ? parseFloat(fps) : null,
        score: score ? parseInt(score) : null,
        testDate: new Date(),
        source: source || 'User Submitted',
        benchmarkData: benchmarkData || null
      },
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    return NextResponse.json(benchmark, { status: 201 })
  } catch (error) {
    console.error('Error creating benchmark:', error)
    return NextResponse.json(
      { error: 'Failed to create benchmark' },
      { status: 500 }
    )
  }
}