import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/benchmarks/compare - Compare performance between multiple GPUs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gpuIds = searchParams.get('gpuIds')?.split(',') || []
    const benchmarkType = searchParams.get('type') || 'gaming'
    const resolution = searchParams.get('resolution') || '1080p'
    const testName = searchParams.get('test')

    if (gpuIds.length === 0) {
      return NextResponse.json(
        { error: 'At least one GPU ID is required' },
        { status: 400 }
      )
    }

    if (gpuIds.length > 10) {
      return NextResponse.json(
        { error: 'Maximum 10 GPUs can be compared at once' },
        { status: 400 }
      )
    }

    const where: any = {
      gpuId: { in: gpuIds },
      benchmarkType,
      resolution
    }

    if (testName) {
      where.testName = { contains: testName, mode: 'insensitive' }
    }

    // Get benchmark data for comparison
    const benchmarks = await prisma.performanceBenchmark.findMany({
      where,
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true,
            originalMsrp: true
          }
        }
      },
      orderBy: [
        { testName: 'asc' },
        { fps: 'desc' }
      ]
    })

    // Get GPU pricing for performance per dollar calculations
    const gpuPricing = await prisma.gPUPricing.findMany({
      where: {
        gpuId: { in: gpuIds }
      },
      orderBy: { scrapedAt: 'desc' },
      distinct: ['gpuId'],
      take: gpuIds.length
    })

    // Group benchmarks by test name and GPU
    const groupedBenchmarks: Record<string, Record<string, any[]>> = {}
    const gpuInfo: Record<string, any> = {}

    benchmarks.forEach(benchmark => {
      const testName = benchmark.testName
      const gpuId = benchmark.gpuId

      if (!groupedBenchmarks[testName]) {
        groupedBenchmarks[testName] = {}
      }

      if (!groupedBenchmarks[testName][gpuId]) {
        groupedBenchmarks[testName][gpuId] = []
      }

      groupedBenchmarks[testName][gpuId].push(benchmark)

      // Store GPU info
      if (!gpuInfo[gpuId]) {
        gpuInfo[gpuId] = {
          ...benchmark.gpu,
          currentPrice: null,
          performancePerDollar: null
        }
      }
    })

    // Add pricing information
    gpuPricing.forEach(pricing => {
      if (gpuInfo[pricing.gpuId]) {
        gpuInfo[pricing.gpuId].currentPrice = parseFloat(pricing.price.toString())
      }
    })

    // Calculate average performance and performance per dollar
    const comparisonResults: any[] = []

    Object.keys(gpuInfo).forEach(gpuId => {
      const gpu = gpuInfo[gpuId]
      const gpuBenchmarks = benchmarks.filter(b => b.gpuId === gpuId)
      
      if (gpuBenchmarks.length > 0) {
        const avgFps = gpuBenchmarks
          .filter(b => b.fps !== null)
          .reduce((sum, b) => sum + parseFloat(b.fps!.toString()), 0) / 
          gpuBenchmarks.filter(b => b.fps !== null).length

        const avgScore = gpuBenchmarks
          .filter(b => b.score !== null)
          .reduce((sum, b) => sum + b.score!, 0) / 
          gpuBenchmarks.filter(b => b.score !== null).length

        let performancePerDollar = null
        if (gpu.currentPrice && avgFps) {
          performancePerDollar = avgFps / gpu.currentPrice
        }

        comparisonResults.push({
          gpu: {
            id: gpu.id,
            name: gpu.name,
            manufacturer: gpu.manufacturer,
            architecture: gpu.architecture,
            originalMsrp: gpu.originalMsrp,
            currentPrice: gpu.currentPrice
          },
          performance: {
            averageFps: avgFps || null,
            averageScore: avgScore || null,
            performancePerDollar,
            benchmarkCount: gpuBenchmarks.length
          },
          benchmarks: gpuBenchmarks.map(b => ({
            testName: b.testName,
            fps: b.fps,
            score: b.score,
            settings: b.settings,
            source: b.source,
            testDate: b.testDate
          }))
        })
      }
    })

    // Sort by average FPS descending
    comparisonResults.sort((a, b) => {
      const aFps = a.performance.averageFps || 0
      const bFps = b.performance.averageFps || 0
      return bFps - aFps
    })

    return NextResponse.json({
      comparison: comparisonResults,
      metadata: {
        benchmarkType,
        resolution,
        testName,
        gpuCount: gpuIds.length,
        totalBenchmarks: benchmarks.length
      }
    })
  } catch (error) {
    console.error('Error comparing benchmarks:', error)
    return NextResponse.json(
      { error: 'Failed to compare benchmarks' },
      { status: 500 }
    )
  }
}