import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/user/wishlist - Get user's wishlist
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const wishlistItems = await prisma.userWishlist.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true,
            specifications: true,
            physicalSpecs: true,
            powerRequirements: true,
            originalMsrp: true,
            pricing: {
              take: 1,
              orderBy: {
                scrapedAt: 'desc'
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({ wishlist: wishlistItems })
  } catch (error) {
    console.error('Error fetching wishlist:', error)
    return NextResponse.json(
      { error: 'Failed to fetch wishlist' },
      { status: 500 }
    )
  }
}

// POST /api/user/wishlist - Add GPU to wishlist
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { gpuId, targetPrice, notes } = await request.json()

    if (!gpuId) {
      return NextResponse.json(
        { error: 'GPU ID is required' },
        { status: 400 }
      )
    }

    // Check if GPU exists
    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId }
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    // Check if already in wishlist
    const existingItem = await prisma.userWishlist.findUnique({
      where: {
        userId_gpuId: {
          userId: session.user.id,
          gpuId: gpuId
        }
      }
    })

    if (existingItem) {
      return NextResponse.json(
        { error: 'GPU is already in your wishlist' },
        { status: 400 }
      )
    }

    // Add to wishlist
    const wishlistItem = await prisma.userWishlist.create({
      data: {
        userId: session.user.id,
        gpuId: gpuId,
        targetPrice: targetPrice ? parseFloat(targetPrice) : null,
        notes: notes || null
      },
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    return NextResponse.json(
      { 
        message: 'GPU added to wishlist',
        wishlistItem 
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error adding to wishlist:', error)
    return NextResponse.json(
      { error: 'Failed to add GPU to wishlist' },
      { status: 500 }
    )
  }
}