import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// DELETE /api/user/wishlist/[id] - Remove GPU from wishlist
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const wishlistItemId = params.id

    // Find the wishlist item and verify ownership
    const wishlistItem = await prisma.userWishlist.findUnique({
      where: {
        id: wishlistItemId
      }
    })

    if (!wishlistItem) {
      return NextResponse.json(
        { error: 'Wishlist item not found' },
        { status: 404 }
      )
    }

    if (wishlistItem.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to delete this item' },
        { status: 403 }
      )
    }

    // Delete the wishlist item
    await prisma.userWishlist.delete({
      where: {
        id: wishlistItemId
      }
    })

    return NextResponse.json(
      { message: 'GPU removed from wishlist' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error removing from wishlist:', error)
    return NextResponse.json(
      { error: 'Failed to remove GPU from wishlist' },
      { status: 500 }
    )
  }
}

// PUT /api/user/wishlist/[id] - Update wishlist item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const wishlistItemId = params.id
    const { targetPrice, notes } = await request.json()

    // Find the wishlist item and verify ownership
    const wishlistItem = await prisma.userWishlist.findUnique({
      where: {
        id: wishlistItemId
      }
    })

    if (!wishlistItem) {
      return NextResponse.json(
        { error: 'Wishlist item not found' },
        { status: 404 }
      )
    }

    if (wishlistItem.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to update this item' },
        { status: 403 }
      )
    }

    // Update the wishlist item
    const updatedItem = await prisma.userWishlist.update({
      where: {
        id: wishlistItemId
      },
      data: {
        targetPrice: targetPrice ? parseFloat(targetPrice) : null,
        notes: notes || null
      },
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    return NextResponse.json(
      { 
        message: 'Wishlist item updated',
        wishlistItem: updatedItem 
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error updating wishlist item:', error)
    return NextResponse.json(
      { error: 'Failed to update wishlist item' },
      { status: 500 }
    )
  }
}