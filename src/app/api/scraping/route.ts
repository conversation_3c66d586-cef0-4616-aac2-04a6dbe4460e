import { NextRequest, NextResponse } from 'next/server';
import { webScraper } from '@/lib/web-scraper';
import { pricingService } from '@/lib/pricing-service';
import { priceValidator } from '@/lib/price-validator';
import { backgroundJobProcessor } from '@/lib/background-jobs';
import { prisma } from '@/lib/prisma';

// GET /api/scraping - Get scraping status and statistics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'status':
        return await getScrapingStatus();
      
      case 'anomalies':
        return await getPriceAnomalies();
      
      case 'statistics':
        const gpuId = searchParams.get('gpuId');
        const days = parseInt(searchParams.get('days') || '30');
        return await getPriceStatistics(gpuId, days);
      
      default:
        return NextResponse.json({
          message: 'Available actions: status, anomalies, statistics',
          endpoints: {
            status: '/api/scraping?action=status',
            anomalies: '/api/scraping?action=anomalies',
            statistics: '/api/scraping?action=statistics&gpuId=<id>&days=<days>'
          }
        });
    }
  } catch (error) {
    console.error('Error in scraping GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/scraping - Trigger scraping operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, gpuId, gpuIds, gpuName } = body;

    switch (action) {
      case 'scrape-single':
        if (!gpuName) {
          return NextResponse.json(
            { error: 'GPU name is required for single scraping' },
            { status: 400 }
          );
        }
        return await scrapeSingleGPU(gpuName, gpuId);
      
      case 'scrape-batch':
        if (!gpuIds || !Array.isArray(gpuIds)) {
          return NextResponse.json(
            { error: 'GPU IDs array is required for batch scraping' },
            { status: 400 }
          );
        }
        return await scrapeBatchGPUs(gpuIds);
      
      case 'validate-prices':
        return await validateAllPrices();
      
      case 'cleanup-stale':
        return await cleanupStaleData();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Available: scrape-single, scrape-batch, validate-prices, cleanup-stale' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in scraping POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getScrapingStatus() {
  try {
    // Get recent scraping activity
    const recentPrices = await prisma.gPUPricing.findMany({
      where: {
        scrapedAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      select: {
        retailer: true,
        scrapedAt: true
      },
      orderBy: { scrapedAt: 'desc' },
      take: 100
    });

    // Group by retailer
    const retailerStats = recentPrices.reduce((acc, price) => {
      if (!acc[price.retailer]) {
        acc[price.retailer] = { count: 0, lastScrape: price.scrapedAt };
      }
      acc[price.retailer].count++;
      if (price.scrapedAt > acc[price.retailer].lastScrape) {
        acc[price.retailer].lastScrape = price.scrapedAt;
      }
      return acc;
    }, {} as Record<string, { count: number; lastScrape: Date }>);

    // Get total counts
    const totalPrices = await prisma.gPUPricing.count();
    const totalGPUs = await prisma.gPU.count();

    return NextResponse.json({
      status: 'active',
      statistics: {
        totalPrices,
        totalGPUs,
        pricesLast24h: recentPrices.length,
        retailerStats
      },
      lastUpdate: recentPrices[0]?.scrapedAt || null
    });
  } catch (error) {
    console.error('Error getting scraping status:', error);
    return NextResponse.json(
      { error: 'Failed to get scraping status' },
      { status: 500 }
    );
  }
}

async function getPriceAnomalies() {
  try {
    const anomalies = await priceValidator.detectPriceAnomalies();
    
    return NextResponse.json({
      anomalies,
      summary: {
        total: anomalies.length,
        high: anomalies.filter(a => a.severity === 'high').length,
        medium: anomalies.filter(a => a.severity === 'medium').length,
        low: anomalies.filter(a => a.severity === 'low').length
      }
    });
  } catch (error) {
    console.error('Error getting price anomalies:', error);
    return NextResponse.json(
      { error: 'Failed to get price anomalies' },
      { status: 500 }
    );
  }
}

async function getPriceStatistics(gpuId: string | null, days: number) {
  try {
    if (!gpuId) {
      return NextResponse.json(
        { error: 'GPU ID is required for statistics' },
        { status: 400 }
      );
    }

    const stats = await priceValidator.getPriceStatistics(gpuId, days);
    
    if (!stats) {
      return NextResponse.json(
        { error: 'No price data found for this GPU' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      gpuId,
      period: `${days} days`,
      statistics: stats
    });
  } catch (error) {
    console.error('Error getting price statistics:', error);
    return NextResponse.json(
      { error: 'Failed to get price statistics' },
      { status: 500 }
    );
  }
}

async function scrapeSingleGPU(gpuName: string, gpuId?: string) {
  try {
    console.log(`Starting manual scrape for GPU: ${gpuName}`);
    
    const scrapedPrices = await webScraper.scrapeGPUPrices(gpuName);
    
    if (gpuId && scrapedPrices.length > 0) {
      // Convert to pricing service format
      const formattedPrices = scrapedPrices.map(price => ({
        retailer: price.retailer,
        price: price.price,
        url: price.url,
        condition: price.condition
      }));
      
      await pricingService.addPriceData(gpuId, formattedPrices);
    }
    
    return NextResponse.json({
      message: `Scraped ${scrapedPrices.length} prices for ${gpuName}`,
      prices: scrapedPrices,
      saved: gpuId ? true : false
    });
  } catch (error) {
    console.error('Error scraping single GPU:', error);
    return NextResponse.json(
      { error: 'Failed to scrape GPU prices' },
      { status: 500 }
    );
  }
}

async function scrapeBatchGPUs(gpuIds: string[]) {
  try {
    const jobId = await backgroundJobProcessor.addPriceScrapingJob(gpuIds);
    
    return NextResponse.json({
      message: `Batch scraping job queued for ${gpuIds.length} GPUs`,
      jobId,
      status: 'queued'
    }, { status: 202 });
  } catch (error) {
    console.error('Error starting batch scraping:', error);
    return NextResponse.json(
      { error: 'Failed to start batch scraping' },
      { status: 500 }
    );
  }
}

async function validateAllPrices() {
  try {
    const anomalies = await priceValidator.detectPriceAnomalies();
    
    return NextResponse.json({
      message: 'Price validation completed',
      anomaliesFound: anomalies.length,
      anomalies: anomalies.slice(0, 10) // Return top 10 anomalies
    });
  } catch (error) {
    console.error('Error validating prices:', error);
    return NextResponse.json(
      { error: 'Failed to validate prices' },
      { status: 500 }
    );
  }
}

async function cleanupStaleData() {
  try {
    const deletedCount = await priceValidator.cleanupStaleData();
    
    return NextResponse.json({
      message: `Cleaned up ${deletedCount} stale price records`,
      deletedCount
    });
  } catch (error) {
    console.error('Error cleaning up stale data:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup stale data' },
      { status: 500 }
    );
  }
}
