import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/overclocking - Get overclocking data with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gpuId = searchParams.get('gpuId')
    const coolingType = searchParams.get('coolingType')
    const minStability = searchParams.get('minStability')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const skip = (page - 1) * limit

    const where: any = {}

    if (gpuId) {
      where.gpuId = gpuId
    }

    if (coolingType) {
      where.coolingType = coolingType
    }

    if (minStability) {
      where.stabilityRating = { gte: parseInt(minStability) }
    }

    const [overclockingData, total] = await Promise.all([
      prisma.overclockingData.findMany({
        where,
        orderBy: [
          { stabilityRating: 'desc' },
          { performanceGain: 'desc' }
        ],
        skip,
        take: limit,
        include: {
          gpu: {
            select: {
              id: true,
              name: true,
              manufacturer: true,
              architecture: true
            }
          }
        }
      }),
      prisma.overclockingData.count({ where })
    ])

    return NextResponse.json({
      overclockingData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching overclocking data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch overclocking data' },
      { status: 500 }
    )
  }
}

// POST /api/overclocking - Create new overclocking data (authenticated users)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      gpuId,
      coreClockOffset,
      memoryClockOffset,
      powerLimit,
      voltageOffset,
      performanceGain,
      stabilityRating,
      stockTemperature,
      ocTemperature,
      maxTemperature,
      thermalThrottling,
      stockPower,
      ocPower,
      powerEfficiency,
      coolingType,
      ambientTemp,
      testDuration,
      notes
    } = body

    if (!gpuId) {
      return NextResponse.json(
        { error: 'GPU ID is required' },
        { status: 400 }
      )
    }

    // Verify GPU exists
    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId }
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    const overclockingData = await prisma.overclockingData.create({
      data: {
        gpuId,
        coreClockOffset: coreClockOffset ? parseInt(coreClockOffset) : null,
        memoryClockOffset: memoryClockOffset ? parseInt(memoryClockOffset) : null,
        powerLimit: powerLimit ? parseInt(powerLimit) : null,
        voltageOffset: voltageOffset ? parseInt(voltageOffset) : null,
        performanceGain: performanceGain ? parseFloat(performanceGain) : null,
        stabilityRating: stabilityRating ? parseInt(stabilityRating) : null,
        stockTemperature: stockTemperature ? parseInt(stockTemperature) : null,
        ocTemperature: ocTemperature ? parseInt(ocTemperature) : null,
        maxTemperature: maxTemperature ? parseInt(maxTemperature) : null,
        thermalThrottling: thermalThrottling === 'true' || thermalThrottling === true,
        stockPower: stockPower ? parseInt(stockPower) : null,
        ocPower: ocPower ? parseInt(ocPower) : null,
        powerEfficiency: powerEfficiency ? parseFloat(powerEfficiency) : null,
        coolingType: coolingType || null,
        ambientTemp: ambientTemp ? parseInt(ambientTemp) : null,
        testDuration: testDuration ? parseInt(testDuration) : null,
        source: 'User Submitted',
        notes: notes || null
      },
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    return NextResponse.json(overclockingData, { status: 201 })
  } catch (error) {
    console.error('Error creating overclocking data:', error)
    return NextResponse.json(
      { error: 'Failed to create overclocking data' },
      { status: 500 }
    )
  }
}