import { NextRequest, NextResponse } from 'next/server'
import { DataValidationService } from '@/lib/data-validation'

// GET /api/admin/data-validation - Get data quality report
export async function GET(request: NextRequest) {
  try {
    // In production, you'd want to add admin authentication here
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Admin endpoints not available in production' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'validate':
        const validation = await DataValidationService.validateAllGPUs()
        return NextResponse.json(validation)

      case 'report':
        const report = await DataValidationService.generateDataQualityReport()
        return NextResponse.json(report)

      case 'duplicates':
        const duplicates = await DataValidationService.findDuplicateGPUs()
        return NextResponse.json({ duplicates })

      default:
        const defaultReport = await DataValidationService.generateDataQualityReport()
        return NextResponse.json(defaultReport)
    }
  } catch (error) {
    console.error('Error in data validation:', error)
    return NextResponse.json(
      { error: 'Failed to validate data' },
      { status: 500 }
    )
  }
}

// POST /api/admin/data-validation - Perform data fixes
export async function POST(request: NextRequest) {
  try {
    // In production, you'd want to add admin authentication here
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Admin endpoints not available in production' },
        { status: 403 }
      )
    }

    const { action } = await request.json()

    switch (action) {
      case 'autofix':
        const fixResult = await DataValidationService.autoFixDataIssues()
        return NextResponse.json({
          message: `Auto-fixed ${fixResult.fixed} issues`,
          ...fixResult
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in data validation action:', error)
    return NextResponse.json(
      { error: 'Failed to perform data validation action' },
      { status: 500 }
    )
  }
}