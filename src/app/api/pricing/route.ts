import { NextRequest, NextResponse } from 'next/server'
import { pricingService } from '@/lib/pricing-service'
import { backgroundJobProcessor } from '@/lib/background-jobs'

// GET /api/pricing - Get pricing data and market trends
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gpuId = searchParams.get('gpuId')
    const action = searchParams.get('action') // 'current', 'history', 'trend', 'market'
    const days = parseInt(searchParams.get('days') || '30')

    if (!action) {
      return NextResponse.json(
        { error: 'Action parameter is required (current, history, trend, market)' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'current':
        if (!gpuId) {
          return NextResponse.json(
            { error: 'GPU ID is required for current prices' },
            { status: 400 }
          )
        }
        const currentPrices = await pricingService.getCurrentPrices(gpuId)
        return NextResponse.json({ prices: currentPrices })

      case 'history':
        if (!gpuId) {
          return NextResponse.json(
            { error: 'GPU ID is required for price history' },
            { status: 400 }
          )
        }
        const priceHistory = await pricingService.getPriceHistory(gpuId, days)
        return NextResponse.json({ history: priceHistory })

      case 'trend':
        if (!gpuId) {
          return NextResponse.json(
            { error: 'GPU ID is required for price trend' },
            { status: 400 }
          )
        }
        const priceTrend = await pricingService.getPriceTrend(gpuId)
        return NextResponse.json({ trend: priceTrend })

      case 'market':
        const marketTrends = await pricingService.getMarketTrends()
        return NextResponse.json({ market: marketTrends })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: current, history, trend, or market' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in pricing API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch pricing data' },
      { status: 500 }
    )
  }
}

// POST /api/pricing - Trigger price scraping or add price data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, gpuIds, priceData } = body

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'scrape':
        if (!gpuIds || !Array.isArray(gpuIds)) {
          return NextResponse.json(
            { error: 'GPU IDs array is required for scraping' },
            { status: 400 }
          )
        }
        
        const jobId = await backgroundJobProcessor.addPriceScrapingJob(gpuIds)
        return NextResponse.json({ 
          message: 'Price scraping job queued',
          jobId 
        }, { status: 202 })

      case 'add':
        if (!priceData || !priceData.gpuId || !priceData.prices) {
          return NextResponse.json(
            { error: 'Price data with gpuId and prices array is required' },
            { status: 400 }
          )
        }

        await pricingService.addPriceData(priceData.gpuId, priceData.prices)
        return NextResponse.json({ 
          message: 'Price data added successfully' 
        }, { status: 201 })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: scrape or add' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in pricing POST API:', error)
    return NextResponse.json(
      { error: 'Failed to process pricing request' },
      { status: 500 }
    )
  }
}