import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { pricingService } from '@/lib/pricing-service'

// GET /api/pricing/alerts - Get user's price alerts
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const alerts = await pricingService.getUserPriceAlerts(session.user.id)
    return NextResponse.json({ alerts })
  } catch (error) {
    console.error('Error fetching price alerts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch price alerts' },
      { status: 500 }
    )
  }
}

// POST /api/pricing/alerts - Create a new price alert
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { gpuId, targetPrice, emailAlert = true, pushAlert = false } = body

    if (!gpuId || !targetPrice) {
      return NextResponse.json(
        { error: 'GPU ID and target price are required' },
        { status: 400 }
      )
    }

    if (targetPrice <= 0 || targetPrice > 10000) {
      return NextResponse.json(
        { error: 'Target price must be between $1 and $10,000' },
        { status: 400 }
      )
    }

    await pricingService.createPriceAlert(
      session.user.id,
      gpuId,
      targetPrice,
      emailAlert,
      pushAlert
    )

    return NextResponse.json({ 
      message: 'Price alert created successfully' 
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating price alert:', error)
    return NextResponse.json(
      { error: 'Failed to create price alert' },
      { status: 500 }
    )
  }
}

// DELETE /api/pricing/alerts - Delete a price alert
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const alertId = searchParams.get('alertId')

    if (!alertId) {
      return NextResponse.json(
        { error: 'Alert ID is required' },
        { status: 400 }
      )
    }

    // Verify the alert belongs to the user and delete it
    const { prisma } = await import('@/lib/prisma')
    
    const alert = await prisma.priceAlert.findUnique({
      where: { id: alertId }
    })

    if (!alert) {
      return NextResponse.json(
        { error: 'Alert not found' },
        { status: 404 }
      )
    }

    if (alert.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    await prisma.priceAlert.delete({
      where: { id: alertId }
    })

    return NextResponse.json({ 
      message: 'Price alert deleted successfully' 
    })
  } catch (error) {
    console.error('Error deleting price alert:', error)
    return NextResponse.json(
      { error: 'Failed to delete price alert' },
      { status: 500 }
    )
  }
}