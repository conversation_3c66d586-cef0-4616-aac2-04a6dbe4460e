import { NextRequest, NextResponse } from 'next/server';
import { pricingService } from '@/lib/pricing-service';

// GET /api/pricing/[gpuId] - Get current prices for a specific GPU
export async function GET(
  request: NextRequest,
  { params }: { params: { gpuId: string } }
) {
  try {
    const { gpuId } = params;
    
    if (!gpuId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'GPU ID is required',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    const prices = await pricingService.getCurrentPrices(gpuId);
    
    return NextResponse.json({
      success: true,
      data: {
        gpuId,
        prices,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching GPU prices:', error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'PRICE_001',
          message: 'Failed to fetch GPU prices',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}