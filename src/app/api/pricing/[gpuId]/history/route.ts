import { NextRequest, NextResponse } from 'next/server';
import { pricingService } from '@/lib/pricing-service';

// GET /api/pricing/[gpuId]/history - Get price history for a specific GPU
export async function GET(
  request: NextRequest,
  { params }: { params: { gpuId: string } }
) {
  try {
    const { gpuId } = params;
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30d';
    
    if (!gpuId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'GPU ID is required',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    const history = await pricingService.getPriceHistory(gpuId, timeframe);
    
    return NextResponse.json({
      success: true,
      data: history
    });
  } catch (error) {
    console.error('Error fetching price history:', error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'PRICE_001',
          message: 'Failed to fetch price history',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}