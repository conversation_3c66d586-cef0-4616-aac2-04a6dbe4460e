import { NextRequest, NextResponse } from 'next/server';
import { pricingService } from '@/lib/pricing-service';

// POST /api/pricing/test - Test endpoint to simulate price updates
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gpuId, retailer, price, availability, url } = body;
    
    // Validate required fields
    if (!gpuId || !retailer || !price || !availability) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'gpuId, retailer, price, and availability are required',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    // Update the price
    await pricingService.updatePrice(gpuId, retailer, price, availability, url);
    
    return NextResponse.json({
      success: true,
      message: 'Price updated successfully',
      data: {
        gpuId,
        retailer,
        price,
        availability,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating price:', error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'PRICE_001',
          message: 'Failed to update price',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}

// GET /api/pricing/test - Test endpoint to check pricing service functionality
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gpuId = searchParams.get('gpuId');
    
    if (!gpuId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'gpuId parameter is required',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    // Test all pricing service methods
    const currentPrices = await pricingService.getCurrentPrices(gpuId);
    const priceHistory = await pricingService.getPriceHistory(gpuId, '7d');
    const marketTrends = await pricingService.getMarketTrends();
    
    return NextResponse.json({
      success: true,
      data: {
        gpuId,
        currentPrices,
        priceHistory,
        marketTrends,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error testing pricing service:', error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'PRICE_001',
          message: 'Failed to test pricing service',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}