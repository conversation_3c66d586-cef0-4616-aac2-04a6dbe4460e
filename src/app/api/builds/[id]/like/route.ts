import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// POST /api/builds/[id]/like - Toggle like on build
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if build exists
    const build = await prisma.buildShowcase.findUnique({
      where: { id: params.id }
    })

    if (!build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // Check if user already liked this build
    const existingLike = await prisma.buildLike.findUnique({
      where: {
        buildId_userId: {
          buildId: params.id,
          userId: session.user.id
        }
      }
    })

    let liked = false

    if (existingLike) {
      // Unlike - remove like and decrement count
      await prisma.$transaction([
        prisma.buildLike.delete({
          where: { id: existingLike.id }
        }),
        prisma.buildShowcase.update({
          where: { id: params.id },
          data: { likeCount: { decrement: 1 } }
        })
      ])
    } else {
      // Like - add like and increment count
      await prisma.$transaction([
        prisma.buildLike.create({
          data: {
            buildId: params.id,
            userId: session.user.id
          }
        }),
        prisma.buildShowcase.update({
          where: { id: params.id },
          data: { likeCount: { increment: 1 } }
        })
      ])
      liked = true
    }

    // Get updated like count
    const updatedBuild = await prisma.buildShowcase.findUnique({
      where: { id: params.id },
      select: { likeCount: true }
    })

    return NextResponse.json({
      liked,
      likeCount: updatedBuild?.likeCount || 0
    })
  } catch (error) {
    console.error('Error toggling like:', error)
    return NextResponse.json(
      { error: 'Failed to toggle like' },
      { status: 500 }
    )
  }
}