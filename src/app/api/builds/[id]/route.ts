import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/builds/[id] - Get build showcase with comments
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Increment view count
    await prisma.buildShowcase.update({
      where: { id: params.id },
      data: { viewCount: { increment: 1 } }
    })

    const [build, comments, commentCount] = await Promise.all([
      prisma.buildShowcase.findUnique({
        where: { id: params.id },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              image: true,
              reputation: {
                select: {
                  totalScore: true,
                  badges: true
                }
              }
            }
          },
          _count: {
            select: {
              comments: true,
              likes: true
            }
          }
        }
      }),
      prisma.buildComment.findMany({
        where: { 
          buildId: params.id,
          isHidden: false
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              image: true,
              reputation: {
                select: {
                  totalScore: true,
                  badges: true
                }
              }
            }
          }
        }
      }),
      prisma.buildComment.count({
        where: { 
          buildId: params.id,
          isHidden: false
        }
      })
    ])

    if (!build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      build,
      comments,
      pagination: {
        page,
        limit,
        total: commentCount,
        pages: Math.ceil(commentCount / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching build:', error)
    return NextResponse.json(
      { error: 'Failed to fetch build' },
      { status: 500 }
    )
  }
}

// PUT /api/builds/[id] - Update build showcase (author only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const build = await prisma.buildShowcase.findUnique({
      where: { id: params.id }
    })

    if (!build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    if (build.authorId !== session.user.id) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { 
      title, 
      description, 
      buildSpecs, 
      totalCost, 
      buildDate, 
      benchmarkData,
      images,
      videoUrl,
      tags 
    } = body

    const updatedBuild = await prisma.buildShowcase.update({
      where: { id: params.id },
      data: {
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(buildSpecs && { buildSpecs }),
        ...(totalCost !== undefined && { totalCost: totalCost ? parseFloat(totalCost) : null }),
        ...(buildDate !== undefined && { buildDate: buildDate ? new Date(buildDate) : null }),
        ...(benchmarkData !== undefined && { benchmarkData }),
        ...(images && { images }),
        ...(videoUrl !== undefined && { videoUrl }),
        ...(tags && { tags })
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            image: true
          }
        }
      }
    })

    return NextResponse.json(updatedBuild)
  } catch (error) {
    console.error('Error updating build:', error)
    return NextResponse.json(
      { error: 'Failed to update build' },
      { status: 500 }
    )
  }
}

// DELETE /api/builds/[id] - Delete build showcase (author only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const build = await prisma.buildShowcase.findUnique({
      where: { id: params.id }
    })

    if (!build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    if (build.authorId !== session.user.id) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    await prisma.buildShowcase.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Build deleted successfully' })
  } catch (error) {
    console.error('Error deleting build:', error)
    return NextResponse.json(
      { error: 'Failed to delete build' },
      { status: 500 }
    )
  }
}