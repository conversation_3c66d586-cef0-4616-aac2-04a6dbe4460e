import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/builds - Get build showcases with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const sortBy = searchParams.get('sortBy') || 'created'
    const tag = searchParams.get('tag')
    const featured = searchParams.get('featured') === 'true'
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    const where: any = {
      isPublished: true,
      isHidden: false
    }

    if (featured) {
      where.isFeatured = true
    }

    if (tag) {
      where.tags = { has: tag }
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    const orderBy: any = {}
    switch (sortBy) {
      case 'likes':
        orderBy.likeCount = 'desc'
        break
      case 'views':
        orderBy.viewCount = 'desc'
        break
      case 'cost':
        orderBy.totalCost = 'desc'
        break
      default:
        orderBy.createdAt = 'desc'
    }

    const [builds, total] = await Promise.all([
      prisma.buildShowcase.findMany({
        where,
        orderBy: [
          { isFeatured: 'desc' }, // Featured builds first
          orderBy
        ],
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              image: true,
              reputation: {
                select: {
                  totalScore: true,
                  badges: true
                }
              }
            }
          },
          _count: {
            select: {
              comments: true,
              likes: true
            }
          }
        }
      }),
      prisma.buildShowcase.count({ where })
    ])

    return NextResponse.json({
      builds,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching builds:', error)
    return NextResponse.json(
      { error: 'Failed to fetch builds' },
      { status: 500 }
    )
  }
}

// POST /api/builds - Create new build showcase
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      title, 
      description, 
      buildSpecs, 
      totalCost, 
      buildDate, 
      benchmarkData,
      images,
      videoUrl,
      tags 
    } = body

    if (!title || !buildSpecs) {
      return NextResponse.json(
        { error: 'Title and build specifications are required' },
        { status: 400 }
      )
    }

    const build = await prisma.buildShowcase.create({
      data: {
        authorId: session.user.id,
        title,
        description,
        buildSpecs,
        totalCost: totalCost ? parseFloat(totalCost) : null,
        buildDate: buildDate ? new Date(buildDate) : null,
        benchmarkData,
        images: images || [],
        videoUrl,
        tags: tags || []
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            image: true
          }
        }
      }
    })

    return NextResponse.json(build, { status: 201 })
  } catch (error) {
    console.error('Error creating build showcase:', error)
    return NextResponse.json(
      { error: 'Failed to create build showcase' },
      { status: 500 }
    )
  }
}