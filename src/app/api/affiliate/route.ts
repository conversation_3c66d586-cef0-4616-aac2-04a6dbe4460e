import { NextRequest, NextResponse } from 'next/server';
import { affiliateManager } from '@/lib/affiliate-manager';
import { headers } from 'next/headers';

// GET /api/affiliate - Get affiliate statistics and configurations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const retailer = searchParams.get('retailer');
    const days = parseInt(searchParams.get('days') || '30');

    switch (action) {
      case 'stats':
        return await getAffiliateStats(retailer, days);
      
      case 'top-gpus':
        const limit = parseInt(searchParams.get('limit') || '10');
        return await getTopPerformingGPUs(days, limit);
      
      case 'configs':
        return await getAffiliateConfigs();
      
      default:
        return NextResponse.json({
          message: 'Available actions: stats, top-gpus, configs',
          endpoints: {
            stats: '/api/affiliate?action=stats&retailer=<retailer>&days=<days>',
            topGpus: '/api/affiliate?action=top-gpus&days=<days>&limit=<limit>',
            configs: '/api/affiliate?action=configs'
          }
        });
    }
  } catch (error) {
    console.error('Error in affiliate GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/affiliate - Generate affiliate links and track clicks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'generate-link':
        return await generateAffiliateLink(body);
      
      case 'track-click':
        return await trackAffiliateClick(body, request);
      
      case 'cleanup':
        return await cleanupOldData();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Available: generate-link, track-click, cleanup' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in affiliate POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getAffiliateStats(retailer: string | null, days: number) {
  try {
    const stats = await affiliateManager.getAffiliateStats(retailer || undefined, days);
    
    return NextResponse.json({
      period: `${days} days`,
      retailer: retailer || 'all',
      stats,
      summary: {
        totalClicks: stats.reduce((sum, stat) => sum + stat.clicks, 0),
        totalConversions: stats.reduce((sum, stat) => sum + stat.conversions, 0),
        totalRevenue: stats.reduce((sum, stat) => sum + stat.revenue, 0),
        averageConversionRate: stats.length > 0 
          ? stats.reduce((sum, stat) => sum + stat.conversionRate, 0) / stats.length 
          : 0
      }
    });
  } catch (error) {
    console.error('Error getting affiliate stats:', error);
    return NextResponse.json(
      { error: 'Failed to get affiliate statistics' },
      { status: 500 }
    );
  }
}

async function getTopPerformingGPUs(days: number, limit: number) {
  try {
    const topGPUs = await affiliateManager.getTopPerformingGPUs(days, limit);
    
    return NextResponse.json({
      period: `${days} days`,
      limit,
      topGPUs,
      summary: {
        totalGPUs: topGPUs.length,
        totalClicks: topGPUs.reduce((sum, gpu) => sum + gpu.clicks, 0),
        topRetailers: [...new Set(topGPUs.flatMap(gpu => gpu.retailers))]
      }
    });
  } catch (error) {
    console.error('Error getting top performing GPUs:', error);
    return NextResponse.json(
      { error: 'Failed to get top performing GPUs' },
      { status: 500 }
    );
  }
}

async function getAffiliateConfigs() {
  try {
    const configs = affiliateManager.getActiveConfigs();
    
    // Remove sensitive information before sending to client
    const publicConfigs = configs.map(config => ({
      retailer: config.retailer,
      baseUrl: config.baseUrl,
      commission: config.commission,
      isActive: config.isActive,
      hasValidConfig: affiliateManager.validateConfig(config.retailer)
    }));
    
    return NextResponse.json({
      configs: publicConfigs,
      summary: {
        totalRetailers: configs.length,
        activeRetailers: configs.filter(c => c.isActive).length,
        validConfigs: configs.filter(c => affiliateManager.validateConfig(c.retailer)).length
      }
    });
  } catch (error) {
    console.error('Error getting affiliate configs:', error);
    return NextResponse.json(
      { error: 'Failed to get affiliate configurations' },
      { status: 500 }
    );
  }
}

async function generateAffiliateLink(body: any) {
  try {
    const { retailer, originalUrl, gpuId, productId, productName } = body;
    
    if (!retailer || !originalUrl || !gpuId) {
      return NextResponse.json(
        { error: 'Retailer, originalUrl, and gpuId are required' },
        { status: 400 }
      );
    }
    
    const affiliateLink = affiliateManager.generateAffiliateLink(
      retailer,
      originalUrl,
      gpuId,
      productId,
      productName
    );
    
    return NextResponse.json({
      originalUrl,
      affiliateLink,
      retailer,
      gpuId,
      hasAffiliateProgram: affiliateLink !== originalUrl,
      message: affiliateLink !== originalUrl 
        ? 'Affiliate link generated successfully'
        : 'No affiliate program configured for this retailer'
    });
  } catch (error) {
    console.error('Error generating affiliate link:', error);
    return NextResponse.json(
      { error: 'Failed to generate affiliate link' },
      { status: 500 }
    );
  }
}

async function trackAffiliateClick(body: any, request: NextRequest) {
  try {
    const { retailer, gpuId, sessionId, userId } = body;
    
    if (!retailer || !gpuId || !sessionId) {
      return NextResponse.json(
        { error: 'Retailer, gpuId, and sessionId are required' },
        { status: 400 }
      );
    }
    
    // Extract metadata from request
    const headersList = headers();
    const userAgent = headersList.get('user-agent') || undefined;
    const referrer = headersList.get('referer') || undefined;
    const forwardedFor = headersList.get('x-forwarded-for');
    const ipAddress = forwardedFor ? forwardedFor.split(',')[0] : 
                     headersList.get('x-real-ip') || 
                     request.ip || 
                     undefined;
    
    const clickId = await affiliateManager.trackClick(
      retailer,
      gpuId,
      sessionId,
      {
        userId,
        userAgent,
        referrer,
        ipAddress
      }
    );
    
    return NextResponse.json({
      clickId,
      message: 'Click tracked successfully',
      retailer,
      gpuId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error tracking affiliate click:', error);
    return NextResponse.json(
      { error: 'Failed to track affiliate click' },
      { status: 500 }
    );
  }
}

async function cleanupOldData() {
  try {
    const deletedCount = await affiliateManager.cleanupOldClicks();
    
    return NextResponse.json({
      message: `Cleaned up ${deletedCount} old affiliate clicks`,
      deletedCount,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error cleaning up affiliate data:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup affiliate data' },
      { status: 500 }
    );
  }
}
