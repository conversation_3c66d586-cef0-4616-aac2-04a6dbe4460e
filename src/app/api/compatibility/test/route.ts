import { NextRequest, NextResponse } from 'next/server'
import { CompatibilityService } from '@/lib/compatibility-service'

// POST /api/compatibility/test - Test compatibility service with sample data
export async function POST(request: NextRequest) {
  try {
    // Sample GPU data for testing
    const sampleGPU = {
      id: 'test-gpu',
      name: 'GeForce RTX 4070',
      manufacturer: 'NVIDIA',
      specifications: {
        memory: { size: 12, type: 'GDDR6X', bandwidth: 504.2, busWidth: 192 },
        cores: { cudaCores: 5888, rtCores: 46, tensorCores: 184 },
        clocks: { baseClock: 1920, boostClock: 2475, memoryClock: 1313 },
        process: '5nm',
      },
      physicalSpecs: {
        dimensions: { length: 240, width: 110, height: 40 },
        slots: 2,
        cooling: { type: 'Air', fans: 2 },
        ports: { displayPort: 3, hdmi: 1, dvi: 0, usbc: 0 },
      },
      powerRequirements: {
        tdp: 200,
        recommendedPSU: 550,
        connectors: ['16-pin'],
      },
    }

    // Sample system configurations
    const testSystems = [
      {
        name: 'High-End Gaming System',
        system: {
          cpu: { model: 'Intel i7-13700K', cores: 16, threads: 24, baseClock: 3400 },
          psu: { wattage: 750, efficiency: '80+ Gold', connectors: ['16-pin', '8-pin', '6-pin'] },
          case: { maxGPULength: 350, maxGPUWidth: 150, maxGPUHeight: 60 },
          motherboard: { model: 'Z790', chipset: 'Z790', pcieSlots: ['PCIe 5.0 x16'] },
          ram: { size: 32, speed: 5600, type: 'DDR5' },
        },
      },
      {
        name: 'Budget Gaming System',
        system: {
          cpu: { model: 'AMD Ryzen 5 5600', cores: 6, threads: 12, baseClock: 3500 },
          psu: { wattage: 500, efficiency: '80+ Bronze', connectors: ['8-pin', '6-pin'] },
          case: { maxGPULength: 280, maxGPUWidth: 120, maxGPUHeight: 40 },
          motherboard: { model: 'B450', chipset: 'B450', pcieSlots: ['PCIe 3.0 x16'] },
          ram: { size: 16, speed: 3200, type: 'DDR4' },
        },
      },
      {
        name: 'Compact ITX System',
        system: {
          cpu: { model: 'Intel i5-13400', cores: 10, threads: 16, baseClock: 2500 },
          psu: { wattage: 600, efficiency: '80+ Gold', connectors: ['16-pin'] },
          case: { maxGPULength: 215, maxGPUWidth: 110, maxGPUHeight: 40 },
          motherboard: { model: 'Z790-ITX', chipset: 'Z790', pcieSlots: ['PCIe 5.0 x16'] },
          ram: { size: 32, speed: 5200, type: 'DDR5' },
        },
      },
    ]

    const results = []

    for (const testSystem of testSystems) {
      const compatibility = await CompatibilityService.checkCompatibility(
        sampleGPU,
        testSystem.system
      )

      results.push({
        systemName: testSystem.name,
        compatibility,
        summary: {
          overall: compatibility.overall,
          powerStatus: compatibility.power.status,
          physicalStatus: compatibility.physical.status,
          bottleneckSeverity: compatibility.bottleneck.severity,
          recommendationCount: compatibility.recommendations.length,
        },
      })
    }

    return NextResponse.json({
      message: 'Compatibility service test completed',
      gpu: {
        name: sampleGPU.name,
        manufacturer: sampleGPU.manufacturer,
      },
      results,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Error testing compatibility service:', error)
    return NextResponse.json(
      { error: 'Failed to test compatibility service' },
      { status: 500 }
    )
  }
}

// GET /api/compatibility/test - Get compatibility test information
export async function GET() {
  return NextResponse.json({
    message: 'Compatibility Service Test Endpoint',
    description: 'POST to this endpoint to run compatibility tests with sample data',
    features: [
      'Power supply compatibility checking',
      'Physical clearance validation',
      'CPU bottleneck analysis',
      'Comprehensive recommendations',
    ],
    testSystems: [
      'High-End Gaming System',
      'Budget Gaming System',
      'Compact ITX System',
    ],
  })
}