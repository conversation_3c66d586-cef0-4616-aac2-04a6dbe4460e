import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { CompatibilityService } from '@/lib/compatibility-service'

// POST /api/compatibility - Check GPU compatibility with system
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      gpuId,
      system,
    } = body

    // Validate required fields
    if (!gpuId || !system) {
      return NextResponse.json(
        { error: 'Missing required fields: gpuId, system' },
        { status: 400 }
      )
    }

    // Get GPU data
    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId },
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    // Perform compatibility check
    const compatibilityResult = await CompatibilityService.checkCompatibility(gpu, system)

    return NextResponse.json({
      gpu: {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
      },
      system,
      compatibility: compatibilityResult,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Error checking compatibility:', error)
    return NextResponse.json(
      { error: 'Failed to check compatibility' },
      { status: 500 }
    )
  }
}

// GET /api/compatibility/requirements/[gpuId] - Get GPU requirements
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gpuId = searchParams.get('gpuId')

    if (!gpuId) {
      return NextResponse.json(
        { error: 'Missing gpuId parameter' },
        { status: 400 }
      )
    }

    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId },
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    const requirements = CompatibilityService.getGPURequirements(gpu)

    return NextResponse.json({
      gpu: {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
      },
      requirements,
    })
  } catch (error) {
    console.error('Error getting GPU requirements:', error)
    return NextResponse.json(
      { error: 'Failed to get GPU requirements' },
      { status: 500 }
    )
  }
}