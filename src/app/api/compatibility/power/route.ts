import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { CompatibilityService } from '@/lib/compatibility-service'

// POST /api/compatibility/power - Check power supply compatibility
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const { gpuId, psu } = body

    if (!gpuId || !psu) {
      return NextResponse.json(
        { error: 'Missing required fields: gpuId, psu' },
        { status: 400 }
      )
    }

    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId },
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    const powerCompatibility = CompatibilityService.validatePowerSupply(gpu, psu)

    return NextResponse.json({
      gpu: {
        id: gpu.id,
        name: gpu.name,
      },
      psu,
      compatibility: powerCompatibility,
      recommendations: powerCompatibility.status !== 'compatible' ? [
        ...(powerCompatibility.connectors.missing.length > 0 ? 
          [`Missing power connectors: ${powerCompatibility.connectors.missing.join(', ')}`] : []),
        ...(powerCompatibility.available < powerCompatibility.required ? 
          [`PSU wattage insufficient: ${powerCompatibility.available}W available, ${powerCompatibility.required}W recommended`] : [])
      ] : ['Power supply is compatible!'],
    })
  } catch (error) {
    console.error('Error checking power compatibility:', error)
    return NextResponse.json(
      { error: 'Failed to check power compatibility' },
      { status: 500 }
    )
  }
}