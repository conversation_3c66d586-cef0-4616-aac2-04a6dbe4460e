import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/reviews - Get reviews with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gpuId = searchParams.get('gpuId')
    const userId = searchParams.get('userId')
    const rating = searchParams.get('rating')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      isHidden: false // Only show non-hidden reviews
    }

    if (gpuId) where.gpuId = gpuId
    if (userId) where.userId = userId
    if (rating) where.rating = parseInt(rating)

    // Build orderBy clause
    const orderBy: any = {}
    if (sortBy === 'rating' || sortBy === 'createdAt') {
      orderBy[sortBy] = sortOrder
    } else {
      orderBy.createdAt = 'desc'
    }

    const [reviews, totalCount] = await Promise.all([
      prisma.userReview.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              username: true,
              createdAt: true,
              // Calculate user's review count for reputation
              _count: {
                select: {
                  reviews: {
                    where: {
                      isHidden: false
                    }
                  }
                }
              }
            }
          },
          gpu: {
            select: {
              id: true,
              name: true,
              manufacturer: true,
              architecture: true
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.userReview.count({ where })
    ])

    return NextResponse.json({
      reviews,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching reviews:', error)
    return NextResponse.json(
      { error: 'Failed to fetch reviews' },
      { status: 500 }
    )
  }
}

// POST /api/reviews - Create a new review
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { 
      gpuId, 
      rating, 
      title, 
      content, 
      systemSpecs, 
      performanceData 
    } = await request.json()

    // Validation
    if (!gpuId || !rating) {
      return NextResponse.json(
        { error: 'GPU ID and rating are required' },
        { status: 400 }
      )
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      )
    }

    // Check if GPU exists
    const gpu = await prisma.gPU.findUnique({
      where: { id: gpuId }
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    // Check if user already reviewed this GPU
    const existingReview = await prisma.userReview.findUnique({
      where: {
        userId_gpuId: {
          userId: session.user.id,
          gpuId: gpuId
        }
      }
    })

    if (existingReview) {
      return NextResponse.json(
        { error: 'You have already reviewed this GPU' },
        { status: 400 }
      )
    }

    // Create review
    const review = await prisma.userReview.create({
      data: {
        userId: session.user.id,
        gpuId: gpuId,
        rating: parseInt(rating),
        title: title || null,
        content: content || null,
        systemSpecs: systemSpecs || null,
        performanceData: performanceData || null
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            createdAt: true
          }
        },
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    return NextResponse.json(
      { 
        message: 'Review created successfully',
        review 
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating review:', error)
    return NextResponse.json(
      { error: 'Failed to create review' },
      { status: 500 }
    )
  }
}