import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/reviews/[id] - Get specific review
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reviewId = params.id

    const review = await prisma.userReview.findUnique({
      where: {
        id: reviewId,
        isHidden: false
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            createdAt: true,
            _count: {
              select: {
                reviews: {
                  where: {
                    isHidden: false
                  }
                }
              }
            }
          }
        },
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ review })
  } catch (error) {
    console.error('Error fetching review:', error)
    return NextResponse.json(
      { error: 'Failed to fetch review' },
      { status: 500 }
    )
  }
}

// PUT /api/reviews/[id] - Update review
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const reviewId = params.id
    const { 
      rating, 
      title, 
      content, 
      systemSpecs, 
      performanceData 
    } = await request.json()

    // Find the review and verify ownership
    const review = await prisma.userReview.findUnique({
      where: { id: reviewId }
    })

    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      )
    }

    if (review.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to update this review' },
        { status: 403 }
      )
    }

    // Validation
    if (rating && (rating < 1 || rating > 5)) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      )
    }

    // Update review
    const updatedReview = await prisma.userReview.update({
      where: { id: reviewId },
      data: {
        ...(rating !== undefined && { rating: parseInt(rating) }),
        ...(title !== undefined && { title: title || null }),
        ...(content !== undefined && { content: content || null }),
        ...(systemSpecs !== undefined && { systemSpecs: systemSpecs || null }),
        ...(performanceData !== undefined && { performanceData: performanceData || null })
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            createdAt: true
          }
        },
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
            architecture: true
          }
        }
      }
    })

    return NextResponse.json(
      { 
        message: 'Review updated successfully',
        review: updatedReview 
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error updating review:', error)
    return NextResponse.json(
      { error: 'Failed to update review' },
      { status: 500 }
    )
  }
}

// DELETE /api/reviews/[id] - Delete review
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const reviewId = params.id

    // Find the review and verify ownership
    const review = await prisma.userReview.findUnique({
      where: { id: reviewId }
    })

    if (!review) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      )
    }

    if (review.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to delete this review' },
        { status: 403 }
      )
    }

    // Delete the review
    await prisma.userReview.delete({
      where: { id: reviewId }
    })

    return NextResponse.json(
      { message: 'Review deleted successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error deleting review:', error)
    return NextResponse.json(
      { error: 'Failed to delete review' },
      { status: 500 }
    )
  }
}