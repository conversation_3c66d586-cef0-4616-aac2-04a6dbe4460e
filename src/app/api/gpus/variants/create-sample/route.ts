import { NextRequest, NextResponse } from 'next/server'
import { GPUVariantService } from '@/lib/gpu-variant-service'

// POST /api/gpus/variants/create-sample - Create sample GPU variants for demonstration
export async function POST(request: NextRequest) {
  try {
    // Sample variants for RTX 4090 D (we know this base GPU exists)
    const rtx4090DBaseId = "cmda950zd003or8dfi9322666" // From our previous API call
    
    const sampleVariants = [
      {
        baseGpuId: rtx4090DBaseId,
        boardPartner: "ASUS",
        modelName: "ROG Strix RTX 4090 D OC",
        sku: "ROG-STRIX-RTX4090D-O24G-GAMING",
        customSpecs: {
          clocks: {
            baseClock: 2340,
            boostClock: 2640,
            memoryClock: 1313
          }
        },
        physicalSpecs: {
          ports: { dvi: 0, hdmi: 2, usbc: 0, displayPort: 3 },
          slots: 3.5,
          weight: 2.5,
          cooling: { fans: 3, type: "Air", heatpipes: 7 },
          dimensions: { width: 149, height: 70, length: 357 }
        },
        powerRequirements: {
          tdp: 450,
          connectors: ["16-pin"],
          recommendedPSU: 850
        },
        features: {
          rgb: true,
          dualBios: true,
          backplate: true,
          zeroFanMode: true,
          customCooling: "Axial-tech fans with 0dB mode",
          overclockingSupport: true
        },
        warranty: "3 years"
      },
      {
        baseGpuId: rtx4090DBaseId,
        boardPartner: "MSI",
        modelName: "Gaming X Trio RTX 4090 D",
        sku: "RTX-4090D-GAMING-X-TRIO-24G",
        customSpecs: {
          clocks: {
            baseClock: 2320,
            boostClock: 2610,
            memoryClock: 1313
          }
        },
        physicalSpecs: {
          ports: { dvi: 0, hdmi: 1, usbc: 0, displayPort: 3 },
          slots: 3,
          weight: 2.2,
          cooling: { fans: 3, type: "Air", heatpipes: 6 },
          dimensions: { width: 140, height: 61, length: 336 }
        },
        powerRequirements: {
          tdp: 450,
          connectors: ["16-pin"],
          recommendedPSU: 850
        },
        features: {
          rgb: true,
          dualBios: true,
          backplate: true,
          zeroFanMode: true,
          customCooling: "Tri Frozr 3 cooling",
          overclockingSupport: true
        },
        warranty: "3 years"
      },
      {
        baseGpuId: rtx4090DBaseId,
        boardPartner: "Gigabyte",
        modelName: "Gaming OC RTX 4090 D",
        sku: "GV-N4090DGAMING-OC-24GD",
        customSpecs: {
          clocks: {
            baseClock: 2310,
            boostClock: 2595,
            memoryClock: 1313
          }
        },
        physicalSpecs: {
          ports: { dvi: 0, hdmi: 2, usbc: 0, displayPort: 2 },
          slots: 3,
          weight: 2.0,
          cooling: { fans: 3, type: "Air", heatpipes: 5 },
          dimensions: { width: 136, height: 56, length: 320 }
        },
        powerRequirements: {
          tdp: 450,
          connectors: ["16-pin"],
          recommendedPSU: 850
        },
        features: {
          rgb: true,
          dualBios: false,
          backplate: true,
          zeroFanMode: true,
          customCooling: "WINDFORCE 3X cooling",
          overclockingSupport: true
        },
        warranty: "4 years"
      }
    ]
    
    const results = []
    let successCount = 0
    let errorCount = 0
    
    for (const variantData of sampleVariants) {
      try {
        const variant = await GPUVariantService.createGPUVariant(variantData)
        results.push({
          status: 'success',
          variant: {
            id: variant.id,
            boardPartner: variant.boardPartner,
            modelName: variant.modelName,
            sku: variant.sku
          }
        })
        successCount++
      } catch (error) {
        results.push({
          status: 'error',
          boardPartner: variantData.boardPartner,
          modelName: variantData.modelName,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        errorCount++
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Sample variants created: ${successCount} successful, ${errorCount} errors`,
      results,
      stats: {
        processed: sampleVariants.length,
        successful: successCount,
        errors: errorCount
      }
    })
    
  } catch (error) {
    console.error('Error creating sample variants:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create sample variants',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
