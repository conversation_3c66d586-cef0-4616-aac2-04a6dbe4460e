import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/gpus/[id] - Get a specific GPU by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const gpu = await prisma.gPU.findUnique({
      where: { id: params.id },
      include: {
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 10, // Get latest prices from different retailers
          distinct: ['retailer'],
        },
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                createdAt: true,
              },
            },
          },
          where: { isHidden: false },
          orderBy: [
            { isVerified: 'desc' },
            { createdAt: 'desc' },
          ],
          take: 5, // Get latest 5 reviews
        },
        benchmarks: {
          orderBy: { testDate: 'desc' },
          take: 20, // Get latest benchmarks
        },
        _count: {
          select: {
            reviews: true,
            wishlists: true,
          },
        },
      },
    })

    if (!gpu) {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }

    // Calculate review statistics
    const avgRating = gpu.reviews.length > 0 
      ? gpu.reviews.reduce((sum, review) => sum + review.rating, 0) / gpu.reviews.length
      : 0

    // Get current pricing info
    const currentPrices = gpu.pricing.map(price => ({
      retailer: price.retailer,
      price: Number(price.price),
      availability: price.availability,
      url: price.url,
      lastUpdated: price.scrapedAt,
    }))

    const lowestPrice = currentPrices.length > 0
      ? Math.min(...currentPrices.map(p => p.price))
      : null

    // Format benchmarks by category
    const benchmarksByType = gpu.benchmarks.reduce((acc, benchmark) => {
      if (!acc[benchmark.benchmarkType]) {
        acc[benchmark.benchmarkType] = []
      }
      acc[benchmark.benchmarkType].push({
        testName: benchmark.testName,
        resolution: benchmark.resolution,
        settings: benchmark.settings,
        fps: benchmark.fps ? Number(benchmark.fps) : null,
        score: benchmark.score,
        testDate: benchmark.testDate,
        source: benchmark.source,
        benchmarkData: benchmark.benchmarkData,
      })
      return acc
    }, {} as Record<string, any[]>)

    const formattedGPU = {
      id: gpu.id,
      name: gpu.name,
      manufacturer: gpu.manufacturer,
      architecture: gpu.architecture,
      launchDate: gpu.launchDate,
      originalMsrp: gpu.originalMsrp ? Number(gpu.originalMsrp) : null,
      specifications: gpu.specifications,
      physicalSpecs: gpu.physicalSpecs,
      powerRequirements: gpu.powerRequirements,
      pricing: {
        current: currentPrices,
        lowest: lowestPrice,
      },
      reviews: {
        average: Math.round(avgRating * 10) / 10,
        count: gpu._count.reviews,
        recent: gpu.reviews.map(review => ({
          id: review.id,
          rating: review.rating,
          title: review.title,
          content: review.content,
          systemSpecs: review.systemSpecs,
          performanceData: review.performanceData,
          isVerified: review.isVerified,
          createdAt: review.createdAt,
          user: review.user,
        })),
      },
      benchmarks: benchmarksByType,
      popularity: {
        wishlistCount: gpu._count.wishlists,
      },
      createdAt: gpu.createdAt,
      updatedAt: gpu.updatedAt,
    }

    return NextResponse.json(formattedGPU)
  } catch (error) {
    console.error('Error fetching GPU:', error)
    return NextResponse.json(
      { error: 'Failed to fetch GPU' },
      { status: 500 }
    )
  }
}

// PUT /api/gpus/[id] - Update a GPU (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    const {
      name,
      manufacturer,
      architecture,
      launchDate,
      originalMsrp,
      specifications,
      physicalSpecs,
      powerRequirements,
    } = body

    const gpu = await prisma.gPU.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(manufacturer && { manufacturer }),
        ...(architecture && { architecture }),
        ...(launchDate && { launchDate: new Date(launchDate) }),
        ...(originalMsrp !== undefined && { originalMsrp }),
        ...(specifications && { specifications }),
        ...(physicalSpecs && { physicalSpecs }),
        ...(powerRequirements && { powerRequirements }),
      },
    })

    return NextResponse.json(gpu)
  } catch (error) {
    console.error('Error updating GPU:', error)
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to update GPU' },
      { status: 500 }
    )
  }
}

// DELETE /api/gpus/[id] - Delete a GPU (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.gPU.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ message: 'GPU deleted successfully' })
  } catch (error) {
    console.error('Error deleting GPU:', error)
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'GPU not found' },
        { status: 404 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to delete GPU' },
      { status: 500 }
    )
  }
}