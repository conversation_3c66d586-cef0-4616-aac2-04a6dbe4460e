import { NextRequest, NextResponse } from 'next/server'
import { GPUVariantService } from '@/lib/gpu-variant-service'

// POST /api/gpus/add-variants - Add board partner variants for popular GPUs
export async function POST(request: NextRequest) {
  try {
    const { baseGpuId, variants } = await request.json()
    
    if (!baseGpuId || !variants || !Array.isArray(variants)) {
      return NextResponse.json(
        { error: 'baseGpuId and variants array are required' },
        { status: 400 }
      )
    }
    
    const results = []
    let successCount = 0
    let errorCount = 0
    
    for (const variantData of variants) {
      try {
        const variant = await GPUVariantService.createGPUVariant({
          baseGpuId,
          ...variantData
        })
        
        results.push({
          id: variant.id,
          modelName: variant.modelName,
          boardPartner: variant.boardPartner,
          status: 'success'
        })
        successCount++
      } catch (error) {
        results.push({
          modelName: variantData.modelName,
          boardPartner: variantData.boardPartner,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        errorCount++
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Added ${successCount} variants successfully, ${errorCount} errors`,
      results,
      stats: {
        processed: variants.length,
        successful: successCount,
        errors: errorCount
      }
    })
    
  } catch (error) {
    console.error('Error adding GPU variants:', error)
    return NextResponse.json(
      { 
        error: 'Failed to add GPU variants',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET /api/gpus/add-variants - Get sample variant data for popular GPUs
export async function GET() {
  const sampleVariants = {
    "rtx4090d": {
      baseGpuName: "GeForce RTX 4090 D",
      variants: [
        {
          boardPartner: "ASUS",
          modelName: "ROG Strix RTX 4090 D OC",
          sku: "ROG-STRIX-RTX4090D-O24G-GAMING",
          customSpecs: {
            clocks: {
              baseClock: 2340,
              boostClock: 2640,
              memoryClock: 1313
            }
          },
          physicalSpecs: {
            dimensions: { length: 357, width: 149, height: 70 },
            slots: 3.5,
            weight: 2.5,
            cooling: {
              type: "Air",
              fans: 3,
              heatpipes: 7
            },
            ports: {
              hdmi: 2,
              displayPort: 3,
              dvi: 0,
              usbc: 0
            }
          },
          powerRequirements: {
            tdp: 450,
            connectors: ["16-pin"],
            recommendedPSU: 850
          },
          features: {
            rgb: true,
            overclockingSupport: true,
            customCooling: "Axial-tech fans with 0dB mode",
            backplate: true,
            dualBios: true,
            zeroFanMode: true
          },
          warranty: "3 years"
        },
        {
          boardPartner: "MSI",
          modelName: "Gaming X Trio RTX 4090 D",
          sku: "RTX-4090D-GAMING-X-TRIO-24G",
          customSpecs: {
            clocks: {
              baseClock: 2320,
              boostClock: 2610,
              memoryClock: 1313
            }
          },
          physicalSpecs: {
            dimensions: { length: 336, width: 140, height: 61 },
            slots: 3,
            weight: 2.2,
            cooling: {
              type: "Air",
              fans: 3,
              heatpipes: 6
            },
            ports: {
              hdmi: 1,
              displayPort: 3,
              dvi: 0,
              usbc: 0
            }
          },
          powerRequirements: {
            tdp: 450,
            connectors: ["16-pin"],
            recommendedPSU: 850
          },
          features: {
            rgb: true,
            overclockingSupport: true,
            customCooling: "Tri Frozr 3 cooling",
            backplate: true,
            dualBios: true,
            zeroFanMode: true
          },
          warranty: "3 years"
        },
        {
          boardPartner: "GIGABYTE",
          modelName: "Gaming OC RTX 4090 D",
          sku: "GV-N4090DGAMING-OC-24GD",
          customSpecs: {
            clocks: {
              baseClock: 2310,
              boostClock: 2595,
              memoryClock: 1313
            }
          },
          physicalSpecs: {
            dimensions: { length: 340, width: 150, height: 75 },
            slots: 3.5,
            weight: 2.4,
            cooling: {
              type: "Air",
              fans: 3,
              heatpipes: 7
            },
            ports: {
              hdmi: 2,
              displayPort: 3,
              dvi: 0,
              usbc: 0
            }
          },
          powerRequirements: {
            tdp: 450,
            connectors: ["16-pin"],
            recommendedPSU: 850
          },
          features: {
            rgb: true,
            overclockingSupport: true,
            customCooling: "WINDFORCE 3X cooling",
            backplate: true,
            dualBios: false,
            zeroFanMode: true
          },
          warranty: "4 years"
        }
      ]
    }
  }
  
  return NextResponse.json({
    success: true,
    message: "Sample variant data for popular GPUs",
    data: sampleVariants
  })
}
