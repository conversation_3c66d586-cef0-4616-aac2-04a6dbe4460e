import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { GPUFilters } from '@/types/gpu'

// GET /api/gpus - Get GPUs with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    const skip = (page - 1) * limit
    
    const manufacturer = searchParams.get('manufacturer')
    const architecture = searchParams.get('architecture')
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sortBy') || 'name'
    const sortOrder = searchParams.get('sortOrder') || 'asc'

    // Build where clause
    const where: any = {}
    
    if (manufacturer) {
      where.manufacturer = { in: manufacturer.split(',') }
    }
    
    if (architecture) {
      where.architecture = { in: architecture.split(',') }
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { manufacturer: { contains: search, mode: 'insensitive' } },
        { architecture: { contains: search, mode: 'insensitive' } },
      ]
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (sortBy === 'price') {
      // For price sorting, we'll need to join with pricing data
      orderBy.name = sortOrder as 'asc' | 'desc' // Fallback to name for now
    } else {
      orderBy[sortBy] = sortOrder as 'asc' | 'desc'
    }

    // Execute query
    const [gpus, total] = await Promise.all([
      prisma.gPU.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          pricing: {
            orderBy: { scrapedAt: 'desc' },
            take: 1, // Get latest price
            distinct: ['retailer'],
          },
          reviews: {
            select: {
              rating: true,
            },
          },
          _count: {
            select: {
              reviews: true,
            },
          },
        },
      }),
      prisma.gPU.count({ where }),
    ])

    // Calculate average ratings and format response
    const formattedGPUs = gpus.map(gpu => {
      const avgRating = gpu.reviews.length > 0 
        ? gpu.reviews.reduce((sum, review) => sum + review.rating, 0) / gpu.reviews.length
        : 0

      const lowestPrice = gpu.pricing.length > 0
        ? Math.min(...gpu.pricing.map(p => Number(p.price)))
        : null

      return {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
        architecture: gpu.architecture,
        launchDate: gpu.launchDate,
        originalMsrp: gpu.originalMsrp ? Number(gpu.originalMsrp) : null,
        specifications: gpu.specifications,
        physicalSpecs: gpu.physicalSpecs,
        powerRequirements: gpu.powerRequirements,
        currentPrice: lowestPrice,
        averageRating: Math.round(avgRating * 10) / 10,
        reviewCount: gpu._count.reviews,
        createdAt: gpu.createdAt,
        updatedAt: gpu.updatedAt,
      }
    })

    return NextResponse.json({
      gpus: formattedGPUs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching GPUs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch GPUs' },
      { status: 500 }
    )
  }
}

// POST /api/gpus - Create a new GPU (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      name,
      manufacturer,
      architecture,
      launchDate,
      originalMsrp,
      specifications,
      physicalSpecs,
      powerRequirements,
    } = body

    // Validate required fields
    if (!name || !manufacturer || !specifications) {
      return NextResponse.json(
        { error: 'Missing required fields: name, manufacturer, specifications' },
        { status: 400 }
      )
    }

    const gpu = await prisma.gPU.create({
      data: {
        name,
        manufacturer,
        architecture,
        launchDate: launchDate ? new Date(launchDate) : null,
        originalMsrp,
        specifications,
        physicalSpecs,
        powerRequirements,
      },
    })

    return NextResponse.json(gpu, { status: 201 })
  } catch (error) {
    console.error('Error creating GPU:', error)
    return NextResponse.json(
      { error: 'Failed to create GPU' },
      { status: 500 }
    )
  }
}