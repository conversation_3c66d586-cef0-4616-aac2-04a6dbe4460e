import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// POST /api/gpus/import-variants - Import GPU variants from Google Sheets
export async function POST(request: NextRequest) {
  try {
    const { force } = await request.json().catch(() => ({ force: false }))
    
    if (process.env.NODE_ENV === 'production' && !force) {
      return NextResponse.json(
        { error: 'Import is not allowed in production without force flag' },
        { status: 403 }
      )
    }

    console.log('Starting GPU variants import from Google Sheets...')
    
    const apiUrl = process.env.GPU_DATA_API_URL
    if (!apiUrl) {
      return NextResponse.json(
        { error: 'GPU_DATA_API_URL not configured' },
        { status: 500 }
      )
    }

    // Fetch variants data from Google Sheets
    const response = await fetch(`${apiUrl}?variants=true`)
    if (!response.ok) {
      throw new Error(`Failed to fetch variants data: ${response.statusText}`)
    }

    const data = await response.json()
    if (!data.success || !Array.isArray(data.data)) {
      throw new Error('Invalid response format from Google Sheets API')
    }

    console.log(`Processing ${data.variantCount} variants for ${data.baseGPUCount} base GPUs...`)

    let processedBaseGPUs = 0
    let processedVariants = 0
    let errors = 0

    // Process each base GPU and its variants
    for (const baseGPUData of data.data) {
      try {
        // Find or create the base GPU
        let baseGpu = await prisma.baseGPU.findFirst({
          where: { name: baseGPUData.productName }
        })

        if (!baseGpu) {
          // Create base GPU from the data
          baseGpu = await prisma.baseGPU.create({
            data: {
              name: baseGPUData.productName,
              chipManufacturer: baseGPUData.brand?.toUpperCase() || 'UNKNOWN',
              architecture: extractArchitecture(baseGPUData.productName),
              launchDate: parseDate(baseGPUData.released),
              originalMsrp: parsePrice(baseGPUData.launchPrice),
              specifications: {
                memory: {
                  size: parseMemorySize(baseGPUData.memory),
                  type: parseMemoryType(baseGPUData.memory),
                  bandwidth: parseMemoryBandwidth(baseGPUData.memoryClock)
                },
                cores: {
                  cudaCores: parseCudaCores(baseGPUData.shadersTMUsROPs),
                  streamProcessors: parseStreamProcessors(baseGPUData.shadersTMUsROPs),
                  rtCores: parseInt(baseGPUData.rtCores) || null,
                  tensorCores: parseInt(baseGPUData.tensorCores) || null
                },
                clocks: {
                  baseClock: parseClockSpeed(baseGPUData.gpuClock, 'base'),
                  boostClock: parseClockSpeed(baseGPUData.gpuClock, 'boost'),
                  memoryClock: parseClockSpeed(baseGPUData.memoryClock)
                }
              },
              powerRequirements: {
                tdp: parseInt(baseGPUData.tdp) || null,
                recommendedPSU: parseInt(baseGPUData.suggestedPSU) || null,
                powerConnectors: parseConnectors(baseGPUData.powerConnector)
              },
              physicalSpecs: {
                dimensions: baseGPUData.dimensions || null,
                slots: parseSlots(baseGPUData.dimensions),
                ports: parseDisplayPorts(baseGPUData.ports)
              }
            }
          })
          processedBaseGPUs++
        }

        // Process variants for this base GPU
        if (baseGPUData.variants && Array.isArray(baseGPUData.variants)) {
          for (const variantData of baseGPUData.variants) {
            try {
              // Check if variant already exists
              const existingVariant = await prisma.gPUVariant.findFirst({
                where: {
                  baseGpuId: baseGpu.id,
                  modelName: variantData.modelName,
                  boardPartner: variantData.boardPartner
                }
              })

              if (!existingVariant) {
                await prisma.gPUVariant.create({
                  data: {
                    baseGpuId: baseGpu.id,
                    boardPartner: variantData.boardPartner,
                    modelName: variantData.modelName,
                    sku: variantData.sku,
                    clockOverrides: {
                      baseClock: parseFloat(variantData.baseClock) || null,
                      boostClock: parseFloat(variantData.boostClock) || null,
                      memoryClock: parseFloat(variantData.memoryClock) || null
                    },
                    powerRequirements: {
                      tdp: parseInt(variantData.tdpOverride) || null
                    },
                    coolingSpecs: {
                      type: variantData.coolingType,
                      fanCount: parseInt(variantData.coolingFans) || null,
                      heatpipeCount: parseInt(variantData.coolingHeatpipes) || null
                    },
                    features: {
                      rgbLighting: variantData.rgbLighting === 'Yes',
                      overclocked: variantData.factoryOverclock === 'Yes',
                      dualBios: variantData.dualBios === 'Yes',
                      zeroRpmMode: variantData.zeroRpmMode === 'Yes'
                    },
                    physicalSpecs: {
                      length: parseFloat(variantData.cardLength) || null,
                      width: parseFloat(variantData.cardWidth) || null,
                      height: parseFloat(variantData.cardHeight) || null,
                      weight: parseFloat(variantData.cardWeight) || null,
                      slots: parseFloat(variantData.slotWidth) || null
                    },
                    warrantyInfo: {
                      years: parseInt(variantData.warrantyYears) || null,
                      transferable: variantData.warrantyTransferable === 'Yes'
                    }
                  }
                })
                processedVariants++
              }
            } catch (variantError) {
              console.error(`Error processing variant ${variantData.modelName}:`, variantError)
              errors++
            }
          }
        }
      } catch (baseGpuError) {
        console.error(`Error processing base GPU ${baseGPUData.productName}:`, baseGpuError)
        errors++
      }
    }

    return NextResponse.json({
      message: 'GPU variants import completed',
      result: {
        processedBaseGPUs,
        processedVariants,
        errors,
        totalBaseGPUs: data.baseGPUCount,
        totalVariants: data.variantCount
      }
    }, { status: 200 })
    
  } catch (error) {
    console.error('Error importing GPU variants:', error)
    return NextResponse.json(
      { 
        error: 'Failed to import GPU variants',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Helper functions for parsing data
function extractArchitecture(gpuName: string): string {
  const name = gpuName.toLowerCase()
  if (name.includes('4090') || name.includes('4080') || name.includes('4070')) return 'Ada Lovelace'
  if (name.includes('3090') || name.includes('3080') || name.includes('3070') || name.includes('3060')) return 'Ampere'
  if (name.includes('7900') || name.includes('7800') || name.includes('7700')) return 'RDNA 3'
  if (name.includes('6900') || name.includes('6800') || name.includes('6700') || name.includes('6600')) return 'RDNA 2'
  if (name.includes('arc')) return 'Xe-HPG'
  return 'Unknown'
}

function parseDate(dateStr: string): Date | null {
  if (!dateStr) return null
  const date = new Date(dateStr)
  return isNaN(date.getTime()) ? null : date
}

function parsePrice(priceStr: string): number | null {
  if (!priceStr) return null
  const price = parseFloat(priceStr.replace(/[^0-9.]/g, ''))
  return isNaN(price) ? null : price
}

function parseMemorySize(memoryStr: string): number | null {
  if (!memoryStr) return null
  const match = memoryStr.match(/(\d+)\s*GB/i)
  return match ? parseInt(match[1]) : null
}

function parseMemoryType(memoryStr: string): string | null {
  if (!memoryStr) return null
  if (memoryStr.includes('GDDR6X')) return 'GDDR6X'
  if (memoryStr.includes('GDDR6')) return 'GDDR6'
  if (memoryStr.includes('GDDR5')) return 'GDDR5'
  if (memoryStr.includes('HBM')) return 'HBM2'
  return null
}

function parseMemoryBandwidth(clockStr: string): number | null {
  if (!clockStr) return null
  const match = clockStr.match(/(\d+)\s*MHz/i)
  return match ? parseInt(match[1]) : null
}

function parseCudaCores(shadersStr: string): number | null {
  if (!shadersStr) return null
  const match = shadersStr.match(/(\d+)/)
  return match ? parseInt(match[1]) : null
}

function parseStreamProcessors(shadersStr: string): number | null {
  return parseCudaCores(shadersStr) // Same parsing logic
}

function parseClockSpeed(clockStr: string, type?: 'base' | 'boost'): number | null {
  if (!clockStr) return null
  const matches = clockStr.match(/(\d+)\s*MHz/gi)
  if (!matches) return null
  
  if (type === 'base') return parseInt(matches[0].replace(/[^0-9]/g, ''))
  if (type === 'boost' && matches.length > 1) return parseInt(matches[1].replace(/[^0-9]/g, ''))
  return parseInt(matches[0].replace(/[^0-9]/g, ''))
}

function parseConnectors(connectorStr: string): string[] {
  if (!connectorStr) return []
  return connectorStr.split(',').map(c => c.trim()).filter(Boolean)
}

function parseSlots(dimensionsStr: string): number | null {
  if (!dimensionsStr) return null
  const match = dimensionsStr.match(/(\d+)\s*slot/i)
  return match ? parseInt(match[1]) : null
}

function parseDisplayPorts(portsStr: string): any {
  if (!portsStr) return null
  return { raw: portsStr } // Can be enhanced to parse specific port types
}
