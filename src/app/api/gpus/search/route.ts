import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/gpus/search - Advanced GPU search with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const query = searchParams.get('q') || ''
    const manufacturers = searchParams.get('manufacturers')?.split(',') || []
    const architectures = searchParams.get('architectures')?.split(',') || []
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    const minMemory = searchParams.get('minMemory')
    const maxMemory = searchParams.get('maxMemory')
    const workload = searchParams.get('workload') // gaming, content-creation, ai-ml, mining
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50)

    // Build where clause
    const where: any = {}
    
    // Text search
    if (query) {
      where.OR = [
        { name: { contains: query, mode: 'insensitive' } },
        { manufacturer: { contains: query, mode: 'insensitive' } },
        { architecture: { contains: query, mode: 'insensitive' } },
      ]
    }

    // Manufacturer filter
    if (manufacturers.length > 0) {
      where.manufacturer = { in: manufacturers }
    }

    // Architecture filter
    if (architectures.length > 0) {
      where.architecture = { in: architectures }
    }

    // Memory size filter (stored in specifications.memory.size)
    if (minMemory || maxMemory) {
      where.specifications = {
        path: ['memory', 'size'],
        ...(minMemory && { gte: parseInt(minMemory) }),
        ...(maxMemory && { lte: parseInt(maxMemory) }),
      }
    }

    // Execute search
    const gpus = await prisma.gPU.findMany({
      where,
      take: limit,
      include: {
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 1,
          distinct: ['retailer'],
        },
        reviews: {
          select: { rating: true },
        },
        _count: {
          select: { reviews: true },
        },
      },
      orderBy: [
        { name: 'asc' },
      ],
    })

    // Filter by price range if specified (post-query filtering)
    let filteredGPUs = gpus
    if (minPrice || maxPrice) {
      filteredGPUs = gpus.filter(gpu => {
        if (gpu.pricing.length === 0) return false
        const lowestPrice = Math.min(...gpu.pricing.map(p => Number(p.price)))
        
        if (minPrice && lowestPrice < parseInt(minPrice)) return false
        if (maxPrice && lowestPrice > parseInt(maxPrice)) return false
        
        return true
      })
    }

    // Apply workload-specific scoring/filtering
    if (workload) {
      filteredGPUs = filteredGPUs.map(gpu => {
        let workloadScore = 0
        const specs = gpu.specifications as any

        switch (workload) {
          case 'gaming':
            // Prioritize high memory bandwidth, RT cores, and modern architecture
            workloadScore += specs.memory?.bandwidth || 0
            workloadScore += (specs.cores?.rtCores || 0) * 10
            break
          
          case 'content-creation':
            // Prioritize VRAM size and encoding capabilities
            workloadScore += (specs.memory?.size || 0) * 100
            workloadScore += specs.cores?.cudaCores || specs.cores?.streamProcessors || 0
            break
          
          case 'ai-ml':
            // Prioritize tensor cores and memory bandwidth
            workloadScore += (specs.cores?.tensorCores || 0) * 50
            workloadScore += specs.memory?.bandwidth || 0
            break
          
          case 'mining':
            // Prioritize efficiency and hash rate potential
            workloadScore += specs.cores?.cudaCores || specs.cores?.streamProcessors || 0
            workloadScore -= gpu.powerRequirements?.tdp || 0 // Lower power is better
            break
        }

        return { ...gpu, workloadScore }
      }).sort((a, b) => (b as any).workloadScore - (a as any).workloadScore)
    }

    // Format response
    const formattedGPUs = filteredGPUs.map(gpu => {
      const avgRating = gpu.reviews.length > 0 
        ? gpu.reviews.reduce((sum, review) => sum + review.rating, 0) / gpu.reviews.length
        : 0

      const lowestPrice = gpu.pricing.length > 0
        ? Math.min(...gpu.pricing.map(p => Number(p.price)))
        : null

      return {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
        architecture: gpu.architecture,
        launchDate: gpu.launchDate,
        originalMsrp: gpu.originalMsrp ? Number(gpu.originalMsrp) : null,
        specifications: gpu.specifications,
        currentPrice: lowestPrice,
        averageRating: Math.round(avgRating * 10) / 10,
        reviewCount: gpu._count.reviews,
        ...(workload && { workloadScore: (gpu as any).workloadScore }),
      }
    })

    return NextResponse.json({
      gpus: formattedGPUs,
      total: formattedGPUs.length,
      query: {
        q: query,
        manufacturers,
        architectures,
        minPrice,
        maxPrice,
        minMemory,
        maxMemory,
        workload,
      },
    })
  } catch (error) {
    console.error('Error searching GPUs:', error)
    return NextResponse.json(
      { error: 'Failed to search GPUs' },
      { status: 500 }
    )
  }
}