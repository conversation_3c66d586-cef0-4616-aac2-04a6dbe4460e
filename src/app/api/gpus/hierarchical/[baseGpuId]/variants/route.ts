import { NextRequest, NextResponse } from 'next/server'
import { GPUVariantService } from '@/lib/gpu-variant-service'

// GET /api/gpus/hierarchical/[baseGpuId]/variants - Get all variants for a base GPU
export async function GET(
  request: NextRequest,
  { params }: { params: { baseGpuId: string } }
) {
  try {
    const { baseGpuId } = params
    
    // Get the base GPU with all its variants
    const baseGpu = await GPUVariantService.getBaseGPUWithVariants(baseGpuId)
    
    if (!baseGpu) {
      return NextResponse.json(
        { error: 'Base GPU not found' },
        { status: 404 }
      )
    }
    
    // Get just the variants for easier consumption
    const variants = await GPUVariantService.getVariantsForBaseGPU(baseGpuId)
    
    return NextResponse.json({
      success: true,
      data: {
        baseGpu: {
          id: baseGpu.id,
          name: baseGpu.name,
          chipManufacturer: baseGpu.chipManufacturer,
          architecture: baseGpu.architecture,
          specifications: baseGpu.specifications
        },
        variants: variants,
        variantCount: variants.length
      },
      message: `Found ${variants.length} variants for ${baseGpu.name}`
    })
    
  } catch (error) {
    console.error('Error fetching GPU variants:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch GPU variants',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
