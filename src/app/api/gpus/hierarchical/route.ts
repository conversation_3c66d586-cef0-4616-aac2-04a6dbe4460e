import { NextRequest, NextResponse } from 'next/server'
import { GPUVariantService } from '@/lib/gpu-variant-service'

// GET /api/gpus/hierarchical - Get all base GPUs with their variants
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const chipManufacturer = searchParams.get('chipManufacturer')
    const boardPartner = searchParams.get('boardPartner')
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    
    if (search) {
      // Search mode
      const filters: any = {}
      
      if (chipManufacturer) filters.chipManufacturer = chipManufacturer
      if (boardPartner) filters.boardPartner = boardPartner
      if (minPrice && maxPrice) {
        filters.priceRange = { 
          min: parseInt(minPrice), 
          max: parseInt(maxPrice) 
        }
      }
      
      const results = await GPUVariantService.searchGPUs(search, filters)
      
      return NextResponse.json({
        success: true,
        data: results,
        message: `Found ${results.baseGpus.length} base GPUs and ${results.variants.length} variants`
      })
    } else {
      // List all mode
      const baseGpus = await GPUVariantService.getAllBaseGPUsWithVariants()
      
      return NextResponse.json({
        success: true,
        data: { baseGpus, variants: [] },
        total: baseGpus.length,
        message: `Retrieved ${baseGpus.length} base GPUs with variants`
      })
    }
    
  } catch (error) {
    console.error('Error fetching hierarchical GPU data:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch GPU data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/gpus/hierarchical - Create new base GPU or variant
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, data } = body
    
    if (type === 'baseGpu') {
      const baseGpu = await GPUVariantService.createBaseGPU(data)
      return NextResponse.json({
        success: true,
        data: baseGpu,
        message: 'Base GPU created successfully'
      }, { status: 201 })
      
    } else if (type === 'variant') {
      const variant = await GPUVariantService.createGPUVariant(data)
      return NextResponse.json({
        success: true,
        data: variant,
        message: 'GPU variant created successfully'
      }, { status: 201 })
      
    } else {
      return NextResponse.json(
        { error: 'Invalid type. Must be "baseGpu" or "variant"' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    console.error('Error creating GPU data:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create GPU data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
