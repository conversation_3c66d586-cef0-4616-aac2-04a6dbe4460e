import { NextRequest, NextResponse } from 'next/server'
import { GPUService } from '@/lib/gpu-service'

// POST /api/gpus/import - Import GPU data from Google Sheets
export async function POST(request: NextRequest) {
  try {
    // Check if this is a development environment or if explicitly allowed
    const { force } = await request.json().catch(() => ({ force: false }))
    
    if (process.env.NODE_ENV === 'production' && !force) {
      return NextResponse.json(
        { error: 'Import is not allowed in production without force flag' },
        { status: 403 }
      )
    }

    console.log('Starting GPU data import from Google Sheets...')
    
    // Import data using the existing GPUService method
    const result = await GPUService.seedGPUData()
    
    return NextResponse.json({
      message: 'GPU data import completed successfully',
      result: {
        total: result.total,
        inserted: result.inserted,
        updated: result.updated,
        errors: result.errors
      }
    }, { status: 200 })
    
  } catch (error) {
    console.error('Error importing GPU data:', error)
    return NextResponse.json(
      { 
        error: 'Failed to import GPU data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET /api/gpus/import - Get import status and preview data
export async function GET() {
  try {
    const apiUrl = process.env.GPU_DATA_API_URL
    if (!apiUrl) {
      return NextResponse.json(
        { error: 'GPU_DATA_API_URL not configured' },
        { status: 500 }
      )
    }

    // Fetch a small sample of data to show what's available
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.statusText}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      status: 'Google Sheets API accessible',
      apiUrl: apiUrl,
      dataPreview: {
        success: data.success,
        totalRecords: data.data ? data.data.length : 0,
        sampleRecords: data.data ? data.data.slice(0, 3) : [],
        brands: data.data ? [...new Set(data.data.map((gpu: any) => gpu.brand).filter(Boolean))] : []
      }
    })
    
  } catch (error) {
    console.error('Error checking import status:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check import status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
