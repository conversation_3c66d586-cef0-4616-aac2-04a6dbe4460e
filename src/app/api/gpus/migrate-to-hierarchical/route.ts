import { NextRequest, NextResponse } from 'next/server'
import { GPUVariantService } from '@/lib/gpu-variant-service'
import { prisma } from '@/lib/prisma'

// POST /api/gpus/migrate-to-hierarchical - Migrate existing GPUs to hierarchical structure
export async function POST(request: NextRequest) {
  try {
    const { limit = 10 } = await request.json().catch(() => ({}))
    
    // Get unmigrated GPUs
    const legacyGpus = await prisma.gPU.findMany({
      where: { migrated: false },
      take: limit,
      orderBy: { launchDate: 'desc' }
    })
    
    if (legacyGpus.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No GPUs to migrate',
        migrated: 0
      })
    }
    
    const results = []
    let successCount = 0
    let errorCount = 0
    
    for (const gpu of legacyGpus) {
      try {
        const result = await GPUVariantService.migrateGPUToHierarchical(gpu.id)
        results.push({
          legacyId: gpu.id,
          legacyName: gpu.name,
          baseGpuId: result.baseGpu.id,
          variantId: result.variant.id,
          status: 'success'
        })
        successCount++
      } catch (error) {
        results.push({
          legacyId: gpu.id,
          legacyName: gpu.name,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        errorCount++
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Migration completed: ${successCount} successful, ${errorCount} errors`,
      results,
      stats: {
        processed: legacyGpus.length,
        successful: successCount,
        errors: errorCount
      }
    })
    
  } catch (error) {
    console.error('Error migrating GPUs:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to migrate GPUs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET /api/gpus/migrate-to-hierarchical - Get migration status
export async function GET() {
  try {
    const [totalGpus, migratedGpus, baseGpus, variants] = await Promise.all([
      prisma.gPU.count(),
      prisma.gPU.count({ where: { migrated: true } }),
      prisma.baseGPU.count(),
      prisma.gPUVariant.count()
    ])
    
    return NextResponse.json({
      success: true,
      stats: {
        totalLegacyGpus: totalGpus,
        migratedGpus: migratedGpus,
        unmigrated: totalGpus - migratedGpus,
        baseGpus: baseGpus,
        variants: variants
      },
      migrationProgress: totalGpus > 0 ? (migratedGpus / totalGpus * 100).toFixed(1) + '%' : '0%'
    })
    
  } catch (error) {
    console.error('Error getting migration status:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get migration status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
