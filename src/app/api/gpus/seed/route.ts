import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Sample GPU data for seeding - in production this would come from external sources
const sampleGPUData = [
  {
    name: 'GeForce RTX 4090',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: new Date('2022-10-12'),
    originalMsrp: 1599.00,
    specifications: {
      memory: {
        size: 24,
        type: 'GDDR6X',
        bandwidth: 1008,
        busWidth: 384
      },
      cores: {
        cudaCores: 16384,
        rtCores: 128,
        tensorCores: 512
      },
      clocks: {
        baseClock: 2230,
        boostClock: 2520,
        memoryClock: 21000
      },
      process: '4nm TSMC',
      transistors: 76.3,
      dieSize: 608
    },
    physicalSpecs: {
      dimensions: {
        length: 304,
        width: 137,
        height: 61
      },
      weight: 2200,
      slots: 3,
      cooling: {
        type: 'Air',
        fans: 3,
        heatpipes: 7
      },
      ports: {
        displayPort: 3,
        hdmi: 1
      }
    },
    powerRequirements: {
      tdp: 450,
      recommendedPSU: 850,
      connectors: ['12VHPWR'],
      peakPower: 600,
      idlePower: 25
    }
  },
  {
    name: 'Radeon RX 7900 XTX',
    manufacturer: 'AMD',
    architecture: 'RDNA 3',
    launchDate: new Date('2022-12-13'),
    originalMsrp: 999.00,
    specifications: {
      memory: {
        size: 24,
        type: 'GDDR6',
        bandwidth: 960,
        busWidth: 384
      },
      cores: {
        streamProcessors: 6144,
        rtCores: 96,
        aiAccelerators: 192
      },
      clocks: {
        baseClock: 1855,
        boostClock: 2500,
        memoryClock: 20000
      },
      process: '5nm TSMC',
      transistors: 58.0,
      dieSize: 533
    },
    physicalSpecs: {
      dimensions: {
        length: 287,
        width: 120,
        height: 51
      },
      weight: 1800,
      slots: 2.5,
      cooling: {
        type: 'Air',
        fans: 3,
        heatpipes: 6
      },
      ports: {
        displayPort: 2,
        hdmi: 1,
        usbc: 1
      }
    },
    powerRequirements: {
      tdp: 355,
      recommendedPSU: 750,
      connectors: ['8-pin', '8-pin'],
      peakPower: 500,
      idlePower: 20
    }
  },
  {
    name: 'GeForce RTX 4070 Ti',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: new Date('2023-01-05'),
    originalMsrp: 799.00,
    specifications: {
      memory: {
        size: 12,
        type: 'GDDR6X',
        bandwidth: 504,
        busWidth: 192
      },
      cores: {
        cudaCores: 7680,
        rtCores: 60,
        tensorCores: 240
      },
      clocks: {
        baseClock: 2310,
        boostClock: 2610,
        memoryClock: 21000
      },
      process: '4nm TSMC',
      transistors: 35.8,
      dieSize: 295
    },
    physicalSpecs: {
      dimensions: {
        length: 267,
        width: 111,
        height: 40
      },
      weight: 1200,
      slots: 2,
      cooling: {
        type: 'Air',
        fans: 2,
        heatpipes: 4
      },
      ports: {
        displayPort: 3,
        hdmi: 1
      }
    },
    powerRequirements: {
      tdp: 285,
      recommendedPSU: 700,
      connectors: ['12VHPWR'],
      peakPower: 400,
      idlePower: 15
    }
  },
  {
    name: 'Radeon RX 7800 XT',
    manufacturer: 'AMD',
    architecture: 'RDNA 3',
    launchDate: new Date('2023-09-06'),
    originalMsrp: 499.00,
    specifications: {
      memory: {
        size: 16,
        type: 'GDDR6',
        bandwidth: 624,
        busWidth: 256
      },
      cores: {
        streamProcessors: 3840,
        rtCores: 60,
        aiAccelerators: 120
      },
      clocks: {
        baseClock: 1295,
        boostClock: 2430,
        memoryClock: 19500
      },
      process: '5nm TSMC',
      transistors: 28.1,
      dieSize: 346
    },
    physicalSpecs: {
      dimensions: {
        length: 267,
        width: 111,
        height: 40
      },
      weight: 1100,
      slots: 2,
      cooling: {
        type: 'Air',
        fans: 2,
        heatpipes: 4
      },
      ports: {
        displayPort: 2,
        hdmi: 1,
        usbc: 1
      }
    },
    powerRequirements: {
      tdp: 263,
      recommendedPSU: 700,
      connectors: ['8-pin', '6-pin'],
      peakPower: 350,
      idlePower: 18
    }
  },
  {
    name: 'GeForce RTX 4060 Ti',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: new Date('2023-05-24'),
    originalMsrp: 399.00,
    specifications: {
      memory: {
        size: 8,
        type: 'GDDR6',
        bandwidth: 288,
        busWidth: 128
      },
      cores: {
        cudaCores: 4352,
        rtCores: 34,
        tensorCores: 136
      },
      clocks: {
        baseClock: 2310,
        boostClock: 2535,
        memoryClock: 18000
      },
      process: '4nm TSMC',
      transistors: 22.9,
      dieSize: 188
    },
    physicalSpecs: {
      dimensions: {
        length: 244,
        width: 111,
        height: 40
      },
      weight: 900,
      slots: 2,
      cooling: {
        type: 'Air',
        fans: 2,
        heatpipes: 3
      },
      ports: {
        displayPort: 3,
        hdmi: 1
      }
    },
    powerRequirements: {
      tdp: 165,
      recommendedPSU: 550,
      connectors: ['8-pin'],
      peakPower: 220,
      idlePower: 12
    }
  }
]

// POST /api/gpus/seed - Seed the database with GPU data
export async function POST(request: NextRequest) {
  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Seeding is not allowed in production' },
        { status: 403 }
      )
    }

    const { force } = await request.json().catch(() => ({ force: false }))

    // Check if GPUs already exist
    const existingGPUCount = await prisma.gPU.count()
    
    if (existingGPUCount > 0 && !force) {
      return NextResponse.json(
        { 
          message: 'Database already contains GPU data. Use force=true to reseed.',
          existingCount: existingGPUCount 
        },
        { status: 200 }
      )
    }

    // Clear existing data if force is true
    if (force) {
      await prisma.gPU.deleteMany()
    }

    // Seed the database
    const createdGPUs = []
    
    for (const gpuData of sampleGPUData) {
      const gpu = await prisma.gPU.create({
        data: {
          name: gpuData.name,
          manufacturer: gpuData.manufacturer,
          architecture: gpuData.architecture,
          launchDate: gpuData.launchDate,
          originalMsrp: gpuData.originalMsrp,
          specifications: gpuData.specifications,
          physicalSpecs: gpuData.physicalSpecs,
          powerRequirements: gpuData.powerRequirements
        }
      })
      createdGPUs.push(gpu)
    }

    // Also seed some sample pricing data
    for (const gpu of createdGPUs) {
      // Create sample pricing data for each GPU
      const retailers = ['Newegg', 'Amazon', 'Best Buy', 'Micro Center', 'B&H']
      const basePrice = gpu.originalMsrp ? Number(gpu.originalMsrp) : 500
      
      for (const retailer of retailers) {
        // Create some price variation
        const priceVariation = (Math.random() - 0.5) * 0.2 // ±10% variation
        const price = basePrice * (1 + priceVariation)
        const availability = Math.random() > 0.3 ? 'in-stock' : Math.random() > 0.5 ? 'limited' : 'out-of-stock'
        
        await prisma.gPUPricing.create({
          data: {
            gpuId: gpu.id,
            retailer: retailer,
            price: Math.round(price * 100) / 100, // Round to 2 decimal places
            availability: availability,
            url: `https://${retailer.toLowerCase().replace(' ', '')}.com/gpu/${gpu.id}`,
            scrapedAt: new Date()
          }
        })
      }
    }

    return NextResponse.json(
      { 
        message: 'Database seeded successfully',
        gpusCreated: createdGPUs.length,
        pricingEntriesCreated: createdGPUs.length * 5
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error seeding database:', error)
    return NextResponse.json(
      { error: 'Failed to seed database' },
      { status: 500 }
    )
  }
}

// GET /api/gpus/seed - Get seeding status
export async function GET() {
  try {
    const gpuCount = await prisma.gPU.count()
    const pricingCount = await prisma.gPUPricing.count()
    const userCount = await prisma.user.count()
    const reviewCount = await prisma.userReview.count()

    return NextResponse.json({
      status: 'Database status',
      counts: {
        gpus: gpuCount,
        pricing: pricingCount,
        users: userCount,
        reviews: reviewCount
      },
      isSeeded: gpuCount > 0
    })
  } catch (error) {
    console.error('Error checking database status:', error)
    return NextResponse.json(
      { error: 'Failed to check database status' },
      { status: 500 }
    )
  }
}