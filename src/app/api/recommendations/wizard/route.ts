import { NextRequest, NextResponse } from 'next/server';
import { recommendationEngine, RecommendationCriteria } from '@/lib/recommendation-engine';

// POST /api/recommendations/wizard - Multi-step recommendation wizard
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { step, data } = body;

    switch (step) {
      case 'workload':
        return handleWorkloadStep(data);
      case 'budget':
        return handleBudgetStep(data);
      case 'performance':
        return handlePerformanceStep(data);
      case 'system':
        return handleSystemStep(data);
      case 'preferences':
        return handlePreferencesStep(data);
      case 'generate':
        return handleGenerateRecommendations(data);
      default:
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'VALID_001',
              message: 'Invalid step. Must be one of: workload, budget, performance, system, preferences, generate',
              timestamp: new Date().toISOString()
            }
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in recommendation wizard:', error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'REC_001',
          message: 'Failed to process wizard step',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}

// Handle workload selection step
async function handleWorkloadStep(data: any) {
  const { workload } = data;
  
  if (!workload || !['gaming', 'content-creation', 'ai-ml', 'mining'].includes(workload)) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'VALID_001',
          message: 'Invalid workload. Must be one of: gaming, content-creation, ai-ml, mining',
          timestamp: new Date().toISOString()
        }
      },
      { status: 400 }
    );
  }

  // Return workload-specific guidance and next step configuration
  const workloadConfig = getWorkloadConfiguration(workload);
  
  return NextResponse.json({
    success: true,
    data: {
      workload,
      configuration: workloadConfig,
      nextStep: 'budget',
      progress: 20
    }
  });
}

// Handle budget selection step
async function handleBudgetStep(data: any) {
  const { workload, budget } = data;
  
  if (!budget || !budget.min || !budget.max || budget.min >= budget.max) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'VALID_001',
          message: 'Invalid budget. Must have min and max values with min < max',
          timestamp: new Date().toISOString()
        }
      },
      { status: 400 }
    );
  }

  // Get budget-appropriate suggestions
  const budgetGuidance = getBudgetGuidance(workload, budget);
  
  return NextResponse.json({
    success: true,
    data: {
      budget,
      guidance: budgetGuidance,
      nextStep: 'performance',
      progress: 40
    }
  });
}

// Handle performance targets step
async function handlePerformanceStep(data: any) {
  const { workload, performanceTargets } = data;
  
  // Validate performance targets based on workload
  const validation = validatePerformanceTargets(workload, performanceTargets);
  
  if (!validation.valid) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'VALID_001',
          message: validation.message,
          timestamp: new Date().toISOString()
        }
      },
      { status: 400 }
    );
  }

  return NextResponse.json({
    success: true,
    data: {
      performanceTargets,
      nextStep: 'system',
      progress: 60
    }
  });
}

// Handle current system step
async function handleSystemStep(data: any) {
  const { currentSystem } = data;
  
  // Validate system specifications
  const systemValidation = validateSystemSpecs(currentSystem);
  
  return NextResponse.json({
    success: true,
    data: {
      currentSystem,
      validation: systemValidation,
      nextStep: 'preferences',
      progress: 80
    }
  });
}

// Handle preferences step
async function handlePreferencesStep(data: any) {
  const { preferences, priorities } = data;
  
  // Validate and normalize priorities
  const normalizedPriorities = normalizePriorities(priorities);
  
  return NextResponse.json({
    success: true,
    data: {
      preferences,
      priorities: normalizedPriorities,
      nextStep: 'generate',
      progress: 100
    }
  });
}

// Generate final recommendations
async function handleGenerateRecommendations(data: any) {
  const criteria: RecommendationCriteria = {
    workload: data.workload,
    budget: data.budget,
    performanceTargets: data.performanceTargets,
    currentSystem: data.currentSystem,
    futureProofing: data.futureProofing || 3,
    priorities: data.priorities,
    preferences: data.preferences
  };

  const recommendations = await recommendationEngine.getRecommendations(criteria);
  
  // Add wizard-specific enhancements
  const enhancedRecommendations = {
    ...recommendations,
    wizardSummary: generateWizardSummary(criteria),
    alternativeOptions: generateAlternativeOptions(recommendations),
    budgetAnalysis: generateBudgetAnalysis(recommendations, criteria.budget)
  };
  
  return NextResponse.json({
    success: true,
    data: enhancedRecommendations
  });
}

// Helper functions
function getWorkloadConfiguration(workload: string) {
  const configs = {
    gaming: {
      title: 'Gaming Performance',
      description: 'Optimized for high frame rates and visual quality',
      keyFactors: ['Frame Rate', 'Resolution Support', 'Ray Tracing', 'DLSS/FSR'],
      budgetRanges: {
        budget: { min: 200, max: 500, description: 'Entry-level gaming at 1080p' },
        midRange: { min: 500, max: 800, description: 'High-quality 1440p gaming' },
        highEnd: { min: 800, max: 1500, description: '4K gaming with ray tracing' },
        enthusiast: { min: 1500, max: 3000, description: 'Ultimate 4K performance' }
      }
    },
    'content-creation': {
      title: 'Content Creation',
      description: 'Optimized for video editing, 3D rendering, and streaming',
      keyFactors: ['VRAM Capacity', 'CUDA Cores', 'Hardware Encoding', 'Memory Bandwidth'],
      budgetRanges: {
        budget: { min: 400, max: 800, description: 'Basic content creation' },
        midRange: { min: 800, max: 1500, description: '4K video editing' },
        highEnd: { min: 1500, max: 2500, description: 'Professional workflows' },
        enthusiast: { min: 2500, max: 5000, description: '8K and complex rendering' }
      }
    },
    'ai-ml': {
      title: 'AI/ML Development',
      description: 'Optimized for machine learning training and inference',
      keyFactors: ['Tensor Cores', 'VRAM Capacity', 'Memory Bandwidth', 'CUDA Support'],
      budgetRanges: {
        budget: { min: 600, max: 1200, description: 'Learning and small models' },
        midRange: { min: 1200, max: 2000, description: 'Medium model training' },
        highEnd: { min: 2000, max: 4000, description: 'Large model development' },
        enthusiast: { min: 4000, max: 8000, description: 'Research and enterprise' }
      }
    },
    mining: {
      title: 'Cryptocurrency Mining',
      description: 'Optimized for mining efficiency and ROI',
      keyFactors: ['Hash Rate', 'Power Efficiency', 'Memory Type', 'ROI Calculation'],
      budgetRanges: {
        budget: { min: 200, max: 600, description: 'Entry-level mining' },
        midRange: { min: 600, max: 1000, description: 'Efficient mining setup' },
        highEnd: { min: 1000, max: 2000, description: 'High-performance mining' },
        enthusiast: { min: 2000, max: 4000, description: 'Mining farm grade' }
      }
    }
  };
  
  return configs[workload] || configs.gaming;
}

function getBudgetGuidance(workload: string, budget: { min: number; max: number }) {
  const midpoint = (budget.min + budget.max) / 2;
  const config = getWorkloadConfiguration(workload);
  
  let category = 'budget';
  if (midpoint > 1500) category = 'enthusiast';
  else if (midpoint > 800) category = 'highEnd';
  else if (midpoint > 500) category = 'midRange';
  
  return {
    category,
    description: config.budgetRanges[category]?.description || 'Custom budget range',
    expectations: generateBudgetExpectations(workload, midpoint),
    recommendations: generateBudgetRecommendations(workload, budget)
  };
}

function generateBudgetExpectations(workload: string, budget: number) {
  const expectations = {
    gaming: {
      low: 'Solid 1080p gaming at high settings',
      mid: 'Excellent 1440p gaming with ray tracing',
      high: 'Premium 4K gaming experience',
      ultra: 'Ultimate 4K performance with all features'
    },
    'content-creation': {
      low: 'Basic video editing and streaming',
      mid: '4K video editing and 3D rendering',
      high: 'Professional content creation workflows',
      ultra: '8K editing and complex simulations'
    },
    'ai-ml': {
      low: 'Small model training and inference',
      mid: 'Medium-scale model development',
      high: 'Large model training and research',
      ultra: 'Enterprise-grade AI development'
    },
    mining: {
      low: 'Entry-level mining profitability',
      mid: 'Good mining efficiency and ROI',
      high: 'High-performance mining setup',
      ultra: 'Professional mining operation'
    }
  };
  
  let level = 'low';
  if (budget > 2000) level = 'ultra';
  else if (budget > 1000) level = 'high';
  else if (budget > 600) level = 'mid';
  
  return expectations[workload]?.[level] || 'Performance appropriate for budget';
}

function generateBudgetRecommendations(workload: string, budget: { min: number; max: number }) {
  const recommendations = [];
  const midpoint = (budget.min + budget.max) / 2;
  
  if (midpoint < 400) {
    recommendations.push('Consider used or previous-generation GPUs for better value');
  }
  
  if (workload === 'gaming' && midpoint > 1000) {
    recommendations.push('Look for GPUs with DLSS or FSR support for future-proofing');
  }
  
  if (workload === 'ai-ml' && midpoint < 1000) {
    recommendations.push('Prioritize VRAM capacity over raw performance for AI workloads');
  }
  
  return recommendations;
}

function validatePerformanceTargets(workload: string, targets: any) {
  if (!targets) return { valid: true };
  
  // Workload-specific validation
  switch (workload) {
    case 'gaming':
      if (targets.resolution === '8K' && targets.targetFPS > 60) {
        return { valid: false, message: '8K gaming above 60 FPS is not realistic with current hardware' };
      }
      break;
    case 'ai-ml':
      // AI/ML doesn't use gaming performance targets
      if (targets.resolution || targets.targetFPS) {
        return { valid: false, message: 'Resolution and FPS targets are not applicable for AI/ML workloads' };
      }
      break;
  }
  
  return { valid: true };
}

function validateSystemSpecs(system: any) {
  const warnings = [];
  const recommendations = [];
  
  if (system?.psu && system.psu < 500) {
    warnings.push('Low PSU wattage may limit GPU options');
    recommendations.push('Consider upgrading PSU for high-end GPUs');
  }
  
  if (system?.cpu && system.cpu.includes('i3') || system.cpu?.includes('Ryzen 3')) {
    warnings.push('Entry-level CPU may bottleneck high-end GPUs');
  }
  
  return { warnings, recommendations };
}

function normalizePriorities(priorities: any) {
  if (!priorities) {
    return { performance: 40, value: 30, futureProofing: 20, powerEfficiency: 10 };
  }
  
  const total = Object.values(priorities).reduce((sum: number, val: any) => sum + (val || 0), 0);
  
  if (total === 0) {
    return { performance: 40, value: 30, futureProofing: 20, powerEfficiency: 10 };
  }
  
  return {
    performance: Math.round((priorities.performance || 0) / total * 100),
    value: Math.round((priorities.value || 0) / total * 100),
    futureProofing: Math.round((priorities.futureProofing || 0) / total * 100),
    powerEfficiency: Math.round((priorities.powerEfficiency || 0) / total * 100)
  };
}

function generateWizardSummary(criteria: RecommendationCriteria) {
  return {
    workload: criteria.workload,
    budgetRange: `$${criteria.budget.min} - $${criteria.budget.max}`,
    keyPriorities: Object.entries(criteria.priorities || {})
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 2)
      .map(([key]) => key),
    futureProofing: `${criteria.futureProofing || 3} years`,
    specialRequirements: generateSpecialRequirements(criteria)
  };
}

function generateSpecialRequirements(criteria: RecommendationCriteria) {
  const requirements = [];
  
  if (criteria.performanceTargets?.resolution === '4K') {
    requirements.push('4K gaming capability');
  }
  
  if (criteria.performanceTargets?.rayTracing) {
    requirements.push('Ray tracing support');
  }
  
  if (criteria.preferences?.quietOperation) {
    requirements.push('Quiet operation');
  }
  
  if (criteria.preferences?.maxPowerDraw) {
    requirements.push(`Max ${criteria.preferences.maxPowerDraw}W power draw`);
  }
  
  return requirements;
}

function generateAlternativeOptions(recommendations: any) {
  return {
    budgetAlternatives: 'Consider previous generation GPUs for better value',
    performanceAlternatives: 'Look at higher-tier models for future-proofing',
    brandAlternatives: 'Compare equivalent models from different manufacturers'
  };
}

function generateBudgetAnalysis(recommendations: any, budget: { min: number; max: number }) {
  const prices = recommendations.recommendations
    .map((rec: any) => rec.bestPrice.price)
    .filter((price: number) => price > 0);
  
  if (prices.length === 0) return null;
  
  const avgPrice = prices.reduce((sum: number, price: number) => sum + price, 0) / prices.length;
  const budgetMidpoint = (budget.min + budget.max) / 2;
  
  return {
    averageRecommendedPrice: Math.round(avgPrice),
    budgetUtilization: Math.round((avgPrice / budgetMidpoint) * 100),
    savingsOpportunity: budgetMidpoint > avgPrice ? Math.round(budgetMidpoint - avgPrice) : 0,
    upgradeOpportunity: avgPrice < budget.max ? Math.round(budget.max - avgPrice) : 0
  };
}