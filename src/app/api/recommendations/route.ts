import { NextRequest, NextResponse } from 'next/server';
import { recommendationEngine, RecommendationCriteria } from '@/lib/recommendation-engine';

// POST /api/recommendations - Get GPU recommendations based on criteria
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const criteria: RecommendationCriteria = body;

    // Validate required fields
    if (!criteria.workload || !criteria.budget) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'workload and budget are required',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    // Validate workload type
    const validWorkloads = ['gaming', 'content-creation', 'ai-ml', 'mining'];
    if (!validWorkloads.includes(criteria.workload)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'workload must be one of: gaming, content-creation, ai-ml, mining',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    // Validate budget
    if (!criteria.budget.min || !criteria.budget.max || criteria.budget.min >= criteria.budget.max) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALID_001',
            message: 'budget must have valid min and max values (min < max)',
            timestamp: new Date().toISOString()
          }
        },
        { status: 400 }
      );
    }

    // Generate recommendations
    const recommendations = await recommendationEngine.getRecommendations(criteria);

    return NextResponse.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    console.error('Error generating recommendations:', error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'REC_001',
          message: 'Failed to generate recommendations',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}