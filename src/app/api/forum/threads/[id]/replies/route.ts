import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// POST /api/forum/threads/[id]/replies - Create new reply
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { content } = body

    if (!content) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      )
    }

    // Check if thread exists and is not locked
    const thread = await prisma.forumThread.findUnique({
      where: { id: params.id }
    })

    if (!thread) {
      return NextResponse.json(
        { error: 'Thread not found' },
        { status: 404 }
      )
    }

    if (thread.isLocked) {
      return NextResponse.json(
        { error: 'Thread is locked' },
        { status: 403 }
      )
    }

    // Create reply and update thread stats in a transaction
    const result = await prisma.$transaction(async (tx) => {
      const reply = await tx.forumReply.create({
        data: {
          threadId: params.id,
          authorId: session.user.id,
          content
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              image: true,
              reputation: {
                select: {
                  totalScore: true,
                  badges: true
                }
              }
            }
          }
        }
      })

      // Update thread reply count and last reply info
      await tx.forumThread.update({
        where: { id: params.id },
        data: {
          replyCount: { increment: 1 },
          lastReplyAt: new Date(),
          lastReplyBy: session.user.id
        }
      })

      return reply
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating reply:', error)
    return NextResponse.json(
      { error: 'Failed to create reply' },
      { status: 500 }
    )
  }
}