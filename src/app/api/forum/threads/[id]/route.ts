import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/forum/threads/[id] - Get thread with replies
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Increment view count
    await prisma.forumThread.update({
      where: { id: params.id },
      data: { viewCount: { increment: 1 } }
    })

    const [thread, replies, replyCount] = await Promise.all([
      prisma.forumThread.findUnique({
        where: { id: params.id },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              image: true,
              reputation: {
                select: {
                  totalScore: true,
                  badges: true
                }
              }
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
              color: true
            }
          }
        }
      }),
      prisma.forumReply.findMany({
        where: { 
          threadId: params.id,
          isHidden: false
        },
        orderBy: { createdAt: 'asc' },
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              username: true,
              image: true,
              reputation: {
                select: {
                  totalScore: true,
                  badges: true
                }
              }
            }
          }
        }
      }),
      prisma.forumReply.count({
        where: { 
          threadId: params.id,
          isHidden: false
        }
      })
    ])

    if (!thread) {
      return NextResponse.json(
        { error: 'Thread not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      thread,
      replies,
      pagination: {
        page,
        limit,
        total: replyCount,
        pages: Math.ceil(replyCount / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching thread:', error)
    return NextResponse.json(
      { error: 'Failed to fetch thread' },
      { status: 500 }
    )
  }
}

// PUT /api/forum/threads/[id] - Update thread (author or moderator only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const thread = await prisma.forumThread.findUnique({
      where: { id: params.id },
      include: { category: true }
    })

    if (!thread) {
      return NextResponse.json(
        { error: 'Thread not found' },
        { status: 404 }
      )
    }

    // Check if user can edit (author or moderator)
    const canEdit = thread.authorId === session.user.id || 
                   thread.category.moderatorIds.includes(session.user.id)

    if (!canEdit) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { title, content, tags, isPinned, isLocked } = body

    const updatedThread = await prisma.forumThread.update({
      where: { id: params.id },
      data: {
        ...(title && { title }),
        ...(content && { content }),
        ...(tags && { tags }),
        ...(isPinned !== undefined && { isPinned }),
        ...(isLocked !== undefined && { isLocked })
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
            image: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        }
      }
    })

    return NextResponse.json(updatedThread)
  } catch (error) {
    console.error('Error updating thread:', error)
    return NextResponse.json(
      { error: 'Failed to update thread' },
      { status: 500 }
    )
  }
}