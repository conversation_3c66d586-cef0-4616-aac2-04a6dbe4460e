import CompatibilityChecker from '@/components/compatibility/CompatibilityChecker'
import { Layout } from '@/components/layout/Layout'

export default function CompatibilityPage() {
  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-primary mb-4">
              GPU Compatibility Checker
            </h1>
            <p className="text-foreground-muted text-lg max-w-2xl mx-auto">
              Verify if your chosen GPU is compatible with your existing system components. 
              Check power requirements, physical clearance, and potential bottlenecks before making your purchase.
            </p>
          </div>
          
          <CompatibilityChecker />
          
          <div className="mt-12 bg-card rounded-lg p-6 border border-border">
            <h2 className="text-xl font-bold text-primary mb-4">How It Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-foreground-muted">
              <div>
                <h3 className="font-semibold text-foreground mb-2">Power Analysis</h3>
                <p className="text-sm">
                  We check if your power supply can handle the GPU's power requirements, 
                  including wattage and connector compatibility.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2">Physical Fit</h3>
                <p className="text-sm">
                  Verify that the GPU will physically fit in your case by checking 
                  length, width, and height clearances.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2">Bottleneck Detection</h3>
                <p className="text-sm">
                  Analyze potential CPU bottlenecks that could limit your GPU's 
                  performance in different scenarios.
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-8 bg-card rounded-lg p-6 border border-border">
            <h2 className="text-xl font-bold text-primary mb-4">Tips for Accurate Results</h2>
            <ul className="space-y-2 text-foreground-muted text-sm">
              <li className="flex items-start space-x-2">
                <span className="text-primary mt-1">•</span>
                <span>Enter your exact component models for the most accurate compatibility analysis</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-primary mt-1">•</span>
                <span>Check your case manual for maximum GPU clearance specifications</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-primary mt-1">•</span>
                <span>Include all power connectors available on your PSU for accurate power analysis</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-primary mt-1">•</span>
                <span>Consider future upgrades when evaluating power supply requirements</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  )
}