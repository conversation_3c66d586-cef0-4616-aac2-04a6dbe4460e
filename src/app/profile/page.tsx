'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Layout } from '@/components/layout/Layout'
import { Button } from '@/components/ui/Button'

export default function ProfilePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: ''
  })

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
    
    if (session?.user) {
      setFormData({
        name: session.user.name || '',
        username: session.user.username || '',
        email: session.user.email || ''
      })
    }
  }, [session, status, router])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement profile update API
    console.log('Profile update:', formData)
    setIsEditing(false)
  }

  if (status === 'loading') {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <div className="bg-card rounded-lg p-6 border border-border">
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-background-secondary rounded w-1/3"></div>
                <div className="h-4 bg-background-secondary rounded w-2/3"></div>
                <div className="h-4 bg-background-secondary rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    )
  }

  if (!session) {
    return null
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-foreground">Profile</h1>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
              >
                {isEditing ? 'Cancel' : 'Edit Profile'}
              </Button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-black font-bold text-2xl">
                    {session.user?.name?.charAt(0) || session.user?.email?.charAt(0) || 'U'}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-foreground">
                    {session.user?.name || 'User'}
                  </h2>
                  <p className="text-foreground-muted">
                    Member since {new Date(session.user?.createdAt || Date.now()).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-foreground-muted mb-2">
                    Full Name
                  </label>
                  <input
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    disabled={!isEditing}
                    className="input w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                </div>

                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-foreground-muted mb-2">
                    Username
                  </label>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    value={formData.username}
                    onChange={handleChange}
                    disabled={!isEditing}
                    className="input w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-foreground-muted mb-2">
                    Email Address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    disabled={true} // Email should not be editable
                    className="input w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                  <p className="text-xs text-foreground-muted mt-1">
                    Email cannot be changed. Contact support if you need to update your email.
                  </p>
                </div>
              </div>

              {isEditing && (
                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    Save Changes
                  </Button>
                </div>
              )}
            </form>

            <div className="mt-8 pt-6 border-t border-border">
              <h3 className="text-lg font-semibold text-foreground mb-4">Account Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-background-secondary rounded-lg p-4">
                  <div className="text-2xl font-bold text-primary">0</div>
                  <div className="text-sm text-foreground-muted">Reviews Written</div>
                </div>
                <div 
                  className="bg-background-secondary rounded-lg p-4 cursor-pointer hover:bg-background-tertiary transition-colors"
                  onClick={() => router.push('/wishlist')}
                >
                  <div className="text-2xl font-bold text-primary">-</div>
                  <div className="text-sm text-foreground-muted">GPUs in Wishlist</div>
                </div>
                <div 
                  className="bg-background-secondary rounded-lg p-4 cursor-pointer hover:bg-background-tertiary transition-colors"
                  onClick={() => router.push('/alerts')}
                >
                  <div className="text-2xl font-bold text-primary">-</div>
                  <div className="text-sm text-foreground-muted">Price Alerts</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}