'use client'

import { useState, useEffect } from 'react'
import { Layout } from '@/components/layout/Layout'
import { ReviewDisplay } from '@/components/reviews/ReviewDisplay'
import { Button } from '@/components/ui/Button'

interface Review {
  id: string
  rating: number
  title: string | null
  content: string | null
  systemSpecs: any
  performanceData: any
  isVerified: boolean
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    username: string | null
    createdAt: string
    _count: {
      reviews: number
    }
  }
  gpu: {
    id: string
    name: string
    manufacturer: string
    architecture: string | null
  }
}

interface Pagination {
  page: number
  limit: number
  totalCount: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function ReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>([])
  const [pagination, setPagination] = useState<Pagination | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Filters
  const [filters, setFilters] = useState({
    rating: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const fetchReviews = async (page = 1, append = false) => {
    try {
      setIsLoading(true)
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        ...(filters.rating && { rating: filters.rating })
      })

      const response = await fetch(`/api/reviews?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch reviews')
      }

      const data = await response.json()
      
      if (append) {
        setReviews(prev => [...prev, ...data.reviews])
      } else {
        setReviews(data.reviews)
      }
      
      setPagination(data.pagination)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchReviews(1, false)
  }, [filters])

  const handleLoadMore = () => {
    if (pagination?.hasNext) {
      fetchReviews(pagination.page + 1, true)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              GPU Reviews
            </h1>
            <p className="text-foreground-muted">
              Real-world experiences and insights from the GPU community
            </p>
          </div>

          {/* Filters */}
          <div className="bg-card border border-border rounded-lg p-4 mb-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div>
                <label className="block text-sm font-medium text-foreground-muted mb-1">
                  Filter by Rating
                </label>
                <select
                  value={filters.rating}
                  onChange={(e) => handleFilterChange('rating', e.target.value)}
                  className="input"
                >
                  <option value="">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground-muted mb-1">
                  Sort By
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="input"
                >
                  <option value="createdAt">Date</option>
                  <option value="rating">Rating</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground-muted mb-1">
                  Order
                </label>
                <select
                  value={filters.sortOrder}
                  onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                  className="input"
                >
                  <option value="desc">Newest First</option>
                  <option value="asc">Oldest First</option>
                </select>
              </div>
            </div>
          </div>

          {/* Stats */}
          {pagination && (
            <div className="mb-6 text-sm text-foreground-muted">
              Showing {reviews.length} of {pagination.totalCount} reviews
            </div>
          )}

          {/* Error */}
          {error && (
            <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
              <p className="text-red-400">{error}</p>
            </div>
          )}

          {/* Reviews */}
          <ReviewDisplay
            reviews={reviews}
            isLoading={isLoading}
            onLoadMore={handleLoadMore}
            hasMore={pagination?.hasNext || false}
            showGPUInfo={true}
          />
        </div>
      </div>
    </Layout>
  )
}