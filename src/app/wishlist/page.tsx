'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Layout } from '@/components/layout/Layout'
import { Button } from '@/components/ui/Button'
import { GPUCard } from '@/components/ui/GPUCard'

interface WishlistItem {
  id: string
  targetPrice: number | null
  notes: string | null
  createdAt: string
  gpu: {
    id: string
    name: string
    manufacturer: string
    architecture: string
    specifications: any
    physicalSpecs: any
    powerRequirements: any
    originalMsrp: number | null
    pricing: Array<{
      price: number
      retailer: string
      availability: string
    }>
  }
}

export default function WishlistPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [wishlist, setWishlist] = useState<WishlistItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user?.id) {
      fetchWishlist()
    }
  }, [session])

  const fetchWishlist = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/user/wishlist')
      
      if (!response.ok) {
        throw new Error('Failed to fetch wishlist')
      }

      const data = await response.json()
      setWishlist(data.wishlist)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const removeFromWishlist = async (itemId: string) => {
    try {
      const response = await fetch(`/api/user/wishlist/${itemId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to remove item from wishlist')
      }

      // Remove item from local state
      setWishlist(prev => prev.filter(item => item.id !== itemId))
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  const createPriceAlert = async (gpuId: string, targetPrice: number) => {
    try {
      const response = await fetch('/api/pricing/alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          gpuId,
          targetPrice,
          emailAlert: true,
          pushAlert: false
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to create price alert')
      }

      alert('Price alert created successfully!')
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to create price alert')
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-card rounded w-1/4"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-64 bg-card rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    )
  }

  if (!session) {
    return null
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground">My Wishlist</h1>
              <p className="text-foreground-muted mt-2">
                {wishlist.length} GPU{wishlist.length !== 1 ? 's' : ''} saved for later
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push('/gpus')}
            >
              Browse GPUs
            </Button>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
              <p className="text-red-400">{error}</p>
            </div>
          )}

          {wishlist.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-card rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-4xl">💝</span>
              </div>
              <h2 className="text-xl font-semibold text-foreground mb-2">
                Your wishlist is empty
              </h2>
              <p className="text-foreground-muted mb-6">
                Start adding GPUs you're interested in to keep track of them and get price alerts.
              </p>
              <Button onClick={() => router.push('/gpus')}>
                Browse GPUs
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {wishlist.map((item) => (
                <div key={item.id} className="relative">
                  <GPUCard
                    gpu={item.gpu}
                    pricing={item.gpu.pricing[0] ? {
                      price: item.gpu.pricing[0].price,
                      retailer: item.gpu.pricing[0].retailer
                    } : undefined}
                    onSelect={(id) => router.push(`/gpus/${id}`)}
                  />
                  
                  {/* Wishlist Actions */}
                  <div className="mt-4 space-y-2">
                    {item.targetPrice && (
                      <div className="text-sm text-foreground-muted">
                        Target Price: <span className="text-primary font-medium">${item.targetPrice}</span>
                      </div>
                    )}
                    
                    <div className="flex gap-2">
                      {item.targetPrice && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => createPriceAlert(item.gpu.id, item.targetPrice!)}
                          className="flex-1"
                        >
                          Set Alert
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFromWishlist(item.id)}
                        className="text-red-400 hover:text-red-300 border-red-400/30 hover:border-red-300/30"
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}