import type { Metadata } from "next";
import "./globals.css";
import { NextAuthProvider } from '@/components/providers/SessionProvider';

export const metadata: Metadata = {
  title: "GPULabs - Premium GPU Recommendations",
  description: "Discover the perfect GPU for your needs with AI-powered recommendations, real-time pricing, and expert reviews.",
  keywords: "GPU, graphics card, recommendations, gaming, AI, machine learning, cryptocurrency mining",
  authors: [{ name: "GPULabs Team" }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className="min-h-screen bg-background text-foreground font-sans antialiased">
        <NextAuthProvider>
          <div className="relative flex min-h-screen flex-col">
            {children}
          </div>
        </NextAuthProvider>
      </body>
    </html>
  );
}
