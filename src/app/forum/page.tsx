'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'

interface ForumCategory {
  id: string
  name: string
  description: string
  slug: string
  color: string
  icon: string
  _count: {
    threads: number
  }
}

interface ForumThread {
  id: string
  title: string
  slug: string
  replyCount: number
  viewCount: number
  isPinned: boolean
  createdAt: string
  lastReplyAt: string
  author: {
    id: string
    name: string
    username: string
    image: string
  }
  lastReplier?: {
    id: string
    name: string
    username: string
    image: string
  }
  category: {
    id: string
    name: string
    slug: string
    color: string
  }
}

export default function ForumPage() {
  const { data: session } = useSession()
  const [categories, setCategories] = useState<ForumCategory[]>([])
  const [recentThreads, setRecentThreads] = useState<ForumThread[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchForumData()
  }, [])

  const fetchForumData = async () => {
    try {
      const [categoriesRes, threadsRes] = await Promise.all([
        fetch('/api/forum/categories'),
        fetch('/api/forum/threads?limit=10')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData)
      }

      if (threadsRes.ok) {
        const threadsData = await threadsRes.json()
        setRecentThreads(threadsData.threads)
      }
    } catch (error) {
      console.error('Error fetching forum data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-card rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-card rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Community Forum</h1>
            <p className="text-foreground-muted">
              Discuss GPUs, share builds, and get help from the community
            </p>
          </div>
          {session && (
            <Link
              href="/forum/new-thread"
              className="bg-accent hover:bg-accent-hover text-background px-6 py-2 rounded-lg font-medium transition-colors"
            >
              New Thread
            </Link>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Categories */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold text-foreground mb-4">Categories</h2>
            <div className="space-y-3">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/forum/category/${category.slug}`}
                  className="block bg-card hover:bg-card-hover rounded-lg p-4 transition-colors border border-border"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color || '#f59e0b' }}
                      ></div>
                      <div>
                        <h3 className="font-medium text-foreground">{category.name}</h3>
                        {category.description && (
                          <p className="text-sm text-foreground-muted mt-1">
                            {category.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-foreground">
                        {category._count.threads}
                      </div>
                      <div className="text-xs text-foreground-muted">threads</div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Recent Activity</h2>
            <div className="space-y-3">
              {recentThreads.map((thread) => (
                <Link
                  key={thread.id}
                  href={`/forum/thread/${thread.slug}`}
                  className="block bg-card hover:bg-card-hover rounded-lg p-3 transition-colors border border-border"
                >
                  <div className="flex items-start space-x-3">
                    <img
                      src={thread.author.image || '/default-avatar.png'}
                      alt={thread.author.name || thread.author.username}
                      className="w-8 h-8 rounded-full"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-foreground truncate">
                        {thread.title}
                      </h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-foreground-muted">
                          by {thread.author.name || thread.author.username}
                        </span>
                        <span className="text-xs text-foreground-muted">•</span>
                        <span className="text-xs text-foreground-muted">
                          {formatDate(thread.lastReplyAt || thread.createdAt)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className="text-xs text-foreground-muted">
                          {thread.replyCount} replies
                        </span>
                        <span className="text-xs text-foreground-muted">
                          {thread.viewCount} views
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}