'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { useParams, useSearchParams } from 'next/navigation'

interface ForumThread {
  id: string
  title: string
  slug: string
  replyCount: number
  viewCount: number
  isPinned: boolean
  createdAt: string
  lastReplyAt: string
  author: {
    id: string
    name: string
    username: string
    image: string
  }
  lastReplier?: {
    id: string
    name: string
    username: string
    image: string
  }
  category: {
    id: string
    name: string
    slug: string
    color: string
  }
}

interface Pagination {
  page: number
  limit: number
  total: number
  pages: number
}

export default function CategoryPage() {
  const { data: session } = useSession()
  const params = useParams()
  const searchParams = useSearchParams()
  const categorySlug = params.slug as string
  
  const [threads, setThreads] = useState<ForumThread[]>([])
  const [pagination, setPagination] = useState<Pagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'lastReplyAt')
  const [search, setSearch] = useState(searchParams.get('search') || '')

  useEffect(() => {
    fetchThreads()
  }, [categorySlug, sortBy, search])

  const fetchThreads = async (page = 1) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        sortBy,
        ...(search && { search })
      })

      const response = await fetch(`/api/forum/threads?${params}`)
      if (response.ok) {
        const data = await response.json()
        setThreads(data.threads)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching threads:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchThreads(1)
  }

  if (loading && threads.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-card rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="h-16 bg-card rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const categoryName = threads[0]?.category?.name || 'Forum Category'

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <nav className="text-sm text-foreground-muted mb-2">
              <Link href="/forum" className="hover:text-foreground">Forum</Link>
              <span className="mx-2">›</span>
              <span>{categoryName}</span>
            </nav>
            <h1 className="text-2xl font-bold text-foreground">{categoryName}</h1>
          </div>
          {session && (
            <Link
              href={`/forum/new-thread?category=${categorySlug}`}
              className="bg-accent hover:bg-accent-hover text-background px-4 py-2 rounded-lg font-medium transition-colors"
            >
              New Thread
            </Link>
          )}
        </div>

        {/* Search and Sort */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search threads..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full bg-card border border-border rounded-lg px-4 py-2 text-foreground placeholder-foreground-muted focus:outline-none focus:ring-2 focus:ring-accent"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-foreground-muted hover:text-foreground"
              >
                🔍
              </button>
            </div>
          </form>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="bg-card border border-border rounded-lg px-4 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
          >
            <option value="lastReplyAt">Latest Reply</option>
            <option value="created">Newest</option>
            <option value="replies">Most Replies</option>
            <option value="views">Most Views</option>
          </select>
        </div>

        {/* Threads List */}
        <div className="space-y-2">
          {threads.map((thread) => (
            <div
              key={thread.id}
              className={`bg-card border border-border rounded-lg p-4 hover:bg-card-hover transition-colors ${
                thread.isPinned ? 'border-accent' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    {thread.isPinned && (
                      <span className="text-xs bg-accent text-background px-2 py-1 rounded">
                        Pinned
                      </span>
                    )}
                    <Link
                      href={`/forum/thread/${thread.slug}`}
                      className="text-lg font-medium text-foreground hover:text-accent transition-colors"
                    >
                      {thread.title}
                    </Link>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-foreground-muted">
                    <div className="flex items-center space-x-2">
                      <img
                        src={thread.author.image || '/default-avatar.png'}
                        alt={thread.author.name || thread.author.username}
                        className="w-5 h-5 rounded-full"
                      />
                      <span>{thread.author.name || thread.author.username}</span>
                    </div>
                    <span>•</span>
                    <span>{formatDate(thread.createdAt)}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-foreground">
                    {thread.replyCount} replies
                  </div>
                  <div className="text-sm text-foreground-muted">
                    {thread.viewCount} views
                  </div>
                  {thread.lastReplier && (
                    <div className="text-xs text-foreground-muted mt-1">
                      Last by {thread.lastReplier.name || thread.lastReplier.username}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        {pagination && pagination.pages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              {pagination.page > 1 && (
                <button
                  onClick={() => fetchThreads(pagination.page - 1)}
                  className="px-3 py-2 bg-card border border-border rounded-lg text-foreground hover:bg-card-hover transition-colors"
                >
                  Previous
                </button>
              )}
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                const page = i + 1
                return (
                  <button
                    key={page}
                    onClick={() => fetchThreads(page)}
                    className={`px-3 py-2 rounded-lg transition-colors ${
                      page === pagination.page
                        ? 'bg-accent text-background'
                        : 'bg-card border border-border text-foreground hover:bg-card-hover'
                    }`}
                  >
                    {page}
                  </button>
                )
              })}
              {pagination.page < pagination.pages && (
                <button
                  onClick={() => fetchThreads(pagination.page + 1)}
                  className="px-3 py-2 bg-card border border-border rounded-lg text-foreground hover:bg-card-hover transition-colors"
                >
                  Next
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}