'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

interface PriceAlert {
  id: string
  userId: string
  gpuId: string
  targetPrice: number
  currentPrice: number
  isTriggered: boolean
  emailAlert: boolean
  pushAlert: boolean
}

interface GPU {
  id: string
  name: string
  manufacturer: string
  architecture: string
}

export default function AlertsPage() {
  const { data: session } = useSession()
  const [alerts, setAlerts] = useState<PriceAlert[]>([])
  const [availableGPUs, setAvailableGPUs] = useState<GPU[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newAlert, setNewAlert] = useState({
    gpuId: '',
    targetPrice: '',
    emailAlert: true,
    pushAlert: false
  })

  useEffect(() => {
    if (session) {
      fetchAlerts()
      fetchAvailableGPUs()
    }
  }, [session])

  const fetchAlerts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/pricing/alerts')
      if (response.ok) {
        const data = await response.json()
        setAlerts(data.alerts || [])
      }
    } catch (error) {
      console.error('Error fetching alerts:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableGPUs = async () => {
    try {
      const response = await fetch('/api/gpus?limit=100')
      if (response.ok) {
        const data = await response.json()
        setAvailableGPUs(data.gpus || [])
      }
    } catch (error) {
      console.error('Error fetching GPUs:', error)
    }
  }

  const createAlert = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newAlert.gpuId || !newAlert.targetPrice) {
      alert('Please select a GPU and enter a target price')
      return
    }

    try {
      const response = await fetch('/api/pricing/alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          gpuId: newAlert.gpuId,
          targetPrice: parseFloat(newAlert.targetPrice),
          emailAlert: newAlert.emailAlert,
          pushAlert: newAlert.pushAlert
        })
      })

      if (response.ok) {
        setShowCreateForm(false)
        setNewAlert({
          gpuId: '',
          targetPrice: '',
          emailAlert: true,
          pushAlert: false
        })
        fetchAlerts() // Refresh alerts
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create alert')
      }
    } catch (error) {
      console.error('Error creating alert:', error)
      alert('Failed to create alert')
    }
  }

  const deleteAlert = async (alertId: string) => {
    if (!confirm('Are you sure you want to delete this alert?')) {
      return
    }

    try {
      const response = await fetch(`/api/pricing/alerts?alertId=${alertId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchAlerts() // Refresh alerts
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to delete alert')
      }
    } catch (error) {
      console.error('Error deleting alert:', error)
      alert('Failed to delete alert')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getGPUName = (gpuId: string) => {
    const gpu = availableGPUs.find(g => g.id === gpuId)
    return gpu ? gpu.name : 'Unknown GPU'
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Sign In Required</h1>
          <p className="text-foreground-muted mb-6">
            You need to sign in to manage price alerts.
          </p>
          <Link
            href="/auth/signin"
            className="bg-accent hover:bg-accent-hover text-background px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-card rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-card rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Price Alerts</h1>
            <p className="text-foreground-muted">
              Get notified when GPU prices drop to your target
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-accent hover:bg-accent-hover text-background px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Create Alert
          </button>
        </div>

        {/* Create Alert Form */}
        {showCreateForm && (
          <div className="bg-card border border-border rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-foreground mb-4">Create New Price Alert</h2>
            <form onSubmit={createAlert} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Select GPU
                  </label>
                  <select
                    value={newAlert.gpuId}
                    onChange={(e) => setNewAlert({ ...newAlert, gpuId: e.target.value })}
                    className="w-full bg-card border border-border rounded-lg px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
                    required
                  >
                    <option value="">Choose a GPU...</option>
                    {availableGPUs.map((gpu) => (
                      <option key={gpu.id} value={gpu.id}>
                        {gpu.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Target Price ($)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10000"
                    step="1"
                    value={newAlert.targetPrice}
                    onChange={(e) => setNewAlert({ ...newAlert, targetPrice: e.target.value })}
                    className="w-full bg-card border border-border rounded-lg px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
                    placeholder="Enter target price"
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newAlert.emailAlert}
                    onChange={(e) => setNewAlert({ ...newAlert, emailAlert: e.target.checked })}
                    className="w-4 h-4 text-accent bg-card border-border rounded focus:ring-accent"
                  />
                  <span className="ml-2 text-sm text-foreground">Email notifications</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newAlert.pushAlert}
                    onChange={(e) => setNewAlert({ ...newAlert, pushAlert: e.target.checked })}
                    className="w-4 h-4 text-accent bg-card border-border rounded focus:ring-accent"
                  />
                  <span className="ml-2 text-sm text-foreground">Push notifications</span>
                </label>
              </div>

              <div className="flex space-x-4">
                <button
                  type="submit"
                  className="bg-accent hover:bg-accent-hover text-background px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Create Alert
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="bg-card-hover hover:bg-border text-foreground px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Alerts List */}
        {alerts.length > 0 ? (
          <div className="space-y-4">
            {alerts.map((alert) => (
              <div
                key={alert.id}
                className={`bg-card border rounded-lg p-6 ${
                  alert.isTriggered ? 'border-green-500 bg-green-500/5' : 'border-border'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      {getGPUName(alert.gpuId)}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-foreground-muted">Target Price:</span>
                        <span className="ml-2 font-medium text-accent">
                          {formatCurrency(alert.targetPrice)}
                        </span>
                      </div>
                      <div>
                        <span className="text-foreground-muted">Current Price:</span>
                        <span className={`ml-2 font-medium ${
                          alert.currentPrice <= alert.targetPrice ? 'text-green-400' : 'text-foreground'
                        }`}>
                          {alert.currentPrice > 0 ? formatCurrency(alert.currentPrice) : 'N/A'}
                        </span>
                      </div>
                      <div>
                        <span className="text-foreground-muted">Status:</span>
                        <span className={`ml-2 font-medium ${
                          alert.isTriggered ? 'text-green-400' : 'text-foreground-muted'
                        }`}>
                          {alert.isTriggered ? 'Target Reached!' : 'Monitoring'}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 mt-3 text-xs text-foreground-muted">
                      {alert.emailAlert && <span>📧 Email alerts</span>}
                      {alert.pushAlert && <span>📱 Push alerts</span>}
                    </div>
                  </div>
                  <div className="ml-6">
                    <button
                      onClick={() => deleteAlert(alert.id)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-foreground-muted mb-4">
              You don't have any price alerts yet.
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-accent hover:bg-accent-hover text-background px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Create Your First Alert
            </button>
          </div>
        )}
      </div>
    </div>
  )
}