@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --background: #0a0a0a;
  --foreground: #ffffff;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #404040;
}

@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-primary-light text-black font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary/20;
  }
  
  .btn-secondary {
    @apply bg-card hover:bg-card-hover text-foreground border border-border font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }
  
  .card {
    @apply bg-card border border-border rounded-xl p-6 hover:bg-card-hover transition-all duration-200;
  }
  
  .input {
    @apply bg-background-secondary border border-border rounded-lg px-4 py-3 text-foreground placeholder-foreground-muted focus:border-primary focus:outline-none transition-colors duration-200;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-accent-gold bg-clip-text text-transparent;
  }
  
  .glow-effect {
    @apply shadow-lg shadow-primary/10 hover:shadow-primary/20 transition-shadow duration-300;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
