'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'

interface BuildShowcase {
  id: string
  title: string
  description: string
  totalCost: number
  buildDate: string
  images: string[]
  tags: string[]
  viewCount: number
  likeCount: number
  isFeatured: boolean
  createdAt: string
  author: {
    id: string
    name: string
    username: string
    image: string
    reputation?: {
      totalScore: number
      badges: string[]
    }
  }
  _count: {
    comments: number
    likes: number
  }
}

interface Pagination {
  page: number
  limit: number
  total: number
  pages: number
}

export default function BuildsPage() {
  const { data: session } = useSession()
  const [builds, setBuilds] = useState<BuildShowcase[]>([])
  const [pagination, setPagination] = useState<Pagination | null>(null)
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState('created')
  const [selectedTag, setSelectedTag] = useState('')
  const [search, setSearch] = useState('')

  useEffect(() => {
    fetchBuilds()
  }, [sortBy, selectedTag, search])

  const fetchBuilds = async (page = 1) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        sortBy,
        ...(selectedTag && { tag: selectedTag }),
        ...(search && { search })
      })

      const response = await fetch(`/api/builds?${params}`)
      if (response.ok) {
        const data = await response.json()
        setBuilds(data.builds)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching builds:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchBuilds(1)
  }

  const popularTags = ['gaming', 'workstation', 'budget', 'high-end', 'rgb', 'sff', 'watercooled']

  if (loading && builds.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-card rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="h-80 bg-card rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Build Showcase</h1>
            <p className="text-foreground-muted">
              Discover amazing PC builds from the community
            </p>
          </div>
          {session && (
            <Link
              href="/builds/new"
              className="bg-accent hover:bg-accent-hover text-background px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Share Your Build
            </Link>
          )}
        </div>

        {/* Filters */}
        <div className="flex flex-col lg:flex-row gap-4 mb-8">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search builds..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full bg-card border border-border rounded-lg px-4 py-2 text-foreground placeholder-foreground-muted focus:outline-none focus:ring-2 focus:ring-accent"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-foreground-muted hover:text-foreground"
              >
                🔍
              </button>
            </div>
          </form>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="bg-card border border-border rounded-lg px-4 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
          >
            <option value="created">Newest</option>
            <option value="likes">Most Liked</option>
            <option value="views">Most Viewed</option>
            <option value="cost">Highest Cost</option>
          </select>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-8">
          <button
            onClick={() => setSelectedTag('')}
            className={`px-3 py-1 rounded-full text-sm transition-colors ${
              selectedTag === ''
                ? 'bg-accent text-background'
                : 'bg-card border border-border text-foreground hover:bg-card-hover'
            }`}
          >
            All
          </button>
          {popularTags.map((tag) => (
            <button
              key={tag}
              onClick={() => setSelectedTag(tag)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedTag === tag
                  ? 'bg-accent text-background'
                  : 'bg-card border border-border text-foreground hover:bg-card-hover'
              }`}
            >
              {tag}
            </button>
          ))}
        </div>

        {/* Builds Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {builds.map((build) => (
            <Link
              key={build.id}
              href={`/builds/${build.id}`}
              className="group bg-card border border-border rounded-lg overflow-hidden hover:border-accent transition-colors"
            >
              {/* Image */}
              <div className="aspect-video bg-card-hover relative overflow-hidden">
                {build.images.length > 0 ? (
                  <img
                    src={build.images[0]}
                    alt={build.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-foreground-muted">
                    No Image
                  </div>
                )}
                {build.isFeatured && (
                  <div className="absolute top-2 left-2 bg-accent text-background px-2 py-1 rounded text-xs font-medium">
                    Featured
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                  {build.title}
                </h3>
                {build.description && (
                  <p className="text-sm text-foreground-muted mb-3 line-clamp-2">
                    {build.description}
                  </p>
                )}

                {/* Tags */}
                {build.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {build.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-card-hover text-xs text-foreground-muted rounded"
                      >
                        {tag}
                      </span>
                    ))}
                    {build.tags.length > 3 && (
                      <span className="px-2 py-1 bg-card-hover text-xs text-foreground-muted rounded">
                        +{build.tags.length - 3}
                      </span>
                    )}
                  </div>
                )}

                {/* Author and Stats */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <img
                      src={build.author.image || '/default-avatar.png'}
                      alt={build.author.name || build.author.username}
                      className="w-6 h-6 rounded-full"
                    />
                    <span className="text-sm text-foreground-muted">
                      {build.author.name || build.author.username}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm text-foreground-muted">
                    <span>❤️ {build.likeCount}</span>
                    <span>👁️ {build.viewCount}</span>
                  </div>
                </div>

                {/* Cost and Date */}
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-border">
                  {build.totalCost && (
                    <span className="text-sm font-medium text-accent">
                      {formatCurrency(build.totalCost)}
                    </span>
                  )}
                  <span className="text-xs text-foreground-muted">
                    {formatDate(build.createdAt)}
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Pagination */}
        {pagination && pagination.pages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              {pagination.page > 1 && (
                <button
                  onClick={() => fetchBuilds(pagination.page - 1)}
                  className="px-3 py-2 bg-card border border-border rounded-lg text-foreground hover:bg-card-hover transition-colors"
                >
                  Previous
                </button>
              )}
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                const page = i + 1
                return (
                  <button
                    key={page}
                    onClick={() => fetchBuilds(page)}
                    className={`px-3 py-2 rounded-lg transition-colors ${
                      page === pagination.page
                        ? 'bg-accent text-background'
                        : 'bg-card border border-border text-foreground hover:bg-card-hover'
                    }`}
                  >
                    {page}
                  </button>
                )
              })}
              {pagination.page < pagination.pages && (
                <button
                  onClick={() => fetchBuilds(pagination.page + 1)}
                  className="px-3 py-2 bg-card border border-border rounded-lg text-foreground hover:bg-card-hover transition-colors"
                >
                  Next
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}