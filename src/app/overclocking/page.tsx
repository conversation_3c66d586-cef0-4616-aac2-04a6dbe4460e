'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'

interface GPU {
  id: string
  name: string
  manufacturer: string
  architecture: string
}

interface OverclockingData {
  id: string
  coreClockOffset: number | null
  memoryClockOffset: number | null
  powerLimit: number | null
  voltageOffset: number | null
  performanceGain: number | null
  stabilityRating: number | null
  stockTemperature: number | null
  ocTemperature: number | null
  maxTemperature: number | null
  thermalThrottling: boolean | null
  stockPower: number | null
  ocPower: number | null
  powerEfficiency: number | null
  coolingType: string | null
  ambientTemp: number | null
  testDuration: number | null
  source: string
  testDate: string
  notes: string | null
  gpu: GPU
}

export default function OverclockingPage() {
  const searchParams = useSearchParams()
  const [overclockingData, setOverclockingData] = useState<OverclockingData[]>([])
  const [availableGPUs, setAvailableGPUs] = useState<GPU[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGPU, setSelectedGPU] = useState(searchParams.get('gpuId') || '')
  const [coolingFilter, setCoolingFilter] = useState('')
  const [minStability, setMinStability] = useState('')

  useEffect(() => {
    fetchAvailableGPUs()
    fetchOverclockingData()
  }, [selectedGPU, coolingFilter, minStability])

  const fetchAvailableGPUs = async () => {
    try {
      const response = await fetch('/api/gpus?limit=100')
      if (response.ok) {
        const data = await response.json()
        setAvailableGPUs(data.gpus || [])
      }
    } catch (error) {
      console.error('Error fetching GPUs:', error)
    }
  }

  const fetchOverclockingData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (selectedGPU) params.append('gpuId', selectedGPU)
      if (coolingFilter) params.append('coolingType', coolingFilter)
      if (minStability) params.append('minStability', minStability)

      const response = await fetch(`/api/overclocking?${params}`)
      if (response.ok) {
        const data = await response.json()
        setOverclockingData(data.overclockingData || [])
      }
    } catch (error) {
      console.error('Error fetching overclocking data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStabilityColor = (rating: number | null) => {
    if (!rating) return 'text-foreground-muted'
    if (rating >= 4) return 'text-green-400'
    if (rating >= 3) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getStabilityText = (rating: number | null) => {
    if (!rating) return 'Unknown'
    if (rating >= 4) return 'Stable'
    if (rating >= 3) return 'Moderate'
    return 'Unstable'
  }

  const getThermalStatus = (temp: number | null) => {
    if (!temp) return { color: 'text-foreground-muted', text: 'Unknown' }
    if (temp <= 75) return { color: 'text-green-400', text: 'Cool' }
    if (temp <= 80) return { color: 'text-yellow-400', text: 'Warm' }
    if (temp <= 85) return { color: 'text-orange-400', text: 'Hot' }
    return { color: 'text-red-400', text: 'Critical' }
  }

  const formatNumber = (num: number | null | undefined, suffix = '') => {
    if (num === null || num === undefined || typeof num !== 'number') return 'N/A'
    return `${num.toFixed(1)}${suffix}`
  }

  const coolingTypes = ['Air', 'AIO', 'Custom Loop', 'Stock']

  if (loading && overclockingData.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-card rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-32 bg-card rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">GPU Overclocking Analysis</h1>
          <p className="text-foreground-muted">
            Explore overclocking potential, thermal performance, and stability data
          </p>
        </div>

        {/* Filters */}
        <div className="bg-card border border-border rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* GPU Selection */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Select GPU
              </label>
              <select
                value={selectedGPU}
                onChange={(e) => setSelectedGPU(e.target.value)}
                className="w-full bg-card border border-border rounded-lg px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
              >
                <option value="">All GPUs</option>
                {availableGPUs.map((gpu) => (
                  <option key={gpu.id} value={gpu.id}>
                    {gpu.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Cooling Type */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Cooling Type
              </label>
              <select
                value={coolingFilter}
                onChange={(e) => setCoolingFilter(e.target.value)}
                className="w-full bg-card border border-border rounded-lg px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
              >
                <option value="">All Cooling</option>
                {coolingTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            {/* Minimum Stability */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Min Stability
              </label>
              <select
                value={minStability}
                onChange={(e) => setMinStability(e.target.value)}
                className="w-full bg-card border border-border rounded-lg px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
              >
                <option value="">Any Stability</option>
                <option value="4">4+ Stars (Stable)</option>
                <option value="3">3+ Stars (Moderate)</option>
                <option value="2">2+ Stars (Basic)</option>
              </select>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                onClick={() => {
                  setSelectedGPU('')
                  setCoolingFilter('')
                  setMinStability('')
                }}
                className="w-full bg-card-hover hover:bg-border text-foreground px-4 py-2 rounded-lg transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Results */}
        {overclockingData.length > 0 ? (
          <div className="space-y-6">
            {overclockingData.map((data) => (
              <div
                key={data.id}
                className="bg-card border border-border rounded-lg p-6"
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">
                      {data.gpu.name}
                    </h3>
                    <p className="text-sm text-foreground-muted">
                      {data.gpu.manufacturer} • {data.gpu.architecture} • {data.coolingType || 'Unknown Cooling'}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={`text-lg font-semibold ${getStabilityColor(data.stabilityRating)}`}>
                      {getStabilityText(data.stabilityRating)}
                    </div>
                    <div className="text-sm text-foreground-muted">
                      {data.stabilityRating ? `${data.stabilityRating}/5 stars` : 'No rating'}
                    </div>
                  </div>
                </div>

                {/* Performance & Settings */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-4">
                  {/* Performance Gain */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent">
                      {formatNumber(data.performanceGain, '%')}
                    </div>
                    <div className="text-sm text-foreground-muted">Performance Gain</div>
                  </div>

                  {/* Core Clock */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground">
                      {data.coreClockOffset ? `+${data.coreClockOffset}` : 'N/A'}
                    </div>
                    <div className="text-sm text-foreground-muted">Core Clock (MHz)</div>
                  </div>

                  {/* Memory Clock */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground">
                      {data.memoryClockOffset ? `+${data.memoryClockOffset}` : 'N/A'}
                    </div>
                    <div className="text-sm text-foreground-muted">Memory Clock (MHz)</div>
                  </div>

                  {/* Power Limit */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground">
                      {data.powerLimit ? `${data.powerLimit}%` : 'N/A'}
                    </div>
                    <div className="text-sm text-foreground-muted">Power Limit</div>
                  </div>
                </div>

                {/* Thermal & Power Data */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Temperature */}
                  <div className="bg-card-hover rounded-lg p-4">
                    <h4 className="font-medium text-foreground mb-2">Temperature</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Stock:</span>
                        <span className="text-foreground">{formatNumber(data.stockTemperature, '°C')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Overclocked:</span>
                        <span className={getThermalStatus(data.ocTemperature).color}>
                          {formatNumber(data.ocTemperature, '°C')}
                        </span>
                      </div>
                      {data.thermalThrottling !== null && (
                        <div className="flex justify-between">
                          <span className="text-foreground-muted">Throttling:</span>
                          <span className={data.thermalThrottling ? 'text-red-400' : 'text-green-400'}>
                            {data.thermalThrottling ? 'Yes' : 'No'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Power Consumption */}
                  <div className="bg-card-hover rounded-lg p-4">
                    <h4 className="font-medium text-foreground mb-2">Power Consumption</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Stock:</span>
                        <span className="text-foreground">{formatNumber(data.stockPower, 'W')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Overclocked:</span>
                        <span className="text-foreground">{formatNumber(data.ocPower, 'W')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Efficiency:</span>
                        <span className="text-accent">{formatNumber(data.powerEfficiency, ' FPS/W')}</span>
                      </div>
                    </div>
                  </div>

                  {/* Test Conditions */}
                  <div className="bg-card-hover rounded-lg p-4">
                    <h4 className="font-medium text-foreground mb-2">Test Conditions</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Ambient:</span>
                        <span className="text-foreground">{formatNumber(data.ambientTemp, '°C')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Duration:</span>
                        <span className="text-foreground">{formatNumber(data.testDuration, ' min')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-foreground-muted">Source:</span>
                        <span className="text-foreground-muted text-xs">{data.source}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                {data.notes && (
                  <div className="mt-4 p-3 bg-card-hover rounded-lg">
                    <h4 className="font-medium text-foreground mb-1">Notes</h4>
                    <p className="text-sm text-foreground-muted">{data.notes}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-foreground-muted mb-4">
              {loading ? 'Loading overclocking data...' : 'No overclocking data found for the selected filters.'}
            </div>
            {!loading && (
              <p className="text-sm text-foreground-muted">
                Try adjusting your filters or contribute your own overclocking results!
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}