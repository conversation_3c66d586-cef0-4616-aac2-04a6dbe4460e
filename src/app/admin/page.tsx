'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'

interface DatabaseStatus {
  counts: {
    gpus: number
    pricing: number
    users: number
    reviews: number
  }
  isSeeded: boolean
}

interface ValidationReport {
  summary: {
    totalGPUs: number
    dataCompleteness: number
    duplicateRate: number
    validationScore: number
  }
  details: {
    isValid: boolean
    errors: string[]
    warnings: string[]
    stats: {
      totalGPUs: number
      validGPUs: number
      invalidGPUs: number
      missingSpecs: number
      duplicates: number
    }
  }
  recommendations: string[]
}

export default function AdminPage() {
  const [dbStatus, setDbStatus] = useState<DatabaseStatus | null>(null)
  const [validationReport, setValidationReport] = useState<ValidationReport | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDatabaseStatus()
  }, [])

  const fetchDatabaseStatus = async () => {
    try {
      const response = await fetch('/api/gpus/seed')
      if (response.ok) {
        const data = await response.json()
        setDbStatus(data)
      }
    } catch (error) {
      console.error('Failed to fetch database status:', error)
    }
  }

  const seedDatabase = async (force = false) => {
    setIsLoading(true)
    setMessage(null)
    setError(null)

    try {
      const response = await fetch('/api/gpus/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ force })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage(data.message)
        fetchDatabaseStatus()
      } else {
        setError(data.error || 'Failed to seed database')
      }
    } catch (error) {
      setError('Failed to seed database')
    } finally {
      setIsLoading(false)
    }
  }

  const validateData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/admin/data-validation?action=report')
      
      if (response.ok) {
        const data = await response.json()
        setValidationReport(data)
      } else {
        setError('Failed to validate data')
      }
    } catch (error) {
      setError('Failed to validate data')
    } finally {
      setIsLoading(false)
    }
  }

  const autoFixIssues = async () => {
    setIsLoading(true)
    setMessage(null)
    setError(null)

    try {
      const response = await fetch('/api/admin/data-validation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'autofix' })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage(data.message)
        validateData() // Refresh validation report
      } else {
        setError(data.error || 'Failed to auto-fix issues')
      }
    } catch (error) {
      setError('Failed to auto-fix issues')
    } finally {
      setIsLoading(false)
    }
  }

  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Admin Panel</h1>
          <p className="text-foreground-muted">Admin panel is not available in production.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">GPULabs Admin Panel</h1>

        {/* Messages */}
        {message && (
          <div className="mb-6 p-4 bg-green-900/50 border border-green-700 rounded-lg">
            <p className="text-green-400">{message}</p>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Database Status */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-bold text-foreground mb-4">Database Status</h2>
            
            {dbStatus ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-background-secondary rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary">{dbStatus.counts.gpus}</div>
                    <div className="text-sm text-foreground-muted">GPUs</div>
                  </div>
                  <div className="bg-background-secondary rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary">{dbStatus.counts.pricing}</div>
                    <div className="text-sm text-foreground-muted">Price Entries</div>
                  </div>
                  <div className="bg-background-secondary rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary">{dbStatus.counts.users}</div>
                    <div className="text-sm text-foreground-muted">Users</div>
                  </div>
                  <div className="bg-background-secondary rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary">{dbStatus.counts.reviews}</div>
                    <div className="text-sm text-foreground-muted">Reviews</div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={() => seedDatabase(false)}
                    disabled={isLoading}
                    loading={isLoading}
                  >
                    Seed Database
                  </Button>
                  <Button
                    onClick={() => seedDatabase(true)}
                    disabled={isLoading}
                    variant="outline"
                  >
                    Force Reseed
                  </Button>
                  <Button
                    onClick={fetchDatabaseStatus}
                    disabled={isLoading}
                    variant="outline"
                  >
                    Refresh
                  </Button>
                </div>
              </div>
            ) : (
              <div className="animate-pulse space-y-4">
                <div className="h-20 bg-background-secondary rounded"></div>
                <div className="h-10 bg-background-secondary rounded"></div>
              </div>
            )}
          </div>

          {/* Data Validation */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-bold text-foreground mb-4">Data Validation</h2>
            
            {validationReport ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-background-secondary rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary">
                      {validationReport.summary.validationScore.toFixed(1)}%
                    </div>
                    <div className="text-sm text-foreground-muted">Validation Score</div>
                  </div>
                  <div className="bg-background-secondary rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary">
                      {validationReport.summary.dataCompleteness.toFixed(1)}%
                    </div>
                    <div className="text-sm text-foreground-muted">Data Completeness</div>
                  </div>
                </div>

                {validationReport.details.errors.length > 0 && (
                  <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
                    <h4 className="font-semibold text-red-400 mb-2">Errors ({validationReport.details.errors.length})</h4>
                    <div className="text-sm text-red-300 space-y-1 max-h-32 overflow-y-auto">
                      {validationReport.details.errors.slice(0, 5).map((error, index) => (
                        <div key={index}>• {error}</div>
                      ))}
                      {validationReport.details.errors.length > 5 && (
                        <div>... and {validationReport.details.errors.length - 5} more</div>
                      )}
                    </div>
                  </div>
                )}

                {validationReport.recommendations.length > 0 && (
                  <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-400 mb-2">Recommendations</h4>
                    <div className="text-sm text-yellow-300 space-y-1">
                      {validationReport.recommendations.map((rec, index) => (
                        <div key={index}>• {rec}</div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex space-x-3">
                  <Button
                    onClick={validateData}
                    disabled={isLoading}
                    loading={isLoading}
                  >
                    Validate Data
                  </Button>
                  <Button
                    onClick={autoFixIssues}
                    disabled={isLoading}
                    variant="outline"
                  >
                    Auto-Fix Issues
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-foreground-muted">Click "Validate Data" to check data quality.</p>
                <Button
                  onClick={validateData}
                  disabled={isLoading}
                  loading={isLoading}
                >
                  Validate Data
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Additional Tools */}
        <div className="mt-8 bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-bold text-foreground mb-4">Additional Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-background-secondary rounded-lg p-4">
              <h3 className="font-semibold text-foreground mb-2">Data Migration</h3>
              <p className="text-sm text-foreground-muted mb-3">
                Migrate GPU data to new formats and fix structural issues.
              </p>
              <Button size="sm" variant="outline" disabled>
                Coming Soon
              </Button>
            </div>
            <div className="bg-background-secondary rounded-lg p-4">
              <h3 className="font-semibold text-foreground mb-2">Backup & Restore</h3>
              <p className="text-sm text-foreground-muted mb-3">
                Create backups and restore GPU data from previous versions.
              </p>
              <Button size="sm" variant="outline" disabled>
                Coming Soon
              </Button>
            </div>
            <div className="bg-background-secondary rounded-lg p-4">
              <h3 className="font-semibold text-foreground mb-2">Data Import</h3>
              <p className="text-sm text-foreground-muted mb-3">
                Import GPU data from Excel files or external APIs.
              </p>
              <Button size="sm" variant="outline" disabled>
                Coming Soon
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}