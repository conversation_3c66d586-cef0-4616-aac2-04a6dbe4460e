import { Layout } from '@/components/layout/Layout';
import { But<PERSON> } from '@/components/ui/Button';
import { GPUCard } from '@/components/ui/GPUCard';

// This will be replaced with real data from API calls

export default function Home() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background-secondary to-background">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-foreground mb-6">
              Find Your Perfect
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary via-primary-light to-primary">
                GPU Match
              </span>
            </h1>
            <p className="text-xl text-foreground-muted max-w-3xl mx-auto mb-8">
              The definitive platform for GPU research, recommendations, and community insights. 
              Make informed decisions with our comprehensive database and intelligent recommendation engine.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8 py-4">
                Get GPU Recommendations
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4">
                Browse All GPUs
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-background-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Everything You Need to Choose the Right GPU
            </h2>
            <p className="text-xl text-foreground-muted max-w-2xl mx-auto">
              From intelligent recommendations to real-time pricing, we've got you covered.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Smart Recommendations',
                description: 'AI-powered recommendations based on your workload, budget, and system requirements.',
                icon: '🎯'
              },
              {
                title: 'Real-time Pricing',
                description: 'Live pricing data from multiple retailers with price alerts and trend analysis.',
                icon: '💰'
              },
              {
                title: 'Compatibility Check',
                description: 'Verify GPU compatibility with your system components before you buy.',
                icon: '✅'
              },
              {
                title: 'Performance Analysis',
                description: 'Comprehensive benchmarks and performance data across different workloads.',
                icon: '📊'
              },
              {
                title: 'Community Reviews',
                description: 'Real-world insights and reviews from the GPU enthusiast community.',
                icon: '👥'
              },
              {
                title: 'Future-Proofing',
                description: 'Make decisions that will keep your system relevant for years to come.',
                icon: '🚀'
              }
            ].map((feature, index) => (
              <div key={index} className="bg-card border border-border rounded-xl p-6 hover:border-primary/30 transition-all duration-300">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-foreground mb-2">{feature.title}</h3>
                <p className="text-foreground-muted">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured GPUs Section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Popular GPUs Right Now
            </h2>
            <p className="text-xl text-foreground-muted">
              Check out the most popular graphics cards in our database
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sampleGPUs.map((gpu, index) => (
              <GPUCard
                key={gpu.id}
                gpu={gpu}
                pricing={samplePricing[index]}
              />
            ))}
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              View All GPUs
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            Ready to Find Your Perfect GPU?
          </h2>
          <p className="text-xl text-foreground-muted mb-8">
            Get personalized recommendations in minutes with our intelligent recommendation wizard.
          </p>
          <Button size="lg" className="text-lg px-8 py-4">
            Start Recommendation Wizard
          </Button>
        </div>
      </section>
    </Layout>
  );
}