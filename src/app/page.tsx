import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { GPUCard } from '@/components/ui/GPUCard';

// Sample GPU data for homepage display
const sampleGPUs = [
  {
    id: 'rtx-4090',
    name: 'GeForce RTX 4090',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: '2022-10-12',
    originalMsrp: 1599,
    specifications: {
      memory: { size: 24, type: 'GDDR6X', bandwidth: 1008, busWidth: 384 },
      cores: { cudaCores: 16384, rtCores: 128, tensorCores: 512 },
      clocks: { baseClock: 2230, boostClock: 2520, memoryClock: 21000 }
    },
    physicalSpecs: {
      dimensions: { length: 304, width: 137, height: 61 },
      slots: 3
    },
    powerRequirements: { tdp: 450, recommendedPSU: 850 },
    currentPrice: 1599,
    averageRating: 4.8,
    reviewCount: 1247
  },
  {
    id: 'rtx-4070',
    name: 'GeForce RTX 4070',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: '2023-04-13',
    originalMsrp: 599,
    specifications: {
      memory: { size: 12, type: 'GDDR6X', bandwidth: 504, busWidth: 192 },
      cores: { cudaCores: 5888, rtCores: 46, tensorCores: 184 },
      clocks: { baseClock: 1920, boostClock: 2475, memoryClock: 21000 }
    },
    physicalSpecs: {
      dimensions: { length: 240, width: 110, height: 40 },
      slots: 2
    },
    powerRequirements: { tdp: 200, recommendedPSU: 550 },
    currentPrice: 599,
    averageRating: 4.6,
    reviewCount: 892
  },
  {
    id: 'rx-7900-xtx',
    name: 'Radeon RX 7900 XTX',
    manufacturer: 'AMD',
    architecture: 'RDNA 3',
    launchDate: '2022-12-13',
    originalMsrp: 999,
    specifications: {
      memory: { size: 24, type: 'GDDR6', bandwidth: 960, busWidth: 384 },
      cores: { streamProcessors: 6144, computeUnits: 96 },
      clocks: { baseClock: 1855, boostClock: 2500, memoryClock: 20000 }
    },
    physicalSpecs: {
      dimensions: { length: 287, width: 120, height: 51 },
      slots: 2.5
    },
    powerRequirements: { tdp: 355, recommendedPSU: 700 },
    currentPrice: 999,
    averageRating: 4.4,
    reviewCount: 634
  }
];

// Sample pricing data for homepage display
const samplePricing = [
  {
    price: 1599,
    retailer: 'Newegg',
    availability: 'in-stock',
    url: 'https://newegg.com',
    lastUpdated: '2024-01-15T10:30:00Z'
  },
  {
    price: 599,
    retailer: 'Amazon',
    availability: 'in-stock',
    url: 'https://amazon.com',
    lastUpdated: '2024-01-15T09:15:00Z'
  },
  {
    price: 999,
    retailer: 'Best Buy',
    availability: 'limited',
    url: 'https://bestbuy.com',
    lastUpdated: '2024-01-15T08:45:00Z'
  }
];

export default function Home() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background-secondary to-background">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-foreground mb-6">
              Find Your Perfect
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary via-primary-light to-primary">
                GPU Match
              </span>
            </h1>
            <p className="text-xl text-foreground-muted max-w-3xl mx-auto mb-8">
              The definitive platform for GPU research, recommendations, and community insights. 
              Make informed decisions with our comprehensive database and intelligent recommendation engine.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8 py-4">
                Get GPU Recommendations
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4">
                Browse All GPUs
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-background-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Everything You Need to Choose the Right GPU
            </h2>
            <p className="text-xl text-foreground-muted max-w-2xl mx-auto">
              From intelligent recommendations to real-time pricing, we've got you covered.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Smart Recommendations',
                description: 'AI-powered recommendations based on your workload, budget, and system requirements.',
                icon: '🎯'
              },
              {
                title: 'Real-time Pricing',
                description: 'Live pricing data from multiple retailers with price alerts and trend analysis.',
                icon: '💰'
              },
              {
                title: 'Compatibility Check',
                description: 'Verify GPU compatibility with your system components before you buy.',
                icon: '✅'
              },
              {
                title: 'Performance Analysis',
                description: 'Comprehensive benchmarks and performance data across different workloads.',
                icon: '📊'
              },
              {
                title: 'Community Reviews',
                description: 'Real-world insights and reviews from the GPU enthusiast community.',
                icon: '👥'
              },
              {
                title: 'Future-Proofing',
                description: 'Make decisions that will keep your system relevant for years to come.',
                icon: '🚀'
              }
            ].map((feature, index) => (
              <div key={index} className="bg-card border border-border rounded-xl p-6 hover:border-primary/30 transition-all duration-300">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-foreground mb-2">{feature.title}</h3>
                <p className="text-foreground-muted">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured GPUs Section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Popular GPUs Right Now
            </h2>
            <p className="text-xl text-foreground-muted">
              Check out the most popular graphics cards in our database
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sampleGPUs.map((gpu, index) => (
              <GPUCard
                key={gpu.id}
                gpu={gpu}
                pricing={samplePricing[index]}
              />
            ))}
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              View All GPUs
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            Ready to Find Your Perfect GPU?
          </h2>
          <p className="text-xl text-foreground-muted mb-8">
            Get personalized recommendations in minutes with our intelligent recommendation wizard.
          </p>
          <Button size="lg" className="text-lg px-8 py-4">
            Start Recommendation Wizard
          </Button>
        </div>
      </section>
    </Layout>
  );
}