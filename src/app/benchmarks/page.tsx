'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'

interface GPU {
  id: string
  name: string
  manufacturer: string
  architecture: string
}

interface Benchmark {
  id: string
  testName: string
  resolution: string
  settings: string
  fps: number | null
  score: number | null
  source: string
  testDate: string
  gpu: GPU
}

interface ComparisonResult {
  gpu: GPU & {
    originalMsrp: number
    currentPrice: number | null
  }
  performance: {
    averageFps: number | null
    averageScore: number | null
    performancePerDollar: number | null
    benchmarkCount: number
  }
  benchmarks: Array<{
    testName: string
    fps: number | null
    score: number | null
    settings: string
    source: string
    testDate: string
  }>
}

export default function BenchmarksPage() {
  const searchParams = useSearchParams()
  const [benchmarks, setBenchmarks] = useState<Benchmark[]>([])
  const [comparison, setComparison] = useState<ComparisonResult[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGPUs, setSelectedGPUs] = useState<string[]>([])
  const [availableGPUs, setAvailableGPUs] = useState<GPU[]>([])
  const [benchmarkType, setBenchmarkType] = useState('gaming')
  const [resolution, setResolution] = useState('1080p')
  const [viewMode, setViewMode] = useState<'list' | 'compare'>('list')

  // Initialize from URL parameters
  useEffect(() => {
    const compareParam = searchParams.get('compare')
    const viewModeParam = searchParams.get('viewMode')

    if (compareParam) {
      const gpuIds = compareParam.split(',').filter(id => id.trim())
      setSelectedGPUs(gpuIds)
    }

    if (viewModeParam === 'compare') {
      setViewMode('compare')
    }
  }, [searchParams])

  useEffect(() => {
    fetchAvailableGPUs()
    if (viewMode === 'list') {
      fetchBenchmarks()
    }
  }, [benchmarkType, resolution, viewMode])

  useEffect(() => {
    if (viewMode === 'compare' && selectedGPUs.length > 0) {
      fetchComparison()
    }
  }, [selectedGPUs, benchmarkType, resolution, viewMode])

  const fetchAvailableGPUs = async () => {
    try {
      const response = await fetch('/api/gpus?limit=100')
      if (response.ok) {
        const data = await response.json()
        setAvailableGPUs(data.gpus || [])
      }
    } catch (error) {
      console.error('Error fetching GPUs:', error)
    }
  }

  const fetchBenchmarks = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        type: benchmarkType,
        resolution,
        limit: '50'
      })

      const response = await fetch(`/api/benchmarks?${params}`)
      if (response.ok) {
        const data = await response.json()
        setBenchmarks(data.benchmarks || [])
      }
    } catch (error) {
      console.error('Error fetching benchmarks:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchComparison = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        gpuIds: selectedGPUs.join(','),
        type: benchmarkType,
        resolution
      })

      const response = await fetch(`/api/benchmarks/compare?${params}`)
      if (response.ok) {
        const data = await response.json()
        setComparison(data.comparison || [])
      }
    } catch (error) {
      console.error('Error fetching comparison:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGPUSelection = (gpuId: string) => {
    setSelectedGPUs(prev => {
      if (prev.includes(gpuId)) {
        return prev.filter(id => id !== gpuId)
      } else if (prev.length < 5) {
        return [...prev, gpuId]
      }
      return prev
    })
  }

  const formatNumber = (num: number | null | undefined) => {
    if (num === null || num === undefined || typeof num !== 'number') return 'N/A'
    return num.toFixed(1)
  }

  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || typeof amount !== 'number') return 'N/A'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  if (loading && benchmarks.length === 0 && comparison.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-card rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="h-16 bg-card rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">GPU Benchmarks</h1>
          <p className="text-foreground-muted">
            Compare GPU performance across different games and applications
          </p>
        </div>

        {/* Controls */}
        <div className="bg-card border border-border rounded-lg p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            {/* View Mode */}
            <div className="flex bg-card-hover rounded-lg p-1">
              <button
                onClick={() => setViewMode('list')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'list'
                    ? 'bg-accent text-background'
                    : 'text-foreground-muted hover:text-foreground'
                }`}
              >
                Browse All
              </button>
              <button
                onClick={() => setViewMode('compare')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'compare'
                    ? 'bg-accent text-background'
                    : 'text-foreground-muted hover:text-foreground'
                }`}
              >
                Compare GPUs
              </button>
            </div>

            {/* Benchmark Type */}
            <select
              value={benchmarkType}
              onChange={(e) => setBenchmarkType(e.target.value)}
              className="bg-card border border-border rounded-lg px-4 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
            >
              <option value="gaming">Gaming</option>
              <option value="professional">Professional</option>
              <option value="synthetic">Synthetic</option>
            </select>

            {/* Resolution */}
            <select
              value={resolution}
              onChange={(e) => setResolution(e.target.value)}
              className="bg-card border border-border rounded-lg px-4 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
            >
              <option value="1080p">1080p</option>
              <option value="1440p">1440p</option>
              <option value="4K">4K</option>
            </select>
          </div>

          {/* GPU Selection for Comparison */}
          {viewMode === 'compare' && (
            <div>
              <h3 className="text-lg font-medium text-foreground mb-3">
                Select GPUs to Compare (max 5)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                {availableGPUs.map((gpu) => (
                  <label
                    key={gpu.id}
                    className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedGPUs.includes(gpu.id)
                        ? 'border-accent bg-accent/10'
                        : 'border-border hover:border-accent/50'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedGPUs.includes(gpu.id)}
                      onChange={() => handleGPUSelection(gpu.id)}
                      disabled={!selectedGPUs.includes(gpu.id) && selectedGPUs.length >= 5}
                      className="w-4 h-4 text-accent bg-card border-border rounded focus:ring-accent"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-foreground truncate">
                        {gpu.name}
                      </div>
                      <div className="text-xs text-foreground-muted">
                        {gpu.manufacturer} • {gpu.architecture}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        {viewMode === 'list' ? (
          /* Benchmark List */
          <div className="space-y-4">
            {benchmarks.map((benchmark) => (
              <div
                key={benchmark.id}
                className="bg-card border border-border rounded-lg p-4"
              >
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h3 className="font-medium text-foreground">
                      {benchmark.gpu.name}
                    </h3>
                    <p className="text-sm text-foreground-muted">
                      {benchmark.gpu.manufacturer} • {benchmark.gpu.architecture}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-accent">
                      {benchmark.fps ? `${formatNumber(benchmark.fps)} FPS` : 
                       benchmark.score ? `${benchmark.score} pts` : 'N/A'}
                    </div>
                    <div className="text-xs text-foreground-muted">
                      {benchmark.testName}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm text-foreground-muted">
                  <span>{benchmark.resolution} • {benchmark.settings}</span>
                  <span>{benchmark.source}</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Comparison View */
          <div>
            {comparison.length > 0 ? (
              <div className="space-y-6">
                {/* Performance Chart */}
                <div className="bg-card border border-border rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4">
                    Performance Comparison
                  </h3>
                  <div className="space-y-4">
                    {comparison.map((result, index) => (
                      <div key={result.gpu.id} className="flex items-center space-x-4">
                        <div className="w-4 text-sm font-medium text-foreground-muted">
                          #{index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium text-foreground">
                              {result.gpu.name}
                            </span>
                            <span className="text-accent font-semibold">
                              {formatNumber(result.performance.averageFps)} FPS
                            </span>
                          </div>
                          <div className="w-full bg-card-hover rounded-full h-2">
                            <div
                              className="bg-accent h-2 rounded-full transition-all duration-500"
                              style={{
                                width: `${Math.min(100, ((result.performance.averageFps || 0) / (comparison[0]?.performance.averageFps || 1)) * 100)}%`
                              }}
                            ></div>
                          </div>
                          <div className="flex items-center justify-between mt-1 text-xs text-foreground-muted">
                            <span>{result.performance.benchmarkCount} benchmarks</span>
                            {result.performance.performancePerDollar && (
                              <span>
                                {formatNumber(result.performance.performancePerDollar)} FPS/$
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Detailed Results */}
                <div className="bg-card border border-border rounded-lg overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-card-hover">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-foreground-muted uppercase tracking-wider">
                            GPU
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-foreground-muted uppercase tracking-wider">
                            Avg FPS
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-foreground-muted uppercase tracking-wider">
                            Current Price
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-foreground-muted uppercase tracking-wider">
                            FPS/$
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-foreground-muted uppercase tracking-wider">
                            Benchmarks
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-border">
                        {comparison.map((result) => (
                          <tr key={result.gpu.id} className="hover:bg-card-hover">
                            <td className="px-6 py-4">
                              <div>
                                <div className="text-sm font-medium text-foreground">
                                  {result.gpu.name}
                                </div>
                                <div className="text-sm text-foreground-muted">
                                  {result.gpu.manufacturer}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm text-foreground">
                              {formatNumber(result.performance.averageFps)}
                            </td>
                            <td className="px-6 py-4 text-sm text-foreground">
                              {formatCurrency(result.gpu.currentPrice)}
                            </td>
                            <td className="px-6 py-4 text-sm text-foreground">
                              {formatNumber(result.performance.performancePerDollar)}
                            </td>
                            <td className="px-6 py-4 text-sm text-foreground-muted">
                              {result.performance.benchmarkCount}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            ) : selectedGPUs.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-foreground-muted">
                  Select GPUs above to start comparing performance
                </p>
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-foreground-muted">Loading comparison...</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}