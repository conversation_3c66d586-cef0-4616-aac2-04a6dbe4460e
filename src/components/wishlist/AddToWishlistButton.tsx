'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'

interface AddToWishlistButtonProps {
  gpuId: string
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
}

export function AddToWishlistButton({ 
  gpuId, 
  className, 
  size = 'md',
  variant = 'outline'
}: AddToWishlistButtonProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isInWishlist, setIsInWishlist] = useState(false)
  const [showPriceInput, setShowPriceInput] = useState(false)
  const [targetPrice, setTargetPrice] = useState('')

  const handleAddToWishlist = async () => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (!showPriceInput) {
      setShowPriceInput(true)
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/user/wishlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          gpuId,
          targetPrice: targetPrice ? parseFloat(targetPrice) : null
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to add to wishlist')
      }

      setIsInWishlist(true)
      setShowPriceInput(false)
      setTargetPrice('')
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to add to wishlist')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveFromWishlist = async () => {
    // This would require fetching the wishlist item ID first
    // For now, we'll just redirect to the wishlist page
    router.push('/wishlist')
  }

  if (isInWishlist) {
    return (
      <Button
        variant="primary"
        size={size}
        className={className}
        onClick={handleRemoveFromWishlist}
      >
        ❤️ In Wishlist
      </Button>
    )
  }

  if (showPriceInput) {
    return (
      <div className="flex gap-2 items-center">
        <input
          type="number"
          placeholder="Target price ($)"
          value={targetPrice}
          onChange={(e) => setTargetPrice(e.target.value)}
          className="input flex-1 text-sm"
          min="0"
          step="0.01"
        />
        <Button
          variant="primary"
          size={size}
          onClick={handleAddToWishlist}
          loading={isLoading}
          disabled={isLoading}
        >
          Add
        </Button>
        <Button
          variant="outline"
          size={size}
          onClick={() => setShowPriceInput(false)}
        >
          Cancel
        </Button>
      </div>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleAddToWishlist}
    >
      🤍 Add to Wishlist
    </Button>
  )
}