import React from 'react';
import { Navigation } from './Navigation';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { CompactAffiliateDisclosure } from '../ui/AffiliateDisclosure';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Navigation />
      <ErrorBoundary>
        <main className="flex-1">
          {children}
        </main>
      </ErrorBoundary>
      <Footer />
    </div>
  );
}

function Footer() {
  return (
    <footer className="bg-background-secondary border-t border-border mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
                <span className="text-black font-bold text-lg">G</span>
              </div>
              <span className="text-xl font-bold text-foreground">
                GPU<span className="text-primary">Labs</span>
              </span>
            </div>
            <p className="text-foreground-muted max-w-md">
              The definitive platform for GPU research, recommendations, and community insights. 
              Make informed decisions with our comprehensive database and intelligent recommendation engine.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
              Platform
            </h3>
            <ul className="space-y-2">
              <li><a href="/gpus" className="text-foreground-muted hover:text-primary transition-colors">Browse GPUs</a></li>
              <li><a href="/recommendations" className="text-foreground-muted hover:text-primary transition-colors">Get Recommendations</a></li>
              <li><a href="/compare" className="text-foreground-muted hover:text-primary transition-colors">Compare GPUs</a></li>
              <li><a href="/compatibility" className="text-foreground-muted hover:text-primary transition-colors">Check Compatibility</a></li>
            </ul>
          </div>

          {/* Community */}
          <div>
            <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
              Community
            </h3>
            <ul className="space-y-2">
              <li><a href="/community" className="text-foreground-muted hover:text-primary transition-colors">Forums</a></li>
              <li><a href="/community/reviews" className="text-foreground-muted hover:text-primary transition-colors">Reviews</a></li>
              <li><a href="/community/builds" className="text-foreground-muted hover:text-primary transition-colors">Build Showcase</a></li>
              <li><a href="/support" className="text-foreground-muted hover:text-primary transition-colors">Support</a></li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-border">
          {/* Affiliate Disclosure */}
          <CompactAffiliateDisclosure className="mb-6" />

          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-foreground-muted text-sm">
              © 2024 GPULabs. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="/privacy" className="text-foreground-muted hover:text-primary text-sm transition-colors">
                Privacy Policy
              </a>
              <a href="/terms" className="text-foreground-muted hover:text-primary text-sm transition-colors">
                Terms of Service
              </a>
              <a href="/api" className="text-foreground-muted hover:text-primary text-sm transition-colors">
                API
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}