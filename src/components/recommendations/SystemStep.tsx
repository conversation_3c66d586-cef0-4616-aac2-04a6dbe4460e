import React from 'react';
import { But<PERSON> } from '@/components/ui/Button';
import { RecommendationCriteria } from './RecommendationWizard';

interface SystemStepProps {
  criteria: RecommendationCriteria;
  updateCriteria: (updates: Partial<RecommendationCriteria>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export function SystemStep({ criteria, updateCriteria, onNext, onPrev }: SystemStepProps) {
  const updateSystemInfo = (key: string, value: any) => {
    updateCriteria({
      currentSystem: {
        ...criteria.currentSystem,
        [key]: value
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Current System
        </h2>
        <p className="text-foreground-muted">
          Tell us about your current system for compatibility checking
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            CPU (Optional)
          </label>
          <input
            type="text"
            value={criteria.currentSystem?.cpu || ''}
            onChange={(e) => updateSystemInfo('cpu', e.target.value)}
            placeholder="e.g., Intel i7-12700K, AMD Ryzen 7 5800X"
            className="w-full px-4 py-3 bg-background-secondary border border-border rounded-lg text-foreground focus:border-primary focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Power Supply (Watts)
          </label>
          <input
            type="number"
            value={criteria.currentSystem?.psu || ''}
            onChange={(e) => updateSystemInfo('psu', parseInt(e.target.value) || 0)}
            placeholder="e.g., 750"
            className="w-full px-4 py-3 bg-background-secondary border border-border rounded-lg text-foreground focus:border-primary focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            PC Case
          </label>
          <input
            type="text"
            value={criteria.currentSystem?.case || ''}
            onChange={(e) => updateSystemInfo('case', e.target.value)}
            placeholder="e.g., Mid Tower, Full Tower"
            className="w-full px-4 py-3 bg-background-secondary border border-border rounded-lg text-foreground focus:border-primary focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Motherboard
          </label>
          <input
            type="text"
            value={criteria.currentSystem?.motherboard || ''}
            onChange={(e) => updateSystemInfo('motherboard', e.target.value)}
            placeholder="e.g., ASUS ROG Strix B550-F"
            className="w-full px-4 py-3 bg-background-secondary border border-border rounded-lg text-foreground focus:border-primary focus:outline-none"
          />
        </div>
      </div>

      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="text-blue-400 text-xl">ℹ️</div>
          <div>
            <div className="font-medium text-foreground mb-1">
              System Information is Optional
            </div>
            <div className="text-sm text-foreground-muted">
              Providing your current system specs helps us check compatibility and avoid bottlenecks, 
              but you can skip this step if you prefer.
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button onClick={onPrev} variant="outline" size="lg" className="px-8">
          Back
        </Button>
        <Button onClick={onNext} size="lg" className="px-8">
          Continue
        </Button>
      </div>
    </div>
  );
}