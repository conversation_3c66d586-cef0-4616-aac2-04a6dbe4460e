import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { cn, formatPrice } from '@/lib/utils';
import { RecommendationCriteria } from './RecommendationWizard';

interface BudgetStepProps {
  criteria: RecommendationCriteria;
  updateCriteria: (updates: Partial<RecommendationCriteria>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const budgetPresets = [
  { label: 'Budget', min: 200, max: 500, description: 'Entry-level gaming and basic tasks' },
  { label: 'Mid-Range', min: 500, max: 800, description: 'Solid 1440p gaming performance' },
  { label: 'High-End', min: 800, max: 1500, description: '4K gaming and professional work' },
  { label: 'Enthusiast', min: 1500, max: 3000, description: 'Ultimate performance and future-proofing' },
  { label: 'Custom', min: 0, max: 0, description: 'Set your own budget range' }
];

export function BudgetStep({ criteria, updateCriteria, onNext, onPrev }: BudgetStepProps) {
  const [selectedPreset, setSelectedPreset] = useState<string>('');
  const [customMin, setCustomMin] = useState(500);
  const [customMax, setCustomMax] = useState(1000);
  const [isCustom, setIsCustom] = useState(false);

  useEffect(() => {
    if (criteria.budget) {
      const { min, max } = criteria.budget;
      const preset = budgetPresets.find(p => p.min === min && p.max === max);
      if (preset && preset.label !== 'Custom') {
        setSelectedPreset(preset.label);
        setIsCustom(false);
      } else {
        setSelectedPreset('Custom');
        setCustomMin(min);
        setCustomMax(max);
        setIsCustom(true);
      }
    }
  }, [criteria.budget]);

  const handlePresetSelect = (preset: typeof budgetPresets[0]) => {
    setSelectedPreset(preset.label);
    if (preset.label === 'Custom') {
      setIsCustom(true);
      updateCriteria({ budget: { min: customMin, max: customMax } });
    } else {
      setIsCustom(false);
      updateCriteria({ budget: { min: preset.min, max: preset.max } });
    }
  };

  const handleCustomBudgetChange = () => {
    if (isCustom) {
      updateCriteria({ budget: { min: customMin, max: customMax } });
    }
  };

  useEffect(() => {
    if (isCustom) {
      handleCustomBudgetChange();
    }
  }, [customMin, customMax, isCustom]);

  const canProceed = criteria.budget && criteria.budget.min < criteria.budget.max;

  const getBudgetGuidance = () => {
    if (!criteria.budget) return '';
    
    const midpoint = (criteria.budget.min + criteria.budget.max) / 2;
    const workload = criteria.workload;
    
    if (workload === 'gaming') {
      if (midpoint < 400) return 'Good for 1080p gaming at high settings';
      if (midpoint < 800) return 'Excellent for 1440p gaming with ray tracing';
      if (midpoint < 1500) return 'Premium 4K gaming experience';
      return 'Ultimate 4K performance with all features enabled';
    }
    
    if (workload === 'content-creation') {
      if (midpoint < 600) return 'Basic video editing and streaming';
      if (midpoint < 1200) return '4K video editing and 3D rendering';
      if (midpoint < 2000) return 'Professional content creation workflows';
      return '8K editing and complex rendering tasks';
    }
    
    if (workload === 'ai-ml') {
      if (midpoint < 800) return 'Small model training and inference';
      if (midpoint < 1500) return 'Medium-scale model development';
      if (midpoint < 2500) return 'Large model training and research';
      return 'Enterprise-grade AI development';
    }
    
    return 'Performance appropriate for your budget range';
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          What's your budget?
        </h2>
        <p className="text-foreground-muted">
          Set your price range to get recommendations that fit your budget
        </p>
      </div>

      {/* Budget Presets */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {budgetPresets.map((preset) => (
          <button
            key={preset.label}
            onClick={() => handlePresetSelect(preset)}
            className={cn(
              'p-4 rounded-lg border-2 transition-all duration-200 text-left',
              selectedPreset === preset.label
                ? 'border-primary bg-primary/10'
                : 'border-border hover:border-primary/50 bg-background-secondary'
            )}
          >
            <div className="font-semibold text-foreground mb-1">
              {preset.label}
            </div>
            {preset.label !== 'Custom' && (
              <div className="text-primary font-medium mb-1">
                {formatPrice(preset.min)} - {formatPrice(preset.max)}
              </div>
            )}
            <div className="text-sm text-foreground-muted">
              {preset.description}
            </div>
          </button>
        ))}
      </div>

      {/* Custom Budget Inputs */}
      {isCustom && (
        <div className="bg-background-secondary rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Custom Budget Range
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Minimum Budget
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted">
                  $
                </span>
                <input
                  type="number"
                  value={customMin}
                  onChange={(e) => setCustomMin(parseInt(e.target.value) || 0)}
                  className="w-full pl-8 pr-4 py-3 bg-background border border-border rounded-lg text-foreground focus:border-primary focus:outline-none"
                  placeholder="500"
                  min="0"
                  step="50"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Maximum Budget
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted">
                  $
                </span>
                <input
                  type="number"
                  value={customMax}
                  onChange={(e) => setCustomMax(parseInt(e.target.value) || 0)}
                  className="w-full pl-8 pr-4 py-3 bg-background border border-border rounded-lg text-foreground focus:border-primary focus:outline-none"
                  placeholder="1000"
                  min="0"
                  step="50"
                />
              </div>
            </div>
          </div>
          
          {customMin >= customMax && (
            <div className="mt-3 text-red-400 text-sm">
              Maximum budget must be higher than minimum budget
            </div>
          )}
        </div>
      )}

      {/* Budget Guidance */}
      {criteria.budget && (
        <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="text-primary text-xl">💡</div>
            <div>
              <div className="font-medium text-foreground mb-1">
                Budget Range: {formatPrice(criteria.budget.min)} - {formatPrice(criteria.budget.max)}
              </div>
              <div className="text-sm text-foreground-muted">
                {getBudgetGuidance()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          onClick={onPrev}
          variant="outline"
          size="lg"
          className="px-8"
        >
          Back
        </Button>
        <Button
          onClick={onNext}
          disabled={!canProceed}
          size="lg"
          className="px-8"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}