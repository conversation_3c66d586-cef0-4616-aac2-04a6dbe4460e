import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { GPUCard } from '@/components/ui/GPUCard';
import { Loading } from '@/components/ui/Loading';
import { formatPrice } from '@/lib/utils';
import { RecommendationCriteria } from './RecommendationWizard';

interface ResultsStepProps {
  criteria: RecommendationCriteria;
  onRestart: () => void;
}

interface GPURecommendation {
  gpu: {
    id: string;
    name: string;
    manufacturer: string;
    architecture: string;
    specifications: {
      memory: { size: number; type: string };
      power: { tdp: number };
      cores: { cuda?: number; stream?: number };
    };
  };
  score: {
    overall: number;
    performance: number;
    value: number;
    futureProofing: number;
  };
  pricing: {
    price: number;
    retailer: string;
    availability: 'in-stock' | 'limited' | 'out-of-stock';
  };
  pros: string[];
  cons: string[];
  reasoning: string[];
}

export function ResultsStep({ criteria, onRestart }: ResultsStepProps) {
  const [recommendations, setRecommendations] = useState<GPURecommendation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock API call to generate recommendations
    const generateMockRecommendations = () => {
      const mockRecommendations: GPURecommendation[] = [
        {
          gpu: {
            id: '1',
            name: 'GeForce RTX 4070 Ti',
            manufacturer: 'NVIDIA',
            architecture: 'Ada Lovelace',
            specifications: {
              memory: { size: 12, type: 'GDDR6X' },
              power: { tdp: 285 },
              cores: { cuda: 7680 }
            }
          },
          score: {
            overall: 92,
            performance: 88,
            value: 95,
            futureProofing: 90
          },
          pricing: {
            price: 799,
            retailer: 'Best Buy',
            availability: 'in-stock'
          },
          pros: [
            'Excellent 1440p gaming performance',
            'Great value for money',
            'DLSS 3 support',
            'Efficient power consumption'
          ],
          cons: [
            'Limited to 12GB VRAM',
            'May struggle with 4K in demanding games'
          ],
          reasoning: [
            'Perfect match for your gaming workload and budget',
            'Excellent price-to-performance ratio',
            'Future-proof with latest architecture'
          ]
        },
        {
          gpu: {
            id: '2',
            name: 'Radeon RX 7800 XT',
            manufacturer: 'AMD',
            architecture: 'RDNA 3',
            specifications: {
              memory: { size: 16, type: 'GDDR6' },
              power: { tdp: 263 },
              cores: { stream: 3840 }
            }
          },
          score: {
            overall: 89,
            performance: 85,
            value: 92,
            futureProofing: 88
          },
          pricing: {
            price: 499,
            retailer: 'Amazon',
            availability: 'in-stock'
          },
          pros: [
            'More VRAM than competitors',
            'Excellent value proposition',
            'Good power efficiency',
            'Strong 1440p performance'
          ],
          cons: [
            'No DLSS support',
            'Ray tracing performance behind NVIDIA'
          ],
          reasoning: [
            'Great alternative with more VRAM',
            'Excellent value in your budget range',
            'Strong performance for the price'
          ]
        },
        {
          gpu: {
            id: '3',
            name: 'GeForce RTX 4060 Ti',
            manufacturer: 'NVIDIA',
            architecture: 'Ada Lovelace',
            specifications: {
              memory: { size: 8, type: 'GDDR6' },
              power: { tdp: 165 },
              cores: { cuda: 4352 }
            }
          },
          score: {
            overall: 85,
            performance: 80,
            value: 88,
            futureProofing: 82
          },
          pricing: {
            price: 399,
            retailer: 'Newegg',
            availability: 'limited'
          },
          pros: [
            'Very power efficient',
            'Great for 1080p gaming',
            'DLSS 3 support',
            'Affordable price point'
          ],
          cons: [
            'Only 8GB VRAM',
            'Limited 4K performance',
            'Narrow memory bus'
          ],
          reasoning: [
            'Budget-friendly option in your range',
            'Good for 1080p high refresh gaming',
            'Efficient and quiet operation'
          ]
        }
      ];

      setTimeout(() => {
        setRecommendations(mockRecommendations);
        setLoading(false);
      }, 1500);
    };

    generateMockRecommendations();
  }, [criteria]);

  if (loading) {
    return (
      <div className="text-center py-12">
        <Loading size="lg" text="Analyzing your requirements and generating personalized recommendations..." />
      </div>
    );
  }

  const budgetRange = criteria.budget ? `${formatPrice(criteria.budget.min)} - ${formatPrice(criteria.budget.max)}` : 'Not specified';

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Your Personalized Recommendations
        </h2>
        <p className="text-foreground-muted">
          Based on your {criteria.workload} workload and {budgetRange} budget
        </p>
      </div>

      {/* Summary */}
      <div className="bg-primary/10 border border-primary/20 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Recommendation Summary
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="text-foreground-muted">Workload</div>
            <div className="font-medium text-foreground capitalize">
              {criteria.workload?.replace('-', ' ')}
            </div>
          </div>
          <div>
            <div className="text-foreground-muted">Budget Range</div>
            <div className="font-medium text-foreground">{budgetRange}</div>
          </div>
          <div>
            <div className="text-foreground-muted">Results Found</div>
            <div className="font-medium text-foreground">{recommendations.length} GPUs</div>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="space-y-6">
        {recommendations.map((rec, index) => (
          <div key={rec.gpu.id} className="relative">
            {index === 0 && (
              <div className="absolute -top-3 left-6 bg-primary text-black px-3 py-1 rounded-full text-sm font-medium z-10">
                Best Match
              </div>
            )}
            
            <div className="bg-card border border-border rounded-xl p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* GPU Info */}
                <div className="lg:col-span-2">
                  <GPUCard
                    gpu={rec.gpu}
                    pricing={rec.pricing}
                    score={rec.score}
                    showScore={true}
                    onSelect={(id) => window.open(`/gpus/${id}`, '_blank')}
                    onCompare={(id) => console.log('Compare:', id)}
                  />
                </div>

                {/* Analysis */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">Why This GPU?</h4>
                    <ul className="text-sm text-foreground-muted space-y-1">
                      {rec.reasoning.map((reason, i) => (
                        <li key={i}>• {reason}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-foreground mb-2">Pros</h4>
                    <ul className="text-sm text-green-400 space-y-1">
                      {rec.pros.slice(0, 3).map((pro, i) => (
                        <li key={i}>✓ {pro}</li>
                      ))}
                    </ul>
                  </div>

                  {rec.cons.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">Considerations</h4>
                      <ul className="text-sm text-yellow-400 space-y-1">
                        {rec.cons.slice(0, 2).map((con, i) => (
                          <li key={i}>⚠ {con}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button onClick={onRestart} variant="outline" size="lg" className="px-8">
          Start Over
        </Button>
        <Button size="lg" className="px-8">
          Save Recommendations
        </Button>
        <Button variant="outline" size="lg" className="px-8">
          Compare All
        </Button>
      </div>
    </div>
  );
}