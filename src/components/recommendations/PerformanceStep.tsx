import React from 'react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { RecommendationCriteria } from './RecommendationWizard';

interface PerformanceStepProps {
  criteria: RecommendationCriteria;
  updateCriteria: (updates: Partial<RecommendationCriteria>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export function PerformanceStep({ criteria, updateCriteria, onNext, onPrev }: PerformanceStepProps) {
  const isGaming = criteria.workload === 'gaming';
  
  const updatePerformanceTarget = (key: string, value: any) => {
    updateCriteria({
      performanceTargets: {
        ...criteria.performanceTargets,
        [key]: value
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Performance Targets
        </h2>
        <p className="text-foreground-muted">
          {isGaming ? 'Set your gaming performance goals' : 'Define your performance requirements'}
        </p>
      </div>

      {isGaming && (
        <div className="space-y-6">
          {/* Resolution */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-3">
              Target Resolution
            </label>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
              {['1080p', '1440p', '4K', '8K'].map((res) => (
                <button
                  key={res}
                  onClick={() => updatePerformanceTarget('resolution', res)}
                  className={cn(
                    'p-3 rounded-lg border-2 transition-all',
                    criteria.performanceTargets?.resolution === res
                      ? 'border-primary bg-primary/10'
                      : 'border-border hover:border-primary/50'
                  )}
                >
                  <div className="font-medium text-foreground">{res}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Target FPS */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-3">
              Target Frame Rate
            </label>
            <div className="grid grid-cols-3 sm:grid-cols-5 gap-3">
              {[30, 60, 90, 120, 144].map((fps) => (
                <button
                  key={fps}
                  onClick={() => updatePerformanceTarget('targetFPS', fps)}
                  className={cn(
                    'p-3 rounded-lg border-2 transition-all',
                    criteria.performanceTargets?.targetFPS === fps
                      ? 'border-primary bg-primary/10'
                      : 'border-border hover:border-primary/50'
                  )}
                >
                  <div className="font-medium text-foreground">{fps} FPS</div>
                </button>
              ))}
            </div>
          </div>

          {/* Features */}
          <div className="space-y-3">
            {[
              { key: 'rayTracing', label: 'Ray Tracing', desc: 'Realistic lighting and reflections' },
              { key: 'dlss', label: 'DLSS/FSR', desc: 'AI upscaling for better performance' },
              { key: 'vr', label: 'VR Gaming', desc: 'Virtual reality compatibility' }
            ].map((feature) => (
              <label key={feature.key} className="flex items-center justify-between p-4 bg-background-secondary rounded-lg cursor-pointer">
                <div>
                  <div className="font-medium text-foreground">{feature.label}</div>
                  <div className="text-sm text-foreground-muted">{feature.desc}</div>
                </div>
                <input
                  type="checkbox"
                  checked={criteria.performanceTargets?.[feature.key as keyof typeof criteria.performanceTargets] || false}
                  onChange={(e) => updatePerformanceTarget(feature.key, e.target.checked)}
                  className="w-5 h-5 text-primary rounded focus:ring-primary"
                />
              </label>
            ))}
          </div>
        </div>
      )}

      {!isGaming && (
        <div className="text-center py-8">
          <div className="text-foreground-muted mb-4">
            Performance targets are automatically optimized for {criteria.workload} workloads
          </div>
          <div className="text-sm text-foreground-muted">
            We'll recommend GPUs based on the specific requirements of your chosen workload
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <Button onClick={onPrev} variant="outline" size="lg" className="px-8">
          Back
        </Button>
        <Button onClick={onNext} size="lg" className="px-8">
          Continue
        </Button>
      </div>
    </div>
  );
}