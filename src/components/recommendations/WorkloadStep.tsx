import React from 'react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { RecommendationCriteria, Workload } from './RecommendationWizard';

interface WorkloadStepProps {
  criteria: RecommendationCriteria;
  updateCriteria: (updates: Partial<RecommendationCriteria>) => void;
  onNext: () => void;
}

const workloads = [
  {
    id: 'gaming' as Workload,
    title: 'Gaming',
    description: 'High frame rates, visual quality, and smooth gameplay',
    icon: '🎮',
    features: ['High FPS', 'Ray Tracing', 'DLSS/FSR', '4K Gaming'],
    color: 'from-blue-500/20 to-purple-500/20 border-blue-500/30'
  },
  {
    id: 'content-creation' as Workload,
    title: 'Content Creation',
    description: 'Video editing, 3D rendering, and streaming',
    icon: '🎨',
    features: ['Large VRAM', 'NVENC/VCE', 'CUDA Cores', 'Professional Apps'],
    color: 'from-green-500/20 to-teal-500/20 border-green-500/30'
  },
  {
    id: 'ai-ml' as Workload,
    title: 'AI/ML Development',
    description: 'Machine learning, AI training, and data science',
    icon: '🤖',
    features: ['Tensor Cores', 'Large VRAM', 'Memory Bandwidth', 'CUDA Support'],
    color: 'from-orange-500/20 to-red-500/20 border-orange-500/30'
  },
  {
    id: 'mining' as Workload,
    title: 'Cryptocurrency Mining',
    description: 'Efficient mining with good ROI and power efficiency',
    icon: '⛏️',
    features: ['Hash Rate', 'Power Efficiency', 'ROI Calculation', 'Thermal Design'],
    color: 'from-yellow-500/20 to-amber-500/20 border-yellow-500/30'
  }
];

export function WorkloadStep({ criteria, updateCriteria, onNext }: WorkloadStepProps) {
  const handleWorkloadSelect = (workload: Workload) => {
    updateCriteria({ workload });
  };

  const canProceed = criteria.workload !== undefined;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          What's your primary use case?
        </h2>
        <p className="text-foreground-muted">
          Choose the workload that best describes how you'll use your GPU
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {workloads.map((workload) => (
          <button
            key={workload.id}
            onClick={() => handleWorkloadSelect(workload.id)}
            className={cn(
              'p-6 rounded-xl border-2 transition-all duration-200 text-left hover:scale-105',
              'bg-gradient-to-br',
              workload.color,
              criteria.workload === workload.id
                ? 'ring-2 ring-primary shadow-lg shadow-primary/20'
                : 'hover:border-primary/50'
            )}
          >
            <div className="flex items-start gap-4">
              <div className="text-4xl">{workload.icon}</div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-foreground mb-1">
                  {workload.title}
                </h3>
                <p className="text-sm text-foreground-muted mb-3">
                  {workload.description}
                </p>
                <div className="flex flex-wrap gap-1">
                  {workload.features.map((feature) => (
                    <span
                      key={feature}
                      className="px-2 py-1 bg-background-secondary rounded text-xs text-foreground-muted"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
              {criteria.workload === workload.id && (
                <div className="text-primary">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          </button>
        ))}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={onNext}
          disabled={!canProceed}
          size="lg"
          className="px-8"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}