import React from 'react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { RecommendationCriteria } from './RecommendationWizard';

interface PreferencesStepProps {
  criteria: RecommendationCriteria;
  updateCriteria: (updates: Partial<RecommendationCriteria>) => void;
  onNext: () => void;
  onPrev: () => void;
  isGenerating: boolean;
}

export function PreferencesStep({ criteria, updateCriteria, onNext, onPrev, isGenerating }: PreferencesStepProps) {
  const updatePriorities = (key: string, value: number) => {
    updateCriteria({
      priorities: {
        ...criteria.priorities,
        [key]: value
      }
    });
  };

  const updatePreferences = (key: string, value: any) => {
    updateCriteria({
      preferences: {
        ...criteria.preferences,
        [key]: value
      }
    });
  };

  const priorities = [
    { key: 'performance', label: 'Performance', desc: 'Raw computing power' },
    { key: 'value', label: 'Value', desc: 'Price-to-performance ratio' },
    { key: 'futureProofing', label: 'Future-Proofing', desc: 'Long-term relevance' },
    { key: 'powerEfficiency', label: 'Power Efficiency', desc: 'Low power consumption' }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Priorities & Preferences
        </h2>
        <p className="text-foreground-muted">
          Fine-tune your recommendations with your priorities and preferences
        </p>
      </div>

      {/* Priorities */}
      <div>
        <h3 className="text-lg font-semibold text-foreground mb-4">
          What matters most to you?
        </h3>
        <div className="space-y-4">
          {priorities.map((priority) => (
            <div key={priority.key} className="space-y-2">
              <div className="flex justify-between">
                <div>
                  <div className="font-medium text-foreground">{priority.label}</div>
                  <div className="text-sm text-foreground-muted">{priority.desc}</div>
                </div>
                <div className="text-primary font-medium">
                  {criteria.priorities?.[priority.key as keyof typeof criteria.priorities] || 25}%
                </div>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={criteria.priorities?.[priority.key as keyof typeof criteria.priorities] || 25}
                onChange={(e) => updatePriorities(priority.key, parseInt(e.target.value))}
                className="w-full h-2 bg-background-tertiary rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Brand Preference */}
      <div>
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Brand Preference
        </h3>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          {['any', 'NVIDIA', 'AMD', 'Intel'].map((brand) => (
            <button
              key={brand}
              onClick={() => updatePreferences('brand', brand)}
              className={cn(
                'p-3 rounded-lg border-2 transition-all',
                criteria.preferences?.brand === brand
                  ? 'border-primary bg-primary/10'
                  : 'border-border hover:border-primary/50'
              )}
            >
              <div className="font-medium text-foreground capitalize">{brand}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Additional Preferences */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-foreground mb-3">
          Additional Preferences
        </h3>
        
        <label className="flex items-center justify-between p-4 bg-background-secondary rounded-lg cursor-pointer">
          <div>
            <div className="font-medium text-foreground">New GPUs Only</div>
            <div className="text-sm text-foreground-muted">Only recommend recently released GPUs</div>
          </div>
          <input
            type="checkbox"
            checked={criteria.preferences?.newOnly || false}
            onChange={(e) => updatePreferences('newOnly', e.target.checked)}
            className="w-5 h-5 text-primary rounded focus:ring-primary"
          />
        </label>

        <label className="flex items-center justify-between p-4 bg-background-secondary rounded-lg cursor-pointer">
          <div>
            <div className="font-medium text-foreground">Quiet Operation</div>
            <div className="text-sm text-foreground-muted">Prioritize low-noise GPUs</div>
          </div>
          <input
            type="checkbox"
            checked={criteria.preferences?.quietOperation || false}
            onChange={(e) => updatePreferences('quietOperation', e.target.checked)}
            className="w-5 h-5 text-primary rounded focus:ring-primary"
          />
        </label>
      </div>

      <div className="flex justify-between">
        <Button onClick={onPrev} variant="outline" size="lg" className="px-8">
          Back
        </Button>
        <Button 
          onClick={onNext} 
          size="lg" 
          className="px-8"
          loading={isGenerating}
        >
          {isGenerating ? 'Generating Recommendations...' : 'Get Recommendations'}
        </Button>
      </div>
    </div>
  );
}