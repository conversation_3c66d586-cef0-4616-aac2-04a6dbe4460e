'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { WorkloadStep } from './WorkloadStep';
import { BudgetStep } from './BudgetStep';
import { PerformanceStep } from './PerformanceStep';
import { SystemStep } from './SystemStep';
import { PreferencesStep } from './PreferencesStep';
import { ResultsStep } from './ResultsStep';

export type Workload = 'gaming' | 'content-creation' | 'ai-ml' | 'mining';

export interface RecommendationCriteria {
  workload?: Workload;
  budget?: {
    min: number;
    max: number;
  };
  performanceTargets?: {
    resolution?: '1080p' | '1440p' | '4K' | '8K';
    targetFPS?: number;
    rayTracing?: boolean;
    dlss?: boolean;
    vr?: boolean;
  };
  currentSystem?: {
    cpu?: string;
    psu?: number;
    case?: string;
    motherboard?: string;
  };
  futureProofing?: number;
  priorities?: {
    performance?: number;
    value?: number;
    futureProofing?: number;
    powerEfficiency?: number;
  };
  preferences?: {
    brand?: 'NVIDIA' | 'AMD' | 'Intel' | 'any';
    newOnly?: boolean;
    maxPowerDraw?: number;
    quietOperation?: boolean;
  };
}

const steps = [
  { id: 'workload', title: 'Workload', description: 'What will you use your GPU for?' },
  { id: 'budget', title: 'Budget', description: 'Set your price range' },
  { id: 'performance', title: 'Performance', description: 'Define your performance targets' },
  { id: 'system', title: 'System', description: 'Tell us about your current system' },
  { id: 'preferences', title: 'Preferences', description: 'Set your priorities and preferences' },
  { id: 'results', title: 'Results', description: 'Your personalized recommendations' }
];

export function RecommendationWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [criteria, setCriteria] = useState<RecommendationCriteria>({});
  const [isGenerating, setIsGenerating] = useState(false);

  const updateCriteria = (updates: Partial<RecommendationCriteria>) => {
    setCriteria(prev => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const generateRecommendations = async () => {
    setIsGenerating(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsGenerating(false);
    nextStep();
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <WorkloadStep
            criteria={criteria}
            updateCriteria={updateCriteria}
            onNext={nextStep}
          />
        );
      case 1:
        return (
          <BudgetStep
            criteria={criteria}
            updateCriteria={updateCriteria}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 2:
        return (
          <PerformanceStep
            criteria={criteria}
            updateCriteria={updateCriteria}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 3:
        return (
          <SystemStep
            criteria={criteria}
            updateCriteria={updateCriteria}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 4:
        return (
          <PreferencesStep
            criteria={criteria}
            updateCriteria={updateCriteria}
            onNext={generateRecommendations}
            onPrev={prevStep}
            isGenerating={isGenerating}
          />
        );
      case 5:
        return (
          <ResultsStep
            criteria={criteria}
            onRestart={() => {
              setCriteria({});
              setCurrentStep(0);
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={cn(
                'flex items-center',
                index < steps.length - 1 && 'flex-1'
              )}
            >
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
                    index <= currentStep
                      ? 'bg-primary text-black'
                      : 'bg-background-tertiary text-foreground-muted'
                  )}
                >
                  {index + 1}
                </div>
                <div className="mt-2 text-center">
                  <div className={cn(
                    'text-sm font-medium',
                    index <= currentStep ? 'text-foreground' : 'text-foreground-muted'
                  )}>
                    {step.title}
                  </div>
                  <div className="text-xs text-foreground-muted hidden sm:block">
                    {step.description}
                  </div>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    'flex-1 h-0.5 mx-4 transition-colors',
                    index < currentStep ? 'bg-primary' : 'bg-background-tertiary'
                  )}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-card border border-border rounded-xl p-8">
        {renderStep()}
      </div>
    </div>
  );
}