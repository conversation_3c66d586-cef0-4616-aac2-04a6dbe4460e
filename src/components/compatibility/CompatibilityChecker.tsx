'use client'

import { useState } from 'react'
import { SystemSpecs, CompatibilityResult } from '@/types/gpu'

interface CompatibilityCheckerProps {
  gpuId?: string
  onCompatibilityCheck?: (result: CompatibilityResult) => void
}

interface FormData {
  gpuId: string
  cpu: {
    model: string
    cores: number
    threads: number
    baseClock: number
  }
  motherboard: {
    model: string
    chipset: string
    pcieSlots: string[]
  }
  psu: {
    wattage: number
    efficiency: string
    connectors: string[]
  }
  case: {
    model: string
    maxGPULength: number
    maxGPUWidth: number
    maxGPUHeight: number
  }
  ram: {
    size: number
    speed: number
    type: string
  }
}

const initialFormData: FormData = {
  gpuId: '',
  cpu: {
    model: '',
    cores: 4,
    threads: 8,
    baseClock: 3000
  },
  motherboard: {
    model: '',
    chipset: '',
    pcieSlots: ['PCIe 4.0 x16']
  },
  psu: {
    wattage: 650,
    efficiency: '80+ Gold',
    connectors: []
  },
  case: {
    model: '',
    maxGPULength: 320,
    maxGPUWidth: 140,
    maxGPUHeight: 60
  },
  ram: {
    size: 16,
    speed: 3200,
    type: 'DDR4'
  }
}

export default function CompatibilityChecker({
  gpuId,
  onCompatibilityCheck
}: CompatibilityCheckerProps) {
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData,
    gpuId: gpuId || ''
  })
  const [compatibilityResult, setCompatibilityResult] = useState<CompatibilityResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (section: keyof FormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...(prev[section] as any),
        [field]: value
      }
    }))
  }

  const handleArrayInputChange = (section: keyof FormData, field: string, value: string) => {
    const arrayValue = value.split(',').map(item => item.trim()).filter(item => item)
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...(prev[section] as any),
        [field]: arrayValue
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      const systemSpecs: SystemSpecs = {
        cpu: formData.cpu,
        motherboard: formData.motherboard,
        psu: formData.psu,
        case: formData.case,
        ram: formData.ram
      }

      const response = await fetch('/api/compatibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gpuId: formData.gpuId,
          system: systemSpecs
        })
      })

      if (!response.ok) {
        throw new Error('Failed to check compatibility')
      }

      const data = await response.json()
      setCompatibilityResult(data.compatibility)
      onCompatibilityCheck?.(data.compatibility)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: 'compatible' | 'warning' | 'incompatible') => {
    switch (status) {
      case 'compatible':
        return 'text-green-400'
      case 'warning':
        return 'text-yellow-400'
      case 'incompatible':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: 'compatible' | 'warning' | 'incompatible') => {
    switch (status) {
      case 'compatible':
        return '✓'
      case 'warning':
        return '⚠'
      case 'incompatible':
        return '✗'
      default:
        return '?'
    }
  }

  return (
    <div className="bg-card rounded-lg p-6 border border-border">
      <h2 className="text-2xl font-bold text-primary mb-6">Hardware Compatibility Checker</h2>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* GPU Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">GPU Selection</h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label htmlFor="gpu-id" className="block text-sm font-medium text-foreground-muted mb-2">
                GPU ID
              </label>
              <input
                id="gpu-id"
                type="text"
                value={formData.gpuId}
                onChange={(e) => setFormData(prev => ({ ...prev, gpuId: e.target.value }))}
                className="input w-full"
                placeholder="Enter GPU ID"
                required
              />
            </div>
          </div>
        </div>

        {/* CPU Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">CPU</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="cpu-model" className="block text-sm font-medium text-foreground-muted mb-2">
                CPU Model
              </label>
              <input
                id="cpu-model"
                type="text"
                value={formData.cpu.model}
                onChange={(e) => handleInputChange('cpu', 'model', e.target.value)}
                className="input w-full"
                placeholder="e.g., Intel i7-12700K"
                required
              />
            </div>
            <div>
              <label htmlFor="cpu-cores" className="block text-sm font-medium text-foreground-muted mb-2">
                Cores
              </label>
              <input
                id="cpu-cores"
                type="number"
                value={formData.cpu.cores}
                onChange={(e) => handleInputChange('cpu', 'cores', parseInt(e.target.value))}
                className="input w-full"
                min="1"
                max="64"
                required
              />
            </div>
            <div>
              <label htmlFor="cpu-threads" className="block text-sm font-medium text-foreground-muted mb-2">
                Threads
              </label>
              <input
                id="cpu-threads"
                type="number"
                value={formData.cpu.threads}
                onChange={(e) => handleInputChange('cpu', 'threads', parseInt(e.target.value))}
                className="input w-full"
                min="1"
                max="128"
                required
              />
            </div>
            <div>
              <label htmlFor="cpu-base-clock" className="block text-sm font-medium text-foreground-muted mb-2">
                Base Clock (MHz)
              </label>
              <input
                id="cpu-base-clock"
                type="number"
                value={formData.cpu.baseClock}
                onChange={(e) => handleInputChange('cpu', 'baseClock', parseInt(e.target.value))}
                className="input w-full"
                min="1000"
                max="6000"
                required
              />
            </div>
          </div>
        </div>

        {/* Motherboard Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Motherboard</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="motherboard-model" className="block text-sm font-medium text-foreground-muted mb-2">
                Motherboard Model
              </label>
              <input
                id="motherboard-model"
                type="text"
                value={formData.motherboard.model}
                onChange={(e) => handleInputChange('motherboard', 'model', e.target.value)}
                className="input w-full"
                placeholder="e.g., ASUS ROG Strix Z690-E"
                required
              />
            </div>
            <div>
              <label htmlFor="motherboard-chipset" className="block text-sm font-medium text-foreground-muted mb-2">
                Chipset
              </label>
              <input
                id="motherboard-chipset"
                type="text"
                value={formData.motherboard.chipset}
                onChange={(e) => handleInputChange('motherboard', 'chipset', e.target.value)}
                className="input w-full"
                placeholder="e.g., Z690"
                required
              />
            </div>
            <div className="md:col-span-2">
              <label htmlFor="motherboard-pcie-slots" className="block text-sm font-medium text-foreground-muted mb-2">
                PCIe Slots (comma-separated)
              </label>
              <input
                id="motherboard-pcie-slots"
                type="text"
                value={formData.motherboard.pcieSlots.join(', ')}
                onChange={(e) => handleArrayInputChange('motherboard', 'pcieSlots', e.target.value)}
                className="input w-full"
                placeholder="e.g., PCIe 4.0 x16, PCIe 3.0 x8"
                required
              />
            </div>
          </div>
        </div>

        {/* PSU Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Power Supply</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="psu-wattage" className="block text-sm font-medium text-foreground-muted mb-2">
                Wattage
              </label>
              <input
                id="psu-wattage"
                type="number"
                value={formData.psu.wattage}
                onChange={(e) => handleInputChange('psu', 'wattage', parseInt(e.target.value))}
                className="input w-full"
                min="300"
                max="2000"
                required
              />
            </div>
            <div>
              <label htmlFor="psu-efficiency" className="block text-sm font-medium text-foreground-muted mb-2">
                Efficiency Rating
              </label>
              <select
                id="psu-efficiency"
                value={formData.psu.efficiency}
                onChange={(e) => handleInputChange('psu', 'efficiency', e.target.value)}
                className="input w-full"
                required
                aria-label="PSU Efficiency Rating"
              >
                <option value="80+ Bronze">80+ Bronze</option>
                <option value="80+ Silver">80+ Silver</option>
                <option value="80+ Gold">80+ Gold</option>
                <option value="80+ Platinum">80+ Platinum</option>
                <option value="80+ Titanium">80+ Titanium</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label htmlFor="psu-connectors" className="block text-sm font-medium text-foreground-muted mb-2">
                Power Connectors (comma-separated)
              </label>
              <input
                id="psu-connectors"
                type="text"
                value={formData.psu.connectors.join(', ')}
                onChange={(e) => handleArrayInputChange('psu', 'connectors', e.target.value)}
                className="input w-full"
                placeholder="e.g., 8-pin, 6-pin, 12VHPWR"
              />
            </div>
          </div>
        </div>

        {/* Case Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">PC Case</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label htmlFor="case-model" className="block text-sm font-medium text-foreground-muted mb-2">
                Case Model
              </label>
              <input
                id="case-model"
                type="text"
                value={formData.case.model}
                onChange={(e) => handleInputChange('case', 'model', e.target.value)}
                className="input w-full"
                placeholder="e.g., Fractal Design Define 7"
              />
            </div>
            <div>
              <label htmlFor="case-max-gpu-length" className="block text-sm font-medium text-foreground-muted mb-2">
                Max GPU Length (mm)
              </label>
              <input
                id="case-max-gpu-length"
                type="number"
                value={formData.case.maxGPULength}
                onChange={(e) => handleInputChange('case', 'maxGPULength', parseInt(e.target.value))}
                className="input w-full"
                min="200"
                max="500"
                required
              />
            </div>
            <div>
              <label htmlFor="case-max-gpu-width" className="block text-sm font-medium text-foreground-muted mb-2">
                Max GPU Width (mm)
              </label>
              <input
                id="case-max-gpu-width"
                type="number"
                value={formData.case.maxGPUWidth}
                onChange={(e) => handleInputChange('case', 'maxGPUWidth', parseInt(e.target.value))}
                className="input w-full"
                min="100"
                max="200"
                required
              />
            </div>
            <div>
              <label htmlFor="case-max-gpu-height" className="block text-sm font-medium text-foreground-muted mb-2">
                Max GPU Height (mm)
              </label>
              <input
                id="case-max-gpu-height"
                type="number"
                value={formData.case.maxGPUHeight}
                onChange={(e) => handleInputChange('case', 'maxGPUHeight', parseInt(e.target.value))}
                className="input w-full"
                min="40"
                max="80"
                required
              />
            </div>
          </div>
        </div>

        {/* RAM Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Memory (RAM)</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="ram-size" className="block text-sm font-medium text-foreground-muted mb-2">
                Size (GB)
              </label>
              <select
                id="ram-size"
                value={formData.ram.size}
                onChange={(e) => handleInputChange('ram', 'size', parseInt(e.target.value))}
                className="input w-full"
                required
                aria-label="RAM Size in GB"
              >
                <option value={8}>8 GB</option>
                <option value={16}>16 GB</option>
                <option value={32}>32 GB</option>
                <option value={64}>64 GB</option>
                <option value={128}>128 GB</option>
              </select>
            </div>
            <div>
              <label htmlFor="ram-speed" className="block text-sm font-medium text-foreground-muted mb-2">
                Speed (MHz)
              </label>
              <input
                id="ram-speed"
                type="number"
                value={formData.ram.speed}
                onChange={(e) => handleInputChange('ram', 'speed', parseInt(e.target.value))}
                className="input w-full"
                min="2133"
                max="6000"
                required
              />
            </div>
            <div>
              <label htmlFor="ram-type" className="block text-sm font-medium text-foreground-muted mb-2">
                Type
              </label>
              <select
                id="ram-type"
                value={formData.ram.type}
                onChange={(e) => handleInputChange('ram', 'type', e.target.value)}
                className="input w-full"
                required
                aria-label="RAM Type"
              >
                <option value="DDR4">DDR4</option>
                <option value="DDR5">DDR5</option>
              </select>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Checking Compatibility...' : 'Check Compatibility'}
          </button>
        </div>
      </form>

      {/* Error Display */}
      {error && (
        <div className="mt-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Compatibility Results */}
      {compatibilityResult && (
        <div className="mt-8 space-y-6">
          <h3 className="text-xl font-bold text-foreground">Compatibility Results</h3>

          {/* Overall Status */}
          <div className={`p-4 rounded-lg border ${compatibilityResult.overall === 'compatible'
              ? 'bg-green-900/30 border-green-700'
              : compatibilityResult.overall === 'warning'
                ? 'bg-yellow-900/30 border-yellow-700'
                : 'bg-red-900/30 border-red-700'
            }`}>
            <div className="flex items-center space-x-3">
              <span className={`text-2xl ${getStatusColor(compatibilityResult.overall)}`}>
                {getStatusIcon(compatibilityResult.overall)}
              </span>
              <div>
                <h4 className="text-lg font-semibold text-foreground">
                  Overall Compatibility: {compatibilityResult.overall.charAt(0).toUpperCase() + compatibilityResult.overall.slice(1)}
                </h4>
              </div>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Power Compatibility */}
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center space-x-2 mb-3">
                <span className={`text-lg ${getStatusColor(compatibilityResult.power.status)}`}>
                  {getStatusIcon(compatibilityResult.power.status)}
                </span>
                <h5 className="font-semibold text-foreground">Power Supply</h5>
              </div>
              <div className="space-y-2 text-sm text-foreground-muted">
                <p>Required: {compatibilityResult.power.required}W</p>
                <p>Available: {compatibilityResult.power.available}W</p>
                {compatibilityResult.power.connectors.missing.length > 0 && (
                  <p className="text-red-400">
                    Missing: {compatibilityResult.power.connectors.missing.join(', ')}
                  </p>
                )}
              </div>
            </div>

            {/* Physical Compatibility */}
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center space-x-2 mb-3">
                <span className={`text-lg ${getStatusColor(compatibilityResult.physical.status)}`}>
                  {getStatusIcon(compatibilityResult.physical.status)}
                </span>
                <h5 className="font-semibold text-foreground">Physical Fit</h5>
              </div>
              <div className="space-y-2 text-sm text-foreground-muted">
                <p>Length: {compatibilityResult.physical.clearance.length.required}mm / {compatibilityResult.physical.clearance.length.available}mm</p>
                <p>Width: {compatibilityResult.physical.clearance.width.required}mm / {compatibilityResult.physical.clearance.width.available}mm</p>
                <p>Height: {compatibilityResult.physical.clearance.height.required}mm / {compatibilityResult.physical.clearance.height.available}mm</p>
              </div>
            </div>

            {/* CPU Bottleneck */}
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center space-x-2 mb-3">
                <span className={`text-lg ${compatibilityResult.bottleneck.severity === 'none' ? 'text-green-400' :
                    compatibilityResult.bottleneck.severity === 'mild' ? 'text-yellow-400' :
                      compatibilityResult.bottleneck.severity === 'moderate' ? 'text-orange-400' :
                        'text-red-400'
                  }`}>
                  {compatibilityResult.bottleneck.severity === 'none' ? '✓' : '⚠'}
                </span>
                <h5 className="font-semibold text-foreground">CPU Bottleneck</h5>
              </div>
              <div className="space-y-2 text-sm text-foreground-muted">
                <p>Bottleneck: {compatibilityResult.bottleneck.cpuBottleneck}%</p>
                <p>Severity: {compatibilityResult.bottleneck.severity}</p>
              </div>
            </div>
          </div>

          {/* Recommendations */}
          {compatibilityResult.recommendations.length > 0 && (
            <div className="bg-card p-4 rounded-lg border border-border">
              <h5 className="font-semibold text-foreground mb-3">Recommendations</h5>
              <ul className="space-y-2">
                {compatibilityResult.recommendations.map((recommendation, index) => (
                  <li key={index} className="text-foreground-muted text-sm flex items-start space-x-2">
                    <span className="text-primary mt-1">•</span>
                    <span>{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}