'use client'

import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { useState } from 'react'

interface AuthButtonsProps {
  mobile?: boolean
}

export function AuthButtons({ mobile = false }: AuthButtonsProps) {
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(false)

  const handleSignOut = async () => {
    setIsLoading(true)
    await signOut({ callbackUrl: '/' })
    setIsLoading(false)
  }

  const containerClass = mobile 
    ? "flex flex-col space-y-2 md:hidden" 
    : "hidden md:flex items-center space-x-3"

  if (status === 'loading') {
    return (
      <div className={containerClass}>
        <div className="w-20 h-8 bg-card animate-pulse rounded"></div>
        <div className="w-24 h-8 bg-card animate-pulse rounded"></div>
      </div>
    )
  }

  if (session) {
    return (
      <div className={containerClass}>
        {/* User info and quick links */}
        <div className={`flex items-center ${mobile ? 'flex-col space-y-2' : 'space-x-4'}`}>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-black font-medium text-sm">
                {session.user?.name?.charAt(0) || session.user?.email?.charAt(0) || 'U'}
              </span>
            </div>
            <span className="text-foreground text-sm font-medium">
              {session.user?.name || session.user?.username || 'User'}
            </span>
          </div>
          
          {/* Quick access links */}
          <div className={`flex ${mobile ? 'w-full justify-center space-x-4' : 'space-x-2'}`}>
            <Link href="/wishlist" className="text-foreground-muted hover:text-primary transition-colors">
              <span className="text-lg">🤍</span>
              {mobile && <span className="ml-1 text-sm">Wishlist</span>}
            </Link>
            <Link href="/alerts" className="text-foreground-muted hover:text-primary transition-colors">
              <span className="text-lg">🔔</span>
              {mobile && <span className="ml-1 text-sm">Alerts</span>}
            </Link>
            <Link href="/profile" className="text-foreground-muted hover:text-primary transition-colors">
              <span className="text-lg">⚙️</span>
              {mobile && <span className="ml-1 text-sm">Profile</span>}
            </Link>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleSignOut}
          loading={isLoading}
          className={mobile ? 'w-full mt-2' : ''}
        >
          Sign Out
        </Button>
      </div>
    )
  }

  return (
    <div className={containerClass}>
      <Link href="/auth/signin">
        <Button variant="outline" size="sm" className={mobile ? 'w-full' : ''}>
          Sign In
        </Button>
      </Link>
      <Link href="/auth/signup">
        <Button variant="primary" size="sm" className={mobile ? 'w-full' : ''}>
          Get Started
        </Button>
      </Link>
    </div>
  )
}