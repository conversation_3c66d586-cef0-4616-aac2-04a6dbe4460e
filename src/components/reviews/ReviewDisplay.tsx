'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/Button'

interface Review {
  id: string
  rating: number
  title: string | null
  content: string | null
  systemSpecs: any
  performanceData: any
  isVerified: boolean
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string | null
    username: string | null
    createdAt: string
    _count: {
      reviews: number
    }
  }
  gpu: {
    id: string
    name: string
    manufacturer: string
    architecture: string | null
  }
}

interface ReviewDisplayProps {
  reviews: Review[]
  isLoading?: boolean
  onLoadMore?: () => void
  hasMore?: boolean
  showGPUInfo?: boolean
}

export function ReviewDisplay({ 
  reviews, 
  isLoading = false, 
  onLoadMore, 
  hasMore = false,
  showGPUInfo = false 
}: ReviewDisplayProps) {
  const { data: session } = useSession()
  const [expandedReviews, setExpandedReviews] = useState<Set<string>>(new Set())

  const toggleExpanded = (reviewId: string) => {
    setExpandedReviews(prev => {
      const newSet = new Set(prev)
      if (newSet.has(reviewId)) {
        newSet.delete(reviewId)
      } else {
        newSet.add(reviewId)
      }
      return newSet
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getUserReputation = (reviewCount: number, memberSince: string) => {
    const monthsSinceMember = Math.floor(
      (Date.now() - new Date(memberSince).getTime()) / (1000 * 60 * 60 * 24 * 30)
    )
    
    if (reviewCount >= 20 && monthsSinceMember >= 12) return 'Expert Reviewer'
    if (reviewCount >= 10 && monthsSinceMember >= 6) return 'Experienced'
    if (reviewCount >= 5) return 'Active Reviewer'
    if (reviewCount >= 2) return 'Contributor'
    return 'New Reviewer'
  }

  const getReputationColor = (reputation: string) => {
    switch (reputation) {
      case 'Expert Reviewer': return 'text-primary'
      case 'Experienced': return 'text-blue-400'
      case 'Active Reviewer': return 'text-green-400'
      case 'Contributor': return 'text-yellow-400'
      default: return 'text-foreground-muted'
    }
  }

  if (isLoading && reviews.length === 0) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-card border border-border rounded-lg p-6 animate-pulse">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-background-secondary rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-background-secondary rounded w-1/4"></div>
                <div className="h-4 bg-background-secondary rounded w-3/4"></div>
                <div className="h-20 bg-background-secondary rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (reviews.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 bg-card rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-4xl">📝</span>
        </div>
        <h3 className="text-xl font-semibold text-foreground mb-2">
          No reviews yet
        </h3>
        <p className="text-foreground-muted">
          Be the first to share your experience with this GPU!
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {reviews.map((review) => {
        const isExpanded = expandedReviews.has(review.id)
        const reputation = getUserReputation(review.user._count.reviews, review.user.createdAt)
        const isOwnReview = session?.user?.id === review.user.id

        return (
          <div key={review.id} className="bg-card border border-border rounded-lg p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start space-x-4">
                {/* User Avatar */}
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-black font-medium">
                    {review.user.name?.charAt(0) || review.user.username?.charAt(0) || 'U'}
                  </span>
                </div>

                {/* User Info and Rating */}
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-foreground">
                      {review.user.name || review.user.username || 'Anonymous'}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${getReputationColor(reputation)} bg-background-secondary`}>
                      {reputation}
                    </span>
                    {review.isVerified && (
                      <span className="text-xs px-2 py-1 rounded bg-green-900/30 text-green-400 border border-green-700">
                        Verified Purchase
                      </span>
                    )}
                  </div>
                  
                  {/* Rating */}
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <span
                          key={star}
                          className={`text-lg ${
                            star <= review.rating ? 'text-primary' : 'text-foreground-muted'
                          }`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                    <span className="text-sm text-foreground-muted">
                      {review.rating}/5
                    </span>
                  </div>

                  <div className="text-xs text-foreground-muted">
                    {formatDate(review.createdAt)}
                    {review.updatedAt !== review.createdAt && ' (edited)'}
                  </div>
                </div>
              </div>

              {/* GPU Info (if showing reviews across multiple GPUs) */}
              {showGPUInfo && (
                <div className="text-right">
                  <div className="text-sm font-medium text-foreground">
                    {review.gpu.name}
                  </div>
                  <div className="text-xs text-foreground-muted">
                    {review.gpu.manufacturer} • {review.gpu.architecture}
                  </div>
                </div>
              )}
            </div>

            {/* Review Title */}
            {review.title && (
              <h4 className="text-lg font-semibold text-foreground mb-2">
                {review.title}
              </h4>
            )}

            {/* Review Content */}
            {review.content && (
              <div className="text-foreground-muted mb-4">
                <p className={`${!isExpanded && review.content.length > 300 ? 'line-clamp-3' : ''}`}>
                  {review.content}
                </p>
                {review.content.length > 300 && (
                  <button
                    onClick={() => toggleExpanded(review.id)}
                    className="text-primary hover:text-primary-light text-sm mt-2"
                  >
                    {isExpanded ? 'Show less' : 'Show more'}
                  </button>
                )}
              </div>
            )}

            {/* System Specs and Performance Data */}
            {(review.systemSpecs || review.performanceData) && (
              <div className="mt-4">
                <button
                  onClick={() => toggleExpanded(review.id)}
                  className="text-sm text-primary hover:text-primary-light mb-2"
                >
                  {isExpanded ? 'Hide' : 'Show'} technical details
                </button>

                {isExpanded && (
                  <div className="bg-background-secondary rounded-lg p-4 space-y-4">
                    {/* System Specs */}
                    {review.systemSpecs && (
                      <div>
                        <h5 className="font-medium text-foreground mb-2">System Specifications</h5>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                          {Object.entries(review.systemSpecs).map(([key, value]) => (
                            value && (
                              <div key={key}>
                                <span className="text-foreground-muted capitalize">{key}:</span>
                                <span className="text-foreground ml-2">{value as string}</span>
                              </div>
                            )
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Performance Data */}
                    {review.performanceData?.games?.length > 0 && (
                      <div>
                        <h5 className="font-medium text-foreground mb-2">Game Performance</h5>
                        <div className="space-y-2">
                          {review.performanceData.games.map((game: any, index: number) => (
                            game.name && (
                              <div key={index} className="text-sm">
                                <span className="text-foreground">{game.name}</span>
                                <span className="text-foreground-muted ml-2">
                                  {game.resolution} • {game.settings} • {game.fps} FPS
                                </span>
                              </div>
                            )
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            {isOwnReview && (
              <div className="flex justify-end space-x-2 mt-4 pt-4 border-t border-border">
                <Button variant="outline" size="sm">
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-400 hover:text-red-300">
                  Delete
                </Button>
              </div>
            )}
          </div>
        )
      })}

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={onLoadMore}
            loading={isLoading}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Load More Reviews'}
          </Button>
        </div>
      )}
    </div>
  )
}