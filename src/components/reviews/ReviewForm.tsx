'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'

interface ReviewFormProps {
  gpuId: string
  gpuName: string
  onReviewSubmitted?: () => void
  onCancel?: () => void
}

interface SystemSpecs {
  cpu: string
  motherboard: string
  ram: string
  psu: string
  case: string
  storage: string
}

interface PerformanceData {
  games: Array<{
    name: string
    resolution: string
    settings: string
    fps: number
  }>
  benchmarks: Array<{
    name: string
    score: number
  }>
  thermals: {
    idleTemp: number
    loadTemp: number
    maxTemp: number
  }
  power: {
    idlePower: number
    loadPower: number
    maxPower: number
  }
}

export function ReviewForm({ gpuId, gpuName, onReviewSubmitted, onCancel }: ReviewFormProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const [formData, setFormData] = useState({
    rating: 5,
    title: '',
    content: '',
    systemSpecs: {
      cpu: '',
      motherboard: '',
      ram: '',
      psu: '',
      case: '',
      storage: ''
    } as SystemSpecs,
    performanceData: {
      games: [{ name: '', resolution: '1080p', settings: 'High', fps: 0 }],
      benchmarks: [{ name: '', score: 0 }],
      thermals: {
        idleTemp: 0,
        loadTemp: 0,
        maxTemp: 0
      },
      power: {
        idlePower: 0,
        loadPower: 0,
        maxPower: 0
      }
    } as PerformanceData
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSystemSpecChange = (field: keyof SystemSpecs, value: string) => {
    setFormData(prev => ({
      ...prev,
      systemSpecs: {
        ...prev.systemSpecs,
        [field]: value
      }
    }))
  }

  const handleGameChange = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      performanceData: {
        ...prev.performanceData,
        games: prev.performanceData.games.map((game, i) => 
          i === index ? { ...game, [field]: value } : game
        )
      }
    }))
  }

  const addGame = () => {
    setFormData(prev => ({
      ...prev,
      performanceData: {
        ...prev.performanceData,
        games: [...prev.performanceData.games, { name: '', resolution: '1080p', settings: 'High', fps: 0 }]
      }
    }))
  }

  const removeGame = (index: number) => {
    setFormData(prev => ({
      ...prev,
      performanceData: {
        ...prev.performanceData,
        games: prev.performanceData.games.filter((_, i) => i !== index)
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session) {
      router.push('/auth/signin')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          gpuId,
          rating: formData.rating,
          title: formData.title.trim() || null,
          content: formData.content.trim() || null,
          systemSpecs: formData.systemSpecs,
          performanceData: formData.performanceData
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to submit review')
      }

      onReviewSubmitted?.()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to submit review')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <h3 className="text-xl font-bold text-foreground mb-4">
        Write a Review for {gpuName}
      </h3>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-foreground-muted mb-2">
            Overall Rating *
          </label>
          <div className="flex space-x-2">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => handleInputChange('rating', star)}
                className={`text-2xl transition-colors ${
                  star <= formData.rating 
                    ? 'text-primary hover:text-primary-light' 
                    : 'text-foreground-muted hover:text-foreground'
                }`}
              >
                ★
              </button>
            ))}
            <span className="ml-2 text-foreground-muted">
              {formData.rating}/5 stars
            </span>
          </div>
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-foreground-muted mb-2">
            Review Title
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className="input w-full"
            placeholder="Summarize your experience..."
            maxLength={100}
          />
        </div>

        {/* Content */}
        <div>
          <label className="block text-sm font-medium text-foreground-muted mb-2">
            Review Content
          </label>
          <textarea
            value={formData.content}
            onChange={(e) => handleInputChange('content', e.target.value)}
            className="input w-full h-32 resize-vertical"
            placeholder="Share your detailed experience with this GPU..."
            maxLength={2000}
          />
          <div className="text-xs text-foreground-muted mt-1">
            {formData.content.length}/2000 characters
          </div>
        </div>

        {/* System Specifications */}
        <div>
          <h4 className="text-lg font-semibold text-foreground mb-3">System Specifications</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground-muted mb-1">CPU</label>
              <input
                type="text"
                value={formData.systemSpecs.cpu}
                onChange={(e) => handleSystemSpecChange('cpu', e.target.value)}
                className="input w-full"
                placeholder="e.g., Intel i7-12700K"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground-muted mb-1">Motherboard</label>
              <input
                type="text"
                value={formData.systemSpecs.motherboard}
                onChange={(e) => handleSystemSpecChange('motherboard', e.target.value)}
                className="input w-full"
                placeholder="e.g., ASUS ROG Strix Z690-E"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground-muted mb-1">RAM</label>
              <input
                type="text"
                value={formData.systemSpecs.ram}
                onChange={(e) => handleSystemSpecChange('ram', e.target.value)}
                className="input w-full"
                placeholder="e.g., 32GB DDR4-3200"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground-muted mb-1">PSU</label>
              <input
                type="text"
                value={formData.systemSpecs.psu}
                onChange={(e) => handleSystemSpecChange('psu', e.target.value)}
                className="input w-full"
                placeholder="e.g., 850W 80+ Gold"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground-muted mb-1">Case</label>
              <input
                type="text"
                value={formData.systemSpecs.case}
                onChange={(e) => handleSystemSpecChange('case', e.target.value)}
                className="input w-full"
                placeholder="e.g., Fractal Design Define 7"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground-muted mb-1">Storage</label>
              <input
                type="text"
                value={formData.systemSpecs.storage}
                onChange={(e) => handleSystemSpecChange('storage', e.target.value)}
                className="input w-full"
                placeholder="e.g., 1TB NVMe SSD"
              />
            </div>
          </div>
        </div>

        {/* Performance Data */}
        <div>
          <h4 className="text-lg font-semibold text-foreground mb-3">Performance Data (Optional)</h4>
          
          {/* Games Performance */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-foreground-muted">Game Performance</label>
              <Button type="button" variant="outline" size="sm" onClick={addGame}>
                Add Game
              </Button>
            </div>
            {formData.performanceData.games.map((game, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-2 mb-2">
                <input
                  type="text"
                  value={game.name}
                  onChange={(e) => handleGameChange(index, 'name', e.target.value)}
                  className="input"
                  placeholder="Game name"
                />
                <select
                  value={game.resolution}
                  onChange={(e) => handleGameChange(index, 'resolution', e.target.value)}
                  className="input"
                >
                  <option value="1080p">1080p</option>
                  <option value="1440p">1440p</option>
                  <option value="4K">4K</option>
                </select>
                <select
                  value={game.settings}
                  onChange={(e) => handleGameChange(index, 'settings', e.target.value)}
                  className="input"
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Ultra">Ultra</option>
                </select>
                <input
                  type="number"
                  value={game.fps || ''}
                  onChange={(e) => handleGameChange(index, 'fps', parseInt(e.target.value) || 0)}
                  className="input"
                  placeholder="FPS"
                  min="0"
                />
                {formData.performanceData.games.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeGame(index)}
                    className="text-red-400 hover:text-red-300"
                  >
                    Remove
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {error && (
          <div className="p-3 bg-red-900/50 border border-red-700 rounded-lg">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-3">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" loading={isLoading} disabled={isLoading}>
            {isLoading ? 'Submitting...' : 'Submit Review'}
          </Button>
        </div>
      </form>
    </div>
  )
}