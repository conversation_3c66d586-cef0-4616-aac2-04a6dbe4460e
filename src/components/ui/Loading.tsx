import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export function Loading({ size = 'md', text, className }: LoadingProps) {
  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  return (
    <div className={cn('flex flex-col items-center justify-center gap-3', className)}>
      <div className="relative">
        <div className={cn('animate-spin rounded-full border-2 border-border', sizes[size])}>
          <div className={cn('rounded-full border-2 border-primary border-t-transparent', sizes[size])} />
        </div>
        <div className="absolute inset-0 animate-pulse">
          <div className={cn('rounded-full bg-primary/20', sizes[size])} />
        </div>
      </div>
      {text && (
        <p className="text-sm text-foreground-muted animate-pulse">{text}</p>
      )}
    </div>
  );
}

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
}

export function LoadingSkeleton({ className, lines = 3 }: LoadingSkeletonProps) {
  return (
    <div className={cn('animate-pulse space-y-3', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className="h-4 bg-background-tertiary rounded"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </div>
  );
}

interface LoadingCardProps {
  className?: string;
}

export function LoadingCard({ className }: LoadingCardProps) {
  return (
    <div className={cn('bg-card border border-border rounded-xl p-6 animate-pulse', className)}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="h-4 bg-background-tertiary rounded w-20 mb-2" />
          <div className="h-6 bg-background-tertiary rounded w-3/4 mb-2" />
        </div>
        <div className="h-8 w-8 bg-background-tertiary rounded" />
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex flex-col">
            <div className="h-3 bg-background-tertiary rounded w-16 mb-1" />
            <div className="h-4 bg-background-tertiary rounded w-20" />
          </div>
        ))}
      </div>
      
      <div className="flex gap-2">
        <div className="h-8 bg-background-tertiary rounded flex-1" />
        <div className="h-8 bg-background-tertiary rounded w-20" />
      </div>
    </div>
  );
}