'use client'

import { useEffect, useRef } from 'react'

interface PerformanceData {
  name: string
  fps: number
  color: string
}

interface PerformanceChartProps {
  data: PerformanceData[]
  title?: string
  height?: number
}

export function PerformanceChart({ data, title, height = 300 }: PerformanceChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || data.length === 0) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const rect = canvas.getBoundingClientRect()
    canvas.width = rect.width * window.devicePixelRatio
    canvas.height = height * window.devicePixelRatio
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, height)

    // Chart dimensions
    const padding = 60
    const chartWidth = rect.width - padding * 2
    const chartHeight = height - padding * 2

    // Find max value for scaling
    const maxFps = Math.max(...data.map(d => d.fps))
    const scale = chartHeight / maxFps

    // Draw bars
    const barWidth = chartWidth / data.length * 0.8
    const barSpacing = chartWidth / data.length * 0.2

    data.forEach((item, index) => {
      const x = padding + index * (barWidth + barSpacing)
      const barHeight = item.fps * scale
      const y = height - padding - barHeight

      // Draw bar
      ctx.fillStyle = item.color
      ctx.fillRect(x, y, barWidth, barHeight)

      // Draw value on top of bar
      ctx.fillStyle = '#ffffff'
      ctx.font = '12px system-ui'
      ctx.textAlign = 'center'
      ctx.fillText(
        `${item.fps.toFixed(1)}`,
        x + barWidth / 2,
        y - 5
      )

      // Draw label at bottom
      ctx.fillStyle = '#9ca3af'
      ctx.font = '10px system-ui'
      ctx.save()
      ctx.translate(x + barWidth / 2, height - padding + 15)
      ctx.rotate(-Math.PI / 4)
      ctx.textAlign = 'right'
      ctx.fillText(item.name, 0, 0)
      ctx.restore()
    })

    // Draw Y-axis
    ctx.strokeStyle = '#374151'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, height - padding)
    ctx.stroke()

    // Draw Y-axis labels
    const steps = 5
    for (let i = 0; i <= steps; i++) {
      const value = (maxFps / steps) * i
      const y = height - padding - (value * scale)
      
      ctx.fillStyle = '#9ca3af'
      ctx.font = '10px system-ui'
      ctx.textAlign = 'right'
      ctx.fillText(value.toFixed(0), padding - 10, y + 3)
      
      // Draw grid line
      ctx.strokeStyle = '#374151'
      ctx.lineWidth = 0.5
      ctx.beginPath()
      ctx.moveTo(padding, y)
      ctx.lineTo(rect.width - padding, y)
      ctx.stroke()
    }

    // Draw title
    if (title) {
      ctx.fillStyle = '#ffffff'
      ctx.font = 'bold 14px system-ui'
      ctx.textAlign = 'center'
      ctx.fillText(title, rect.width / 2, 25)
    }

  }, [data, height, title])

  return (
    <div className="w-full">
      <canvas
        ref={canvasRef}
        className="w-full border border-border rounded-lg bg-card"
        style={{ height: `${height}px` }}
      />
    </div>
  )
}