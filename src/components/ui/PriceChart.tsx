'use client'

import { useEffect, useRef } from 'react'

interface PricePoint {
  date: Date
  price: number
  retailer: string
}

interface PriceChartProps {
  data: PricePoint[]
  title?: string
  height?: number
  showRetailers?: boolean
}

export function PriceChart({ data, title, height = 300, showRetailers = false }: PriceChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || data.length === 0) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const rect = canvas.getBoundingClientRect()
    canvas.width = rect.width * window.devicePixelRatio
    canvas.height = height * window.devicePixelRatio
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, height)

    // Chart dimensions
    const padding = 60
    const chartWidth = rect.width - padding * 2
    const chartHeight = height - padding * 2

    // Sort data by date
    const sortedData = [...data].sort((a, b) => a.date.getTime() - b.date.getTime())

    // Find min/max values for scaling
    const prices = sortedData.map(d => d.price)
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)
    const priceRange = maxPrice - minPrice
    const priceScale = chartHeight / (priceRange || 1)

    const minDate = sortedData[0].date.getTime()
    const maxDate = sortedData[sortedData.length - 1].date.getTime()
    const dateRange = maxDate - minDate
    const dateScale = chartWidth / (dateRange || 1)

    // Group by retailer if showing retailers
    const retailerGroups = new Map<string, typeof sortedData>()
    const colors = ['#f59e0b', '#10b981', '#3b82f6', '#ef4444', '#8b5cf6']
    
    if (showRetailers) {
      sortedData.forEach(point => {
        if (!retailerGroups.has(point.retailer)) {
          retailerGroups.set(point.retailer, [])
        }
        retailerGroups.get(point.retailer)!.push(point)
      })
    } else {
      retailerGroups.set('All', sortedData)
    }

    // Draw grid lines
    ctx.strokeStyle = '#374151'
    ctx.lineWidth = 0.5

    // Vertical grid lines (dates)
    const dateSteps = 5
    for (let i = 0; i <= dateSteps; i++) {
      const x = padding + (chartWidth / dateSteps) * i
      ctx.beginPath()
      ctx.moveTo(x, padding)
      ctx.lineTo(x, height - padding)
      ctx.stroke()
    }

    // Horizontal grid lines (prices)
    const priceSteps = 5
    for (let i = 0; i <= priceSteps; i++) {
      const y = padding + (chartHeight / priceSteps) * i
      ctx.beginPath()
      ctx.moveTo(padding, y)
      ctx.lineTo(rect.width - padding, y)
      ctx.stroke()
    }

    // Draw price lines
    let colorIndex = 0
    retailerGroups.forEach((points, retailer) => {
      const color = colors[colorIndex % colors.length]
      ctx.strokeStyle = color
      ctx.lineWidth = 2

      ctx.beginPath()
      points.forEach((point, index) => {
        const x = padding + (point.date.getTime() - minDate) * dateScale
        const y = height - padding - (point.price - minPrice) * priceScale

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()

      // Draw points
      ctx.fillStyle = color
      points.forEach(point => {
        const x = padding + (point.date.getTime() - minDate) * dateScale
        const y = height - padding - (point.price - minPrice) * priceScale
        
        ctx.beginPath()
        ctx.arc(x, y, 3, 0, 2 * Math.PI)
        ctx.fill()
      })

      colorIndex++
    })

    // Draw axes
    ctx.strokeStyle = '#6b7280'
    ctx.lineWidth = 2

    // Y-axis
    ctx.beginPath()
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, height - padding)
    ctx.stroke()

    // X-axis
    ctx.beginPath()
    ctx.moveTo(padding, height - padding)
    ctx.lineTo(rect.width - padding, height - padding)
    ctx.stroke()

    // Draw Y-axis labels (prices)
    ctx.fillStyle = '#9ca3af'
    ctx.font = '12px system-ui'
    ctx.textAlign = 'right'

    for (let i = 0; i <= priceSteps; i++) {
      const price = minPrice + (priceRange / priceSteps) * i
      const y = height - padding - (chartHeight / priceSteps) * i
      ctx.fillText(`$${price.toFixed(0)}`, padding - 10, y + 4)
    }

    // Draw X-axis labels (dates)
    ctx.textAlign = 'center'
    for (let i = 0; i <= dateSteps; i++) {
      const dateTime = minDate + (dateRange / dateSteps) * i
      const date = new Date(dateTime)
      const x = padding + (chartWidth / dateSteps) * i
      
      const dateStr = date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
      ctx.fillText(dateStr, x, height - padding + 20)
    }

    // Draw title
    if (title) {
      ctx.fillStyle = '#ffffff'
      ctx.font = 'bold 16px system-ui'
      ctx.textAlign = 'center'
      ctx.fillText(title, rect.width / 2, 25)
    }

    // Draw legend if showing retailers
    if (showRetailers && retailerGroups.size > 1) {
      let legendY = 50
      colorIndex = 0
      
      retailerGroups.forEach((_, retailer) => {
        const color = colors[colorIndex % colors.length]
        
        // Legend color box
        ctx.fillStyle = color
        ctx.fillRect(rect.width - 150, legendY, 12, 12)
        
        // Legend text
        ctx.fillStyle = '#ffffff'
        ctx.font = '12px system-ui'
        ctx.textAlign = 'left'
        ctx.fillText(retailer, rect.width - 135, legendY + 9)
        
        legendY += 20
        colorIndex++
      })
    }

  }, [data, height, title, showRetailers])

  return (
    <div className="w-full">
      <canvas
        ref={canvasRef}
        className="w-full border border-border rounded-lg bg-card"
        style={{ height: `${height}px` }}
      />
    </div>
  )
}