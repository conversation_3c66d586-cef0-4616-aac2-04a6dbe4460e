import { render, screen, fireEvent } from '@testing-library/react'
import { GPUCard } from '../GPUCard'

describe('GPUCard Component', () => {
  const mockGPU = {
    id: 'test-gpu-1',
    name: 'GeForce RTX 4090',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    specifications: {
      memory: { size: 24, type: 'GDDR6X' },
      power: { tdp: 450 },
      cores: { cuda: 16384 }
    }
  }

  const mockPricing = {
    price: 1599,
    retailer: 'Newegg'
  }

  const mockScore = {
    overall: 95,
    performance: 98,
    value: 85
  }

  it('renders GPU information correctly', () => {
    render(<GPUCard gpu={mockGPU} />)
    
    expect(screen.getByText('GeForce RTX 4090')).toBeInTheDocument()
    expect(screen.getByText('NVIDIA')).toBeInTheDocument()
    expect(screen.getByText('Ada Lovelace')).toBeInTheDocument()
  })

  it('displays memory specifications', () => {
    render(<GPUCard gpu={mockGPU} />)
    
    expect(screen.getByText('Memory')).toBeInTheDocument()
    expect(screen.getByText('24GB GDDR6X')).toBeInTheDocument()
  })

  it('displays core count', () => {
    render(<GPUCard gpu={mockGPU} />)
    
    expect(screen.getByText('Cores')).toBeInTheDocument()
    expect(screen.getByText('16,384')).toBeInTheDocument()
  })

  it('displays TDP information', () => {
    render(<GPUCard gpu={mockGPU} />)
    
    expect(screen.getByText('TDP')).toBeInTheDocument()
    expect(screen.getByText('450W')).toBeInTheDocument()
  })

  it('displays pricing information when provided', () => {
    render(<GPUCard gpu={mockGPU} pricing={mockPricing} />)
    
    expect(screen.getByText('$1599.00')).toBeInTheDocument()
    expect(screen.getByText('at Newegg')).toBeInTheDocument()
    expect(screen.getByText('IN STOCK')).toBeInTheDocument()
  })

  it('shows price comparison text instead of availability status', () => {
    render(<GPUCard gpu={mockGPU} pricing={mockPricing} />)
    expect(screen.getByText('Compare Prices')).toBeInTheDocument()
    expect(screen.getByText('Available at Newegg')).toBeInTheDocument()
  })

  it('displays manufacturer colors correctly', () => {
    const { rerender } = render(<GPUCard gpu={{ ...mockGPU, manufacturer: 'NVIDIA' }} />)
    expect(screen.getByText('NVIDIA')).toHaveClass('text-green-400')

    rerender(<GPUCard gpu={{ ...mockGPU, manufacturer: 'AMD' }} />)
    expect(screen.getByText('AMD')).toHaveClass('text-red-400')

    rerender(<GPUCard gpu={{ ...mockGPU, manufacturer: 'INTEL' }} />)
    expect(screen.getByText('INTEL')).toHaveClass('text-blue-400')
  })

  it('shows score when provided and showScore is true', () => {
    render(<GPUCard gpu={mockGPU} score={mockScore} showScore />)
    
    expect(screen.getByText('95')).toBeInTheDocument()
    expect(screen.getByText('Overall Score')).toBeInTheDocument()
    expect(screen.getByText('85/100')).toBeInTheDocument()
    expect(screen.getByText('Value Score')).toBeInTheDocument()
  })

  it('displays performance score breakdown when showScore is true', () => {
    render(<GPUCard gpu={mockGPU} score={mockScore} showScore />)
    
    expect(screen.getByText('Performance')).toBeInTheDocument()
    expect(screen.getByText('98/100')).toBeInTheDocument()
    
    // Check for progress bar div - it should exist
    const progressBar = screen.getByText('Performance').parentElement?.parentElement?.querySelector('.bg-primary')
    expect(progressBar).toBeInTheDocument()
  })

  it('handles onSelect callback', () => {
    const handleSelect = jest.fn()
    render(<GPUCard gpu={mockGPU} onSelect={handleSelect} />)
    
    const viewDetailsButton = screen.getByText('View Details')
    fireEvent.click(viewDetailsButton)
    
    expect(handleSelect).toHaveBeenCalledWith('test-gpu-1')
  })

  it('handles onCompare callback', () => {
    const handleCompare = jest.fn()
    render(<GPUCard gpu={mockGPU} onCompare={handleCompare} />)
    
    const compareButton = screen.getByText('Compare')
    fireEvent.click(compareButton)
    
    expect(handleCompare).toHaveBeenCalledWith('test-gpu-1')
  })

  it('renders in compact mode', () => {
    render(<GPUCard gpu={mockGPU} compact />)
    
    // In compact mode, specifications should not be displayed
    expect(screen.queryByText('Memory')).not.toBeInTheDocument()
    expect(screen.queryByText('Cores')).not.toBeInTheDocument()
    expect(screen.queryByText('TDP')).not.toBeInTheDocument()
  })

  it('handles missing specifications gracefully', () => {
    const incompleteGPU = {
      id: 'incomplete-gpu',
      name: 'Incomplete GPU',
      manufacturer: 'NVIDIA'
    }

    render(<GPUCard gpu={incompleteGPU} />)
    
    expect(screen.getByText('Incomplete GPU')).toBeInTheDocument()
    expect(screen.getByText('NVIDIA')).toBeInTheDocument()
  })

  it('handles AMD stream processors correctly', () => {
    const amdGPU = {
      ...mockGPU,
      manufacturer: 'AMD',
      specifications: {
        ...mockGPU.specifications,
        cores: { stream: 6144 }
      }
    }

    render(<GPUCard gpu={amdGPU} />)
    
    expect(screen.getByText('6,144')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<GPUCard gpu={mockGPU} className="custom-card-class" />)
    
    // Find the main card container (not the nested div)
    const card = screen.getByText('GeForce RTX 4090').closest('.bg-card')
    expect(card).toHaveClass('custom-card-class')
  })

  it('shows action buttons only when callbacks are provided', () => {
    const { rerender } = render(<GPUCard gpu={mockGPU} />)
    
    expect(screen.queryByText('View Details')).not.toBeInTheDocument()
    expect(screen.queryByText('Compare')).not.toBeInTheDocument()

    rerender(<GPUCard gpu={mockGPU} onSelect={() => {}} />)
    expect(screen.getByText('View Details')).toBeInTheDocument()
    expect(screen.queryByText('Compare')).not.toBeInTheDocument()

    rerender(<GPUCard gpu={mockGPU} onSelect={() => {}} onCompare={() => {}} />)
    expect(screen.getByText('View Details')).toBeInTheDocument()
    expect(screen.getByText('Compare')).toBeInTheDocument()
  })

  it('handles hover states correctly', () => {
    render(<GPUCard gpu={mockGPU} />)
    
    const card = screen.getByText('GeForce RTX 4090').closest('.bg-card')
    expect(card).toHaveClass('bg-card')
    expect(card).toHaveClass('border-border')
    expect(card).toHaveClass('rounded-xl')
  })
})