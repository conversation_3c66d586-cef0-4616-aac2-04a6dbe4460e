'use client'

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from './Button';

interface AffiliateDisclosureProps {
  className?: string;
  variant?: 'banner' | 'modal' | 'inline';
  showDetails?: boolean;
}

export function AffiliateDisclosure({ 
  className, 
  variant = 'banner',
  showDetails = false 
}: AffiliateDisclosureProps) {
  const [isExpanded, setIsExpanded] = useState(showDetails);
  const [isDismissed, setIsDismissed] = useState(false);

  if (isDismissed && variant === 'banner') {
    return null;
  }

  const baseClasses = {
    banner: 'bg-blue-50 border-blue-200 text-blue-800 border-l-4 border-l-blue-500',
    modal: 'bg-card border border-border rounded-lg shadow-lg',
    inline: 'bg-muted/50 border border-border rounded-md'
  };

  const sizeClasses = {
    banner: 'p-4',
    modal: 'p-6',
    inline: 'p-3'
  };

  return (
    <div className={cn(
      baseClasses[variant],
      sizeClasses[variant],
      'transition-all duration-200',
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <svg 
              className="w-4 h-4 text-blue-600" 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span className="font-medium text-sm">
              Affiliate Disclosure
            </span>
          </div>
          
          <p className="text-sm leading-relaxed">
            GPULabs may earn a commission when you purchase through our links. 
            This helps support our platform and keeps our content free.
            {!isExpanded && (
              <button
                onClick={() => setIsExpanded(true)}
                className="ml-1 text-blue-600 hover:text-blue-800 underline"
              >
                Learn more
              </button>
            )}
          </p>

          {isExpanded && (
            <div className="mt-3 text-sm space-y-2 text-foreground-muted">
              <p>
                <strong>How it works:</strong> When you click on product links and make a purchase, 
                we may receive a small commission from retailers like Amazon, Newegg, and Best Buy. 
                This doesn't affect the price you pay.
              </p>
              <p>
                <strong>Our commitment:</strong> We only recommend products we believe in. 
                Our reviews and comparisons are independent and not influenced by affiliate partnerships.
              </p>
              <p>
                <strong>Transparency:</strong> All affiliate links are clearly marked, and we're 
                committed to being transparent about our monetization methods.
              </p>
              {variant !== 'inline' && (
                <button
                  onClick={() => setIsExpanded(false)}
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Show less
                </button>
              )}
            </div>
          )}
        </div>

        {variant === 'banner' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsDismissed(true)}
            className="ml-4 text-blue-600 hover:text-blue-800"
          >
            ×
          </Button>
        )}
      </div>
    </div>
  );
}

// Compact version for footers or small spaces
export function CompactAffiliateDisclosure({ className }: { className?: string }) {
  return (
    <div className={cn(
      'text-xs text-foreground-muted border-t border-border pt-2',
      className
    )}>
      <p>
        <span className="font-medium">Affiliate Disclosure:</span> GPULabs may earn a commission 
        when you purchase through our links. This helps support our platform at no extra cost to you.
      </p>
    </div>
  );
}

// Hook for managing affiliate disclosure preferences
export function useAffiliateDisclosure() {
  const [hasSeenDisclosure, setHasSeenDisclosure] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('gpulabs_affiliate_disclosure_seen') === 'true';
    }
    return false;
  });

  const markDisclosureAsSeen = () => {
    setHasSeenDisclosure(true);
    if (typeof window !== 'undefined') {
      localStorage.setItem('gpulabs_affiliate_disclosure_seen', 'true');
    }
  };

  return {
    hasSeenDisclosure,
    markDisclosureAsSeen
  };
}
