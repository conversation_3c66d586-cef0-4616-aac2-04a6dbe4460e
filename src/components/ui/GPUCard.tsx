'use client'

import React from 'react';
import { cn, formatPrice } from '@/lib/utils';
import { Button } from './Button';
import { affiliateManager } from '@/lib/affiliate-manager';

interface GPUCardProps {
  gpu: {
    id: string;
    name: string;
    manufacturer: string;
    architecture?: string;
    specifications?: {
      memory?: { size?: number; type?: string };
      power?: { tdp?: number };
      cores?: { cuda?: number; stream?: number };
    };
  };
  pricing?: {
    price: number;
    retailer: string;
  };
  score?: {
    overall: number;
    performance: number;
    value: number;
  };
  className?: string;
  onSelect?: (gpuId: string) => void;
  onCompare?: (gpuId: string) => void;
  showScore?: boolean;
  compact?: boolean;
  isSelectedForComparison?: boolean;
}

export function GPUCard({
  gpu,
  pricing,
  score,
  className,
  onSelect,
  onCompare,
  showScore = false,
  compact = false,
  isSelectedForComparison = false
}: GPUCardProps) {

  const handlePriceClick = async (retailer: string, originalUrl?: string) => {
    if (!originalUrl) return;

    try {
      // Generate affiliate link
      const affiliateLink = affiliateManager.generateAffiliateLink(
        retailer,
        originalUrl,
        gpu.id,
        undefined,
        gpu.name
      );

      // Track the click
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Track click via API (fire and forget)
      fetch('/api/affiliate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'track-click',
          retailer,
          gpuId: gpu.id,
          sessionId
        })
      }).catch(err => console.warn('Failed to track affiliate click:', err));

      // Open the affiliate link
      window.open(affiliateLink, '_blank', 'noopener,noreferrer');

    } catch (error) {
      console.error('Error handling price click:', error);
      // Fallback to original URL
      window.open(originalUrl, '_blank', 'noopener,noreferrer');
    }
  };
  const memorySize = gpu.specifications?.memory?.size;
  const memoryType = gpu.specifications?.memory?.type;
  const tdp = gpu.specifications?.power?.tdp;
  const cores = gpu.specifications?.cores?.cuda || gpu.specifications?.cores?.stream;



  const manufacturerColors = {
    'NVIDIA': 'text-green-400',
    'AMD': 'text-red-400',
    'INTEL': 'text-blue-400'
  };

  return (
    <div
      data-testid="gpu-card"
      role="article"
      aria-labelledby={`gpu-name-${gpu.id}`}
      className={cn(
        'bg-card border border-border rounded-xl p-6 hover:bg-card-hover transition-all duration-300 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10',
        compact && 'p-4',
        isSelectedForComparison && 'border-primary bg-primary/5 shadow-lg shadow-primary/20',
        className
      )}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className={cn('text-sm font-medium', manufacturerColors[gpu.manufacturer as keyof typeof manufacturerColors] || 'text-foreground-muted')}>
              {gpu.manufacturer}
            </span>
            {gpu.architecture && (
              <span className="text-xs bg-background-tertiary px-2 py-1 rounded text-foreground-muted">
                {gpu.architecture}
              </span>
            )}
          </div>
          <h3 id={`gpu-name-${gpu.id}`} className="text-lg font-semibold text-foreground mb-2 line-clamp-2">
            {gpu.name}
          </h3>
        </div>
        
        {showScore && score && (
          <div className="ml-4 text-right">
            <div className="text-2xl font-bold text-primary">
              {score.overall}
            </div>
            <div className="text-xs text-foreground-muted">Overall Score</div>
          </div>
        )}
      </div>

      {/* Specifications */}
      {!compact && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          {memorySize && (
            <div className="flex flex-col">
              <span className="text-xs text-foreground-muted uppercase tracking-wide">Memory</span>
              <span className="text-sm font-medium text-foreground">
                {memorySize}GB {memoryType}
              </span>
            </div>
          )}
          
          {cores && (
            <div className="flex flex-col">
              <span className="text-xs text-foreground-muted uppercase tracking-wide">Cores</span>
              <span className="text-sm font-medium text-foreground">
                {cores.toLocaleString()}
              </span>
            </div>
          )}
          
          {tdp && (
            <div className="flex flex-col">
              <span className="text-xs text-foreground-muted uppercase tracking-wide">TDP</span>
              <span className="text-sm font-medium text-foreground">
                {tdp}W
              </span>
            </div>
          )}
          
          {showScore && score && (
            <div className="flex flex-col">
              <span className="text-xs text-foreground-muted uppercase tracking-wide">Value Score</span>
              <span className="text-sm font-medium text-primary">
                {score.value}/100
              </span>
            </div>
          )}
        </div>
      )}

      {/* Pricing */}
      {pricing && (
        <div className="flex items-center justify-between mb-4 p-3 bg-background-secondary rounded-lg">
          <div>
            <div className="text-xl font-bold text-primary">
              {formatPrice(pricing.price)}
            </div>
            <div className="text-xs text-foreground-muted">
              Available at {pricing.retailer}
            </div>
          </div>
          <div className="text-right">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePriceClick(pricing.retailer, `https://${pricing.retailer.toLowerCase().replace(' ', '')}.com`)}
              className="text-sm"
            >
              View Deal
            </Button>
          </div>
        </div>
      )}

      {/* Score Breakdown */}
      {showScore && score && !compact && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-foreground-muted">Performance</span>
            <span className="text-xs font-medium text-foreground">{score.performance}/100</span>
          </div>
          <div className="w-full bg-background-tertiary rounded-full h-1.5">
            <div 
              className="bg-primary h-1.5 rounded-full transition-all duration-500"
              style={{ width: `${score.performance}%` }}
            />
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex gap-2">
        {onSelect && (
          <Button
            variant="primary"
            size="sm"
            onClick={() => onSelect(gpu.id)}
            className="flex-1"
          >
            View Details
          </Button>
        )}
        {onCompare && (
          <Button
            variant={isSelectedForComparison ? "primary" : "outline"}
            size="sm"
            onClick={() => onCompare(gpu.id)}
          >
            {isSelectedForComparison ? 'Selected' : 'Compare'}
          </Button>
        )}
      </div>
    </div>
  );
}