import { prisma } from './prisma'
import { PriceHistory, RetailerPrice } from '@/types/gpu'

export class PricingService {
  /**
   * Get current prices for a GPU from all retailers
   */
  static async getCurrentPrices(gpuId: string): Promise<RetailerPrice[]> {
    const latestPrices = await prisma.gPUPricing.findMany({
      where: { gpuId },
      orderBy: { scrapedAt: 'desc' },
      take: 10, // Get latest prices from different retailers
      distinct: ['retailer'],
    })

    return latestPrices.map(price => ({
      retailer: price.retailer,
      price: Number(price.price),
      availability: price.availability as 'in-stock' | 'limited' | 'out-of-stock',
      shipping: 0, // TODO: Add shipping cost to schema
      url: price.url || '',
      lastUpdated: price.scrapedAt,
      condition: 'new' as const, // TODO: Add condition to schema
    }))
  }

  /**
   * Get price history for a GPU over a specified timeframe
   */
  static async getPriceHistory(
    gpuId: string, 
    timeframe: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): Promise<PriceHistory> {
    const daysBack = {
      week: 7,
      month: 30,
      quarter: 90,
      year: 365,
    }[timeframe]

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - daysBack)

    const priceData = await prisma.gPUPricing.findMany({
      where: {
        gpuId,
        scrapedAt: { gte: startDate },
      },
      orderBy: { scrapedAt: 'asc' },
    })

    const data = priceData.map(price => ({
      date: price.scrapedAt,
      price: Number(price.price),
      retailer: price.retailer,
    }))

    // Calculate trend
    const recentPrices = data.slice(-7) // Last 7 data points
    const earlierPrices = data.slice(0, 7) // First 7 data points
    
    const recentAvg = recentPrices.reduce((sum, p) => sum + p.price, 0) / recentPrices.length
    const earlierAvg = earlierPrices.reduce((sum, p) => sum + p.price, 0) / earlierPrices.length
    
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable'
    if (recentAvg > earlierAvg * 1.05) trend = 'increasing'
    else if (recentAvg < earlierAvg * 0.95) trend = 'decreasing'

    // Find lowest price
    const lowestPrice = data.reduce((min, current) => 
      current.price < min.price ? current : min
    )

    // Calculate average price
    const averagePrice = data.reduce((sum, p) => sum + p.price, 0) / data.length

    return {
      gpuId,
      data,
      trend,
      lowestPrice,
      averagePrice,
    }
  }

  /**
   * Create a price alert for a user
   */
  static async createPriceAlert(
    userId: string,
    gpuId: string,
    targetPrice: number,
    emailAlert: boolean = true,
    pushAlert: boolean = false
  ) {
    return await prisma.priceAlert.create({
      data: {
        userId,
        gpuId,
        targetPrice,
        emailAlert,
        pushAlert,
      },
    })
  }

  /**
   * Check for triggered price alerts
   */
  static async checkPriceAlerts() {
    const activeAlerts = await prisma.priceAlert.findMany({
      where: { 
        isActive: true,
        triggeredAt: null,
      },
      include: {
        user: true,
      },
    })

    const triggeredAlerts = []

    for (const alert of activeAlerts) {
      const currentPrices = await this.getCurrentPrices(alert.gpuId)
      const lowestCurrentPrice = Math.min(...currentPrices.map(p => p.price))

      if (lowestCurrentPrice <= Number(alert.targetPrice)) {
        // Trigger the alert
        await prisma.priceAlert.update({
          where: { id: alert.id },
          data: { 
            triggeredAt: new Date(),
            isActive: false,
          },
        })

        triggeredAlerts.push({
          alert,
          currentPrice: lowestCurrentPrice,
          retailer: currentPrices.find(p => p.price === lowestCurrentPrice)?.retailer,
        })
      }
    }

    return triggeredAlerts
  }

  /**
   * Add new pricing data (for scraping workers)
   */
  static async addPricingData(
    gpuId: string,
    retailer: string,
    price: number,
    availability: string,
    url?: string
  ) {
    return await prisma.gPUPricing.create({
      data: {
        gpuId,
        retailer,
        price,
        availability,
        url,
      },
    })
  }

  /**
   * Get market trends across all GPUs
   */
  static async getMarketTrends() {
    // Get average prices by manufacturer for the last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const trends = await prisma.$queryRaw`
      SELECT 
        g.manufacturer,
        AVG(p.price::numeric) as avg_price,
        COUNT(*) as price_points,
        MIN(p.price::numeric) as min_price,
        MAX(p.price::numeric) as max_price
      FROM gpus g
      JOIN gpu_pricing p ON g.id = p.gpu_id
      WHERE p.scraped_at >= ${thirtyDaysAgo}
      GROUP BY g.manufacturer
      ORDER BY avg_price DESC
    `

    return trends
  }

  /**
   * Clean up old pricing data (keep daily aggregates)
   */
  static async cleanupOldPricingData(daysToKeep: number = 90) {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    // Delete old pricing data, but keep one record per day per retailer per GPU
    await prisma.$executeRaw`
      DELETE FROM gpu_pricing 
      WHERE scraped_at < ${cutoffDate}
      AND id NOT IN (
        SELECT DISTINCT ON (gpu_id, retailer, DATE(scraped_at)) id
        FROM gpu_pricing
        WHERE scraped_at < ${cutoffDate}
        ORDER BY gpu_id, retailer, DATE(scraped_at), scraped_at DESC
      )
    `
  }
}