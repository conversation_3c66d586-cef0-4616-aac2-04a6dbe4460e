import { prisma } from './prisma'

export interface RecommendationCriteria {
  workload: 'gaming' | 'content-creation' | 'ai-ml' | 'mining'
  budget: { min: number; max: number }
  currentSystem?: {
    cpu?: string
    motherboard?: string
    psu?: { wattage: number; efficiency: string }
    case?: { maxGpuLength: number }
  }
  performanceTargets?: {
    resolution: '1080p' | '1440p' | '4K'
    targetFps?: number
    rayTracing?: boolean
    dlss?: boolean
  }
  futureProofing?: number // years
}

export interface GPURecommendation {
  gpu: any
  score: {
    overall: number
    performance: number
    value: number
    compatibility: number
    futureProof: number
  }
  reasoning: string
  pros: string[]
  cons: string[]
  compatibilityScore: number
  valueScore: number
  performanceScore: number
  futureProofScore: number
}

export class RecommendationEngine {
  /**
   * Get GPU recommendations based on criteria
   */
  async getRecommendations(criteria: RecommendationCriteria): Promise<GPURecommendation[]> {
    try {
      // Get GPUs within budget
      const gpus = await this.getGPUsInBudget(criteria.budget)

      if (gpus.length === 0) {
        return []
      }

      // Score each GPU
      const recommendations = await Promise.all(
        gpus.map(gpu => this.scoreGPU(gpu, criteria))
      )

      // Sort by overall score
      return recommendations
        .sort((a, b) => b.score.overall - a.score.overall)
        .slice(0, 10) // Return top 10 recommendations
    } catch (error) {
      console.error('Error getting recommendations:', error)
      throw error
    }
  }

  /**
   * Get workload-optimized recommendations
   */
  async getWorkloadOptimized(workload: string, budget: { min: number; max: number }): Promise<any[]> {
    const criteria: RecommendationCriteria = {
      workload: workload as any,
      budget
    }

    const recommendations = await this.getRecommendations(criteria)
    return recommendations.map(rec => rec.gpu)
  }

  /**
   * Get value recommendations
   */
  async getValueRecommendations(budget: { min: number; max: number }): Promise<any[]> {
    const gpus = await this.getGPUsInBudget(budget)

    // Sort by value score (performance per dollar)
    return gpus
      .map(gpu => ({
        ...gpu,
        valueScore: this.calculateValueScore(gpu)
      }))
      .sort((a, b) => b.valueScore - a.valueScore)
      .slice(0, 5)
  }

  /**
   * Get future-proofing recommendations
   */
  async getFutureProofing(timeframe: number): Promise<any[]> {
    const gpus = await prisma.gPU.findMany({
      where: {
        launchDate: {
          gte: new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000) // Last 2 years
        }
      }
    })

    return gpus
      .map(gpu => ({
        ...gpu,
        futureProofScore: this.calculateFutureProofScore(gpu, timeframe)
      }))
      .sort((a, b) => b.futureProofScore - a.futureProofScore)
      .slice(0, 5)
  }

  /**
   * Private helper methods
   */
  private async getGPUsInBudget(budget: { min: number; max: number }): Promise<any[]> {
    return await prisma.gPU.findMany({
      where: {
        originalMsrp: {
          gte: budget.min,
          lte: budget.max
        }
      }
    })
  }

  private async scoreGPU(gpu: any, criteria: RecommendationCriteria): Promise<GPURecommendation> {
    const performanceScore = this.calculatePerformanceScore(gpu, criteria)
    const valueScore = this.calculateValueScore(gpu)
    const compatibilityScore = this.calculateCompatibilityScore(gpu, criteria)
    const futureProofScore = this.calculateFutureProofScore(gpu, criteria.futureProofing || 3)

    const overallScore = (
      performanceScore * 0.4 +
      valueScore * 0.3 +
      compatibilityScore * 0.2 +
      futureProofScore * 0.1
    )

    const reasoning = this.generateReasoning(gpu, {
      performance: performanceScore,
      value: valueScore,
      compatibility: compatibilityScore,
      futureProof: futureProofScore
    }, criteria)

    return {
      gpu,
      score: {
        overall: overallScore,
        performance: performanceScore,
        value: valueScore,
        compatibility: compatibilityScore,
        futureProof: futureProofScore
      },
      reasoning: reasoning.reasoning,
      pros: reasoning.pros,
      cons: reasoning.cons,
      compatibilityScore,
      valueScore,
      performanceScore,
      futureProofScore
    }
  }

  private calculatePerformanceScore(gpu: any, criteria: RecommendationCriteria): number {
    const multipliers = this.getWorkloadMultipliers(criteria.workload)

    // Use benchmark data if available for more accurate scoring
    const benchmarks = gpu.benchmarks
    if (benchmarks && criteria.workload === 'gaming') {
      return this.calculateGamingScoreFromBenchmarks(gpu, criteria)
    }

    if (benchmarks && criteria.workload === 'content-creation') {
      return this.calculateProductivityScoreFromBenchmarks(gpu)
    }

    // Fallback to specification-based scoring with higher base scores
    const specs = gpu.specifications || {}
    const cores = specs.cores || {}

    let score = 65 // Higher base score

    // CUDA cores / Stream processors - increased multipliers
    if (cores.cudaCores) {
      score += Math.min(25, cores.cudaCores * 0.008 * multipliers.gaming)
    }
    if (cores.streamProcessors) {
      score += Math.min(25, cores.streamProcessors * 0.012 * multipliers.gaming)
    }

    // Memory - increased impact
    const memory = specs.memory || {}
    if (memory.size) {
      score += Math.min(15, memory.size * multipliers.vramWeight * 1.5)
    }

    // Tensor cores for AI workloads - increased impact
    if (cores.tensorCores && criteria.workload === 'ai-ml') {
      score += Math.min(20, cores.tensorCores * multipliers.tensorWeight * 0.25)
    }

    // Architecture bonus for newer GPUs
    if (gpu.architecture && gpu.architecture.includes('Ada') || gpu.architecture.includes('RDNA3')) {
      score += 5
    }

    return Math.min(100, score)
  }

  private calculateGamingScoreFromBenchmarks(gpu: any, criteria: RecommendationCriteria): number {
    const resolution = criteria.performanceTargets?.resolution || '1440p'
    const targetFps = criteria.performanceTargets?.targetFps || 60
    return RecommendationEngine.calculateGamingScore(gpu, resolution, targetFps)
  }

  private calculateProductivityScoreFromBenchmarks(gpu: any): number {
    return RecommendationEngine.calculateProductivityScore(gpu)
  }

  private calculateValueScore(gpu: any): number {
    const price = gpu.originalMsrp || 1000
    const performanceEstimate = this.estimatePerformance(gpu)

    // Calculate value as performance per dollar, normalized to 0-100 scale
    const performancePerDollar = performanceEstimate / (price / 100)

    // Use logarithmic scaling to better distribute scores
    const normalizedValue = Math.log(performancePerDollar + 1) * 25

    return Math.min(100, Math.max(0, normalizedValue))
  }

  private calculateCompatibilityScore(gpu: any, criteria: RecommendationCriteria): number {
    if (!criteria.currentSystem) return 80 // Default good compatibility

    let score = 100
    const system = criteria.currentSystem
    const gpuSpecs = gpu.physicalSpecs || {}
    const powerReqs = gpu.powerRequirements || {}

    // PSU compatibility
    if (system.psu && powerReqs.tdp) {
      const requiredPower = powerReqs.tdp * 1.2 // 20% headroom
      if (system.psu.wattage < requiredPower) {
        score -= 30
      }
    }

    // Physical fit
    if (system.case && gpuSpecs.dimensions) {
      if (system.case.maxGpuLength < gpuSpecs.dimensions.length) {
        score -= 40
      }
    }

    return Math.max(0, score)
  }

  private calculateFutureProofScore(gpu: any, timeframe: number): number {
    const launchDate = gpu.launchDate ? new Date(gpu.launchDate) : new Date()
    const ageInYears = (Date.now() - launchDate.getTime()) / (365 * 24 * 60 * 60 * 1000)

    // Newer GPUs are more future-proof
    const ageScore = Math.max(0, 100 - (ageInYears * 20))

    // VRAM is important for future-proofing
    const specs = gpu.specifications || {}
    const memory = specs.memory || {}
    const vramScore = memory.size ? Math.min(100, memory.size * 8) : 50

    return (ageScore + vramScore) / 2
  }

  private estimatePerformance(gpu: any): number {
    const specs = gpu.specifications || {}
    const cores = specs.cores || {}
    const memory = specs.memory || {}

    let performance = 0

    if (cores.cudaCores) performance += cores.cudaCores * 0.01
    if (cores.streamProcessors) performance += cores.streamProcessors * 0.01
    if (memory.size) performance += memory.size * 10
    if (memory.bandwidth) performance += memory.bandwidth * 0.1

    return performance
  }

  private getWorkloadMultipliers(workload: string) {
    switch (workload) {
      case 'gaming':
        return {
          gaming: 1.0,
          productivity: 0.3,
          aiMl: 0.2,
          vramWeight: 0.3,
          tensorWeight: 0.1
        }
      case 'content-creation':
        return {
          gaming: 0.4,
          productivity: 1.0,
          aiMl: 0.3,
          vramWeight: 0.6,
          tensorWeight: 0.2
        }
      case 'ai-ml':
        return {
          gaming: 0.2,
          productivity: 0.4,
          aiMl: 1.0,
          vramWeight: 0.4,
          tensorWeight: 0.8
        }
      case 'mining':
        return {
          gaming: 0.1,
          productivity: 0.1,
          aiMl: 0.1,
          efficiency: 0.8,
          hashRate: 0.6,
          vramWeight: 0.3,
          tensorWeight: 0.0
        }
      default:
        return {
          gaming: 0.5,
          productivity: 0.5,
          aiMl: 0.3,
          vramWeight: 0.3,
          tensorWeight: 0.2
        }
    }
  }

  private generateReasoning(gpu: any, scores: any, criteria?: RecommendationCriteria) {
    let reasoning = `${gpu.name} is a solid choice for your requirements.`
    const pros: string[] = []
    const cons: string[] = []

    // Add performance reasoning
    if (scores.performance > 85) {
      reasoning += ' It delivers excellent performance'
      pros.push('Excellent performance')
    } else if (scores.performance > 70) {
      reasoning += ' It provides good performance'
      pros.push('Good performance')
    } else {
      reasoning += ' Performance is adequate for basic tasks'
      cons.push('Limited performance for demanding applications')
    }

    // Add detailed workload-specific reasoning
    if (criteria?.workload === 'gaming') {
      reasoning += ' for gaming applications'

      // Gaming-specific features
      if (gpu.specifications?.rayTracing) {
        pros.push('Ray tracing support')
        reasoning += ' with ray tracing capabilities'
      }

      if (gpu.manufacturer === 'NVIDIA' && gpu.specifications?.aiAcceleration) {
        pros.push('DLSS support')
        reasoning += ' and DLSS support for enhanced performance'
      }

      if (gpu.specifications?.memory?.size >= 12) {
        pros.push('Ample VRAM for modern games')
      } else if (gpu.specifications?.memory?.size < 8) {
        cons.push('Limited VRAM may affect future games')
      }

    } else if (criteria?.workload === 'content-creation') {
      reasoning += ' for content creation workflows'

      if (gpu.specifications?.memory?.size >= 16) {
        pros.push('Excellent VRAM for 4K video editing')
        reasoning += ' with excellent VRAM capacity for professional work'
      } else if (gpu.specifications?.memory?.size >= 12) {
        pros.push('Good VRAM for content creation')
      } else {
        cons.push('Limited VRAM for heavy content creation')
      }

      if (gpu.specifications?.encoders?.nvenc || gpu.specifications?.encoders?.av1) {
        pros.push('Hardware encoding support')
        reasoning += ' and hardware encoding capabilities'
      }

    } else if (criteria?.workload === 'ai-ml') {
      reasoning += ' for AI and machine learning workloads'

      if (gpu.specifications?.cores?.tensorCores) {
        pros.push('Tensor cores for AI acceleration')
        reasoning += ' with dedicated tensor cores for AI acceleration'
      }

      if (gpu.specifications?.memory?.size >= 24) {
        pros.push('Excellent VRAM for large AI models')
      } else if (gpu.specifications?.memory?.size < 12) {
        cons.push('Limited VRAM for large AI models')
      }

    } else if (criteria?.workload === 'mining') {
      reasoning += ' for cryptocurrency mining'

      if (gpu.specifications?.powerEfficiency) {
        pros.push('Good power efficiency')
      }

      if (gpu.specifications?.memory?.type === 'GDDR6X') {
        pros.push('High memory bandwidth')
      }
    }

    // Value assessment
    if (scores.value > 80) {
      pros.push('Excellent value for money')
      reasoning += '. Offers excellent value for the performance delivered'
    } else if (scores.value < 50) {
      cons.push('Expensive for the performance offered')
      reasoning += '. However, it comes at a premium price'
    }

    reasoning += '.'

    return {
      reasoning,
      pros,
      cons
    }
  }

  // Static methods for testing compatibility
  static filterByBudget(gpus: any[], budget: { min: number; max: number }): any[] {
    return gpus.filter(gpu => {
      const price = gpu.originalMsrp
      // Include GPUs without pricing data for now, they'll be scored lower
      if (!price || price === null) return true
      return price >= budget.min && price <= budget.max
    })
  }

  static getWorkloadMultipliers(workload: string) {
    switch (workload) {
      case 'gaming':
        return {
          gaming: 1.0,
          productivity: 0.3,
          aiMl: 0.2,
          vramWeight: 0.3,
          tensorWeight: 0.1
        }
      case 'content-creation':
        return {
          gaming: 0.4,
          productivity: 1.0,
          aiMl: 0.3,
          vramWeight: 0.6,
          tensorWeight: 0.2
        }
      case 'ai-ml':
        return {
          gaming: 0.2,
          productivity: 0.4,
          aiMl: 1.0,
          vramWeight: 0.4,
          tensorWeight: 0.8
        }
      case 'mining':
        return {
          gaming: 0.1,
          productivity: 0.1,
          aiMl: 0.1,
          efficiency: 0.8,
          hashRate: 0.6,
          vramWeight: 0.3,
          tensorWeight: 0.0
        }
      default:
        return {
          gaming: 0.5,
          productivity: 0.5,
          aiMl: 0.3,
          vramWeight: 0.3,
          tensorWeight: 0.2
        }
    }
  }

  static async getRecommendations(gpus: any[], criteria: RecommendationCriteria): Promise<GPURecommendation[]> {
    const engine = new RecommendationEngine()

    // Filter GPUs by budget first
    const budgetFiltered = this.filterByBudget(gpus, criteria.budget)

    // Score each GPU
    const recommendations = await Promise.all(
      budgetFiltered.map(gpu => engine.scoreGPU(gpu, criteria))
    )

    // Sort by overall score
    return recommendations
      .sort((a, b) => b.score.overall - a.score.overall)
      .slice(0, 10)
  }

  static calculateGamingScore(gpu: any, resolution: string, targetFps: number): number {
    const benchmarks = gpu.benchmarks?.gaming
    if (!benchmarks) return 50 // Default score if no benchmarks

    let actualFps = 0
    switch (resolution) {
      case '1080p':
        actualFps = benchmarks.fps1080p || 0
        break
      case '1440p':
        actualFps = benchmarks.fps1440p || 0
        break
      case '4K':
      case '4k':
        actualFps = benchmarks.fps4k || 0
        break
      default:
        actualFps = benchmarks.fps1080p || 0
    }

    if (actualFps === 0) return 30 // Low score if no data

    // Score based on how well it meets target FPS
    const fpsRatio = actualFps / targetFps
    if (fpsRatio >= 1.5) return 95 // Exceeds target significantly
    if (fpsRatio >= 1.2) return 85 // Exceeds target comfortably
    if (fpsRatio >= 1.0) return 75 // Meets target
    if (fpsRatio >= 0.8) return 60 // Close to target
    if (fpsRatio >= 0.6) return 45 // Below target but playable
    return 25 // Poor performance
  }

  static calculateProductivityScore(gpu: any): number {
    const benchmarks = gpu.benchmarks?.productivity
    const specs = gpu.specifications

    let score = 50 // Base score

    // Add score based on productivity benchmarks
    if (benchmarks) {
      if (benchmarks.blenderScore) {
        score += Math.min(25, benchmarks.blenderScore / 150) // Reduced to allow VRAM differentiation
      }
      if (benchmarks.renderScore) {
        score += Math.min(15, benchmarks.renderScore / 300) // Reduced to allow VRAM differentiation
      }
    }

    // Add score based on VRAM (important for productivity) - improved scaling
    if (specs?.memory?.size) {
      // Use logarithmic scaling to better differentiate VRAM sizes
      const vramBonus = Math.min(25, Math.log2(specs.memory.size) * 4)
      score += vramBonus
    }

    return Math.min(100, score)
  }

  static calculateValueScore(gpu: any, workload: string, resolution: string): number {
    const price = gpu.originalMsrp || 1000
    let performance = 50

    if (workload === 'gaming') {
      performance = this.calculateGamingScore(gpu, resolution, 60)
    } else if (workload === 'productivity') {
      performance = this.calculateProductivityScore(gpu)
    }

    // Calculate value as performance per dollar, normalized to 0-100 scale
    const performancePerDollar = performance / (price / 100)

    // Use logarithmic scaling to better distribute scores within 0-100 range
    const normalizedValue = Math.log(performancePerDollar + 1) * 25

    return Math.min(100, Math.max(0, normalizedValue))
  }

  static generateRecommendationReasoning(gpu: any, scores: any, preferences: any): any {
    let reasoning = `${gpu.name} is a solid choice for your needs.`
    const pros: string[] = []
    const cons: string[] = []

    // Performance reasoning
    if (scores.performance > 85) {
      reasoning += ' It delivers excellent performance.'
      if (preferences.workload === 'gaming') {
        pros.push('Excellent gaming performance')
      } else if (preferences.workload === 'content-creation') {
        pros.push('Excellent productivity performance')
      } else {
        pros.push('Excellent performance')
      }
    } else if (scores.performance > 70) {
      reasoning += ' It provides good performance.'
      pros.push('Good performance')
    } else {
      reasoning += ' Performance is adequate for basic tasks.'
      cons.push('Limited performance')
    }

    // Value reasoning
    if (scores.value <= 60) {
      cons.push('Expensive for the performance offered')
    } else if (scores.value > 80) {
      pros.push('Great value for money')
    }

    // Workload-specific reasoning
    if (preferences.workload === 'gaming') {
      reasoning += ' For gaming'
      if ((preferences.performanceTargets?.rayTracing || preferences.features?.includes('rayTracing')) && gpu.specifications?.rayTracing) {
        pros.push('Ray tracing support')
        reasoning += ' with ray tracing capabilities'
      }
      if (gpu.manufacturer === 'NVIDIA' && gpu.specifications?.aiAcceleration) {
        pros.push('DLSS support')
        reasoning += ' and DLSS support'
      }
    } else if (preferences.workload === 'content-creation') {
      reasoning += ' For content creation'
      if (gpu.specifications?.memory?.size >= 16) {
        pros.push('Ample VRAM for content creation')
      }
    }

    // Add cons based on GPU characteristics
    if (gpu.powerRequirements?.tdp > 350) {
      cons.push('High power consumption')
    }

    if (gpu.originalMsrp > 1200) {
      cons.push('Premium pricing')
    }

    reasoning += '.'

    return {
      reasoning,
      pros,
      cons,
      summary: `Overall score: ${scores.overall}/100`
    }
  }
}