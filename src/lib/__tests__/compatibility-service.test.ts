import { CompatibilityService } from '../compatibility-service'
import { SystemSpecs } from '@/types/gpu'

describe('CompatibilityService', () => {
  const mockGPU = {
    id: 'test-gpu-1',
    name: 'Test GPU RTX 4090',
    manufacturer: 'NVIDIA',
    specifications: {
      memory: { size: 24, type: 'GDDR6X' },
      cores: { cudaCores: 16384 }
    },
    physicalSpecs: {
      dimensions: { length: 304, width: 137, height: 61 }
    },
    powerRequirements: {
      tdp: 450,
      recommendedPSU: 850,
      connectors: ['12VHPWR']
    }
  }

  const mockSystem: SystemSpecs = {
    cpu: {
      model: 'Intel i7-12700K',
      cores: 12,
      threads: 20,
      baseClock: 3600
    },
    motherboard: {
      model: 'ASUS ROG Strix Z690-E',
      chipset: 'Z690',
      pcieSlots: ['PCIe 4.0 x16']
    },
    psu: {
      wattage: 850,
      efficiency: '80+ Gold',
      connectors: ['12VHPWR']
    },
    case: {
      model: 'Fractal Design Define 7',
      maxGPULength: 315,
      maxGPUWidth: 140,
      maxGPUHeight: 65
    },
    ram: {
      size: 32,
      speed: 3200,
      type: 'DDR4'
    }
  }

  describe('checkCompatibility', () => {
    it('should return compatible for a well-matched system', async () => {
      const result = await CompatibilityService.checkCompatibility(mockGPU, mockSystem)

      // The system might return 'warning' due to tight clearances (within 95% of limit)
      expect(['compatible', 'warning']).toContain(result.overall)
      expect(result.power.status).toBe('compatible')
      expect(['compatible', 'warning']).toContain(result.physical.status)
      expect(Array.isArray(result.recommendations)).toBe(true)
    })

    it('should return incompatible for insufficient PSU wattage', async () => {
      const insufficientPSUSystem = {
        ...mockSystem,
        psu: {
          ...mockSystem.psu,
          wattage: 500
        }
      }

      const result = await CompatibilityService.checkCompatibility(mockGPU, insufficientPSUSystem)

      expect(result.overall).toBe('incompatible')
      expect(result.power.status).toBe('incompatible')
      expect(result.recommendations).toContain('Upgrade to a 850W or higher PSU (currently 500W)')
    })

    it('should return incompatible for missing power connectors', async () => {
      const missingConnectorSystem = {
        ...mockSystem,
        psu: {
          ...mockSystem.psu,
          connectors: ['8-pin', '6-pin']
        }
      }

      const result = await CompatibilityService.checkCompatibility(mockGPU, missingConnectorSystem)

      expect(result.overall).toBe('incompatible')
      expect(result.power.status).toBe('incompatible')
      expect(result.power.connectors.missing).toContain('12VHPWR')
    })

    it('should return incompatible for insufficient case clearance', async () => {
      const smallCaseSystem = {
        ...mockSystem,
        case: {
          ...mockSystem.case,
          maxGPULength: 250 // Too small for 304mm GPU
        }
      }

      const result = await CompatibilityService.checkCompatibility(mockGPU, smallCaseSystem)

      expect(result.overall).toBe('incompatible')
      expect(result.physical.status).toBe('incompatible')
      expect(Array.isArray(result.recommendations)).toBe(true)
    })
  })

  describe('validatePowerSupply', () => {
    it('should validate compatible PSU', () => {
      const result = CompatibilityService.validatePowerSupply(mockGPU, mockSystem.psu)

      expect(result.status).toBe('compatible')
      expect(result.required).toBe(850)
      expect(result.available).toBe(850)
      expect(result.connectors.missing).toHaveLength(0)
    })

    it('should detect insufficient wattage', () => {
      const lowWattagePSU = { ...mockSystem.psu, wattage: 400 }
      const result = CompatibilityService.validatePowerSupply(mockGPU, lowWattagePSU)

      expect(result.status).toBe('incompatible')
      expect(result.available).toBe(400)
      expect(result.required).toBe(850)
    })

    it('should detect missing connectors', () => {
      const incompatiblePSU = {
        ...mockSystem.psu,
        connectors: ['8-pin', '6-pin']
      }
      const result = CompatibilityService.validatePowerSupply(mockGPU, incompatiblePSU)

      expect(result.status).toBe('incompatible')
      expect(result.connectors.missing).toContain('12VHPWR')
    })

    it('should handle warning status for marginal PSU', () => {
      const marginalPSU = { ...mockSystem.psu, wattage: 700 }
      const result = CompatibilityService.validatePowerSupply(mockGPU, marginalPSU)

      expect(result.status).toBe('warning')
    })
  })

  describe('checkPhysicalFit', () => {
    it('should validate compatible case dimensions', () => {
      const result = CompatibilityService.checkPhysicalFit(mockGPU, mockSystem.case)

      // The system might return 'warning' due to tight clearances (within 95% of limit)
      expect(['compatible', 'warning']).toContain(result.status)
      expect(result.clearance.length.required).toBe(304)
      expect(result.clearance.length.available).toBe(315)
    })

    it('should detect insufficient length clearance', () => {
      const smallCase = { ...mockSystem.case, maxGPULength: 250 }
      const result = CompatibilityService.checkPhysicalFit(mockGPU, smallCase)

      expect(result.status).toBe('incompatible')
      expect(result.clearance.length.required).toBe(304)
      expect(result.clearance.length.available).toBe(250)
    })

    it('should detect insufficient width clearance', () => {
      const narrowCase = { ...mockSystem.case, maxGPUWidth: 100 }
      const result = CompatibilityService.checkPhysicalFit(mockGPU, narrowCase)

      expect(result.status).toBe('incompatible')
      expect(result.clearance.width.required).toBe(137)
      expect(result.clearance.width.available).toBe(100)
    })

    it('should handle warning status for tight fit', () => {
      const tightCase = { ...mockSystem.case, maxGPULength: 310 } // 95% of required
      const result = CompatibilityService.checkPhysicalFit(mockGPU, tightCase)

      expect(result.status).toBe('warning')
    })
  })

  describe('analyzeCPUBottleneck', () => {
    it('should detect no bottleneck with high-end CPU', () => {
      const highEndCPU = {
        model: 'Intel i9-13900K',
        cores: 24,
        threads: 32,
        baseClock: 3000
      }

      const result = CompatibilityService.analyzeCPUBottleneck(mockGPU, highEndCPU)

      expect(result.severity).toBe('none')
      expect(result.cpuBottleneck).toBe(0)
    })

    it('should detect severe bottleneck with low-end CPU', () => {
      const lowEndCPU = {
        model: 'Intel i3-10100',
        cores: 4,
        threads: 8,
        baseClock: 3600
      }

      const result = CompatibilityService.analyzeCPUBottleneck(mockGPU, lowEndCPU)

      expect(result.severity).toBe('moderate')
      expect(result.cpuBottleneck).toBeGreaterThan(0)
      expect(result.recommendation).toContain('Consider upgrading your CPU')
    })

    it('should handle mid-range CPU appropriately', () => {
      const midRangeCPU = {
        model: 'Intel i5-12600K',
        cores: 10,
        threads: 16,
        baseClock: 3700
      }

      const result = CompatibilityService.analyzeCPUBottleneck(mockGPU, midRangeCPU)

      expect(['none', 'mild', 'moderate']).toContain(result.severity)
    })
  })

  describe('getGPURequirements', () => {
    it('should return comprehensive GPU requirements', () => {
      const requirements = CompatibilityService.getGPURequirements(mockGPU)

      expect(requirements.power.tdp).toBe(450)
      expect(requirements.power.recommendedPSU).toBe(850)
      expect(requirements.power.connectors).toContain('12VHPWR')
      expect(requirements.physical.length).toBe(304)
      expect(requirements.physical.width).toBe(137)
      expect(requirements.physical.height).toBe(61)
      expect(requirements.physical.slots).toBe(4) // Math.ceil(61/20)
    })

    it('should handle missing specifications gracefully', () => {
      const incompleteGPU = {
        id: 'incomplete-gpu',
        name: 'Incomplete GPU',
        manufacturer: 'Test'
      }

      const requirements = CompatibilityService.getGPURequirements(incompleteGPU)

      expect(requirements.power.tdp).toBe(0)
      expect(requirements.physical.length).toBe(0)
      expect(requirements.physical.slots).toBe(2) // Default
    })
  })

  describe('getWorkloadCompatibilityTips', () => {
    it('should return gaming-specific tips', () => {
      const tips = CompatibilityService.getWorkloadCompatibilityTips('gaming')

      expect(tips).toContain('Ensure your PSU can handle power spikes during gaming')
      expect(tips).toContain('Check that your case has adequate airflow for cooling')
    })

    it('should return content creation tips', () => {
      const tips = CompatibilityService.getWorkloadCompatibilityTips('content-creation')

      expect(tips).toContain('Ensure adequate PSU headroom for sustained workloads')
      expect(tips).toContain('Consider multi-GPU setups if your motherboard supports it')
    })

    it('should return AI/ML specific tips', () => {
      const tips = CompatibilityService.getWorkloadCompatibilityTips('ai-ml')

      expect(tips).toContain('Ensure PSU can handle 100% GPU utilization for extended periods')
      expect(tips).toContain('Consider system memory requirements for large datasets')
    })

    it('should return mining specific tips', () => {
      const tips = CompatibilityService.getWorkloadCompatibilityTips('mining')

      expect(tips).toContain('Calculate power efficiency and electricity costs')
      expect(tips).toContain('Consider multiple GPU compatibility and spacing')
    })

    it('should default to gaming tips for unknown workload', () => {
      const tips = CompatibilityService.getWorkloadCompatibilityTips('unknown-workload')

      expect(tips).toContain('Ensure your PSU can handle power spikes during gaming')
    })
  })
})