/**
 * Integration tests for Recommendation Engine using real GPU data
 * These tests require the database to be running and seeded with GPU data
 */

import { RecommendationEngine } from '../../recommendation-engine'
import { GPUService } from '../../gpu-service'

describe('RecommendationEngine Integration Tests', () => {
  const shouldSkip = process.env.CI === 'true' || process.env.SKIP_INTEGRATION_TESTS === 'true'

  beforeAll(() => {
    if (shouldSkip) {
      console.log('Skipping recommendation integration tests - no database connection')
    }
  })

  describe('Real GPU Recommendations', () => {
    it('should generate gaming recommendations from real GPU data', async () => {
      if (shouldSkip) return

      // Get real GPU data
      const gpuData = await GPUService.getAllGPUs({ limit: 20 })
      
      if (gpuData.gpus.length === 0) {
        console.log('No GPU data available for testing')
        return
      }

      const preferences = {
        workload: 'gaming' as const,
        budget: { min: 300, max: 1500 },
        performance: { resolution: '1440p' as const, targetFps: 60 },
        features: ['rayTracing'],
        priorities: { performance: 8, value: 6, efficiency: 4 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        preferences
      )

      expect(Array.isArray(recommendations)).toBe(true)
      expect(recommendations.length).toBeGreaterThan(0)

      // Check recommendation structure
      recommendations.forEach(rec => {
        expect(rec).toHaveProperty('gpu')
        expect(rec).toHaveProperty('score')
        expect(rec).toHaveProperty('reasoning')
        expect(rec).toHaveProperty('pros')
        expect(rec).toHaveProperty('cons')

        // Score validation
        expect(rec.score.overall).toBeGreaterThanOrEqual(0)
        expect(rec.score.overall).toBeLessThanOrEqual(100)
        expect(rec.score.performance).toBeGreaterThanOrEqual(0)
        expect(rec.score.performance).toBeLessThanOrEqual(100)
        expect(rec.score.value).toBeGreaterThanOrEqual(0)
        expect(rec.score.value).toBeLessThanOrEqual(100)

        // Content validation
        expect(typeof rec.reasoning).toBe('string')
        expect(rec.reasoning.length).toBeGreaterThan(0)
        expect(Array.isArray(rec.pros)).toBe(true)
        expect(Array.isArray(rec.cons)).toBe(true)
      })

      // Recommendations should be sorted by overall score
      for (let i = 1; i < recommendations.length; i++) {
        expect(recommendations[i-1].score.overall).toBeGreaterThanOrEqual(
          recommendations[i].score.overall
        )
      }
    })

    it('should generate content creation recommendations from real data', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 15 })
      
      if (gpuData.gpus.length === 0) return

      const preferences = {
        workload: 'content-creation' as const,
        budget: { min: 500, max: 2000 },
        performance: { resolution: '4k' as const, targetFps: 30 },
        features: ['nvenc', 'largeVram'],
        priorities: { performance: 9, value: 5, efficiency: 6 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        preferences
      )

      expect(recommendations.length).toBeGreaterThan(0)

      // Content creation should prioritize GPUs with more VRAM
      const topRecommendation = recommendations[0]
      if (topRecommendation.gpu.specifications?.memory?.size) {
        expect(topRecommendation.gpu.specifications.memory.size).toBeGreaterThanOrEqual(8)
      }

      // Check that reasoning mentions content creation
      expect(topRecommendation.reasoning.toLowerCase()).toContain('content')
    })

    it('should handle budget constraints with real GPU pricing', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 30 })
      
      if (gpuData.gpus.length === 0) return

      const budgetPreferences = {
        workload: 'gaming' as const,
        budget: { min: 200, max: 500 },
        performance: { resolution: '1080p' as const, targetFps: 60 },
        features: [],
        priorities: { performance: 6, value: 9, efficiency: 5 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        budgetPreferences
      )

      // All recommendations should be within budget
      recommendations.forEach(rec => {
        if (rec.gpu.originalMsrp) {
          expect(rec.gpu.originalMsrp).toBeGreaterThanOrEqual(200)
          expect(rec.gpu.originalMsrp).toBeLessThanOrEqual(500)
        }
      })

      // Budget-conscious recommendations should have high value scores
      if (recommendations.length > 0) {
        const avgValueScore = recommendations.reduce((sum, rec) => sum + rec.score.value, 0) / recommendations.length
        expect(avgValueScore).toBeGreaterThan(60) // Should prioritize value
      }
    })

    it('should differentiate between NVIDIA and AMD GPUs appropriately', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 25 })
      
      if (gpuData.gpus.length === 0) return

      const nvidiaPreferences = {
        workload: 'ai-ml' as const,
        budget: { min: 400, max: 1200 },
        performance: { resolution: '1440p' as const, targetFps: 60 },
        features: ['dlss', 'tensorCores'],
        priorities: { performance: 9, value: 5, efficiency: 6 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        nvidiaPreferences
      )

      if (recommendations.length > 0) {
        // For AI/ML workloads, NVIDIA GPUs should generally score higher due to CUDA/Tensor cores
        const nvidiaRecs = recommendations.filter(rec => rec.gpu.manufacturer === 'NVIDIA')
        const amdRecs = recommendations.filter(rec => rec.gpu.manufacturer === 'AMD')

        if (nvidiaRecs.length > 0 && amdRecs.length > 0) {
          const avgNvidiaScore = nvidiaRecs.reduce((sum, rec) => sum + rec.score.overall, 0) / nvidiaRecs.length
          const avgAmdScore = amdRecs.reduce((sum, rec) => sum + rec.score.overall, 0) / amdRecs.length
          
          // NVIDIA should generally score higher for AI/ML workloads
          expect(avgNvidiaScore).toBeGreaterThanOrEqual(avgAmdScore - 10) // Allow some tolerance
        }
      }
    })

    it('should provide realistic performance expectations', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 20 })
      
      if (gpuData.gpus.length === 0) return

      const highEndPreferences = {
        workload: 'gaming' as const,
        budget: { min: 1000, max: 2000 },
        performance: { resolution: '4k' as const, targetFps: 60 },
        features: ['rayTracing'],
        priorities: { performance: 10, value: 3, efficiency: 4 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        highEndPreferences
      )

      if (recommendations.length > 0) {
        const topRec = recommendations[0]
        
        // High-end GPUs should have high performance scores for 4K gaming
        expect(topRec.score.performance).toBeGreaterThan(70)
        
        // Should mention 4K capability in reasoning or pros
        const text = (topRec.reasoning + ' ' + topRec.pros.join(' ')).toLowerCase()
        expect(text).toMatch(/4k|ultra|high.*resolution|excellent.*performance/)
      }
    })

    it('should handle edge cases with real data', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 10 })
      
      if (gpuData.gpus.length === 0) return

      // Test with very restrictive budget
      const restrictivePreferences = {
        workload: 'gaming' as const,
        budget: { min: 50, max: 100 },
        performance: { resolution: '1080p' as const, targetFps: 60 },
        features: [],
        priorities: { performance: 5, value: 10, efficiency: 5 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        restrictivePreferences
      )

      // Should handle case where no GPUs meet budget
      expect(Array.isArray(recommendations)).toBe(true)
      // If there are recommendations, they should all be within budget
      recommendations.forEach(rec => {
        if (rec.gpu.originalMsrp) {
          expect(rec.gpu.originalMsrp).toBeLessThanOrEqual(100)
        }
      })
    })
  })

  describe('Recommendation Quality with Real Data', () => {
    it('should provide diverse recommendations across price ranges', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 30 })
      
      if (gpuData.gpus.length < 5) return

      const preferences = {
        workload: 'gaming' as const,
        budget: { min: 200, max: 1500 },
        performance: { resolution: '1440p' as const, targetFps: 60 },
        features: [],
        priorities: { performance: 7, value: 7, efficiency: 6 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        preferences
      )

      if (recommendations.length >= 3) {
        const prices = recommendations
          .filter(rec => rec.gpu.originalMsrp)
          .map(rec => rec.gpu.originalMsrp!)
          .sort((a, b) => a - b)

        if (prices.length >= 3) {
          // Should have some diversity in pricing
          const priceRange = prices[prices.length - 1] - prices[0]
          expect(priceRange).toBeGreaterThan(100) // At least $100 difference
        }
      }
    })

    it('should provide consistent scoring across multiple runs', async () => {
      if (shouldSkip) return

      const gpuData = await GPUService.getAllGPUs({ limit: 10 })
      
      if (gpuData.gpus.length === 0) return

      const preferences = {
        workload: 'gaming' as const,
        budget: { min: 400, max: 800 },
        performance: { resolution: '1440p' as const, targetFps: 60 },
        features: [],
        priorities: { performance: 8, value: 6, efficiency: 5 }
      }

      const recommendations1 = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        preferences
      )
      
      const recommendations2 = await RecommendationEngine.getRecommendations(
        gpuData.gpus, 
        preferences
      )

      // Results should be identical for same input
      expect(recommendations1.length).toBe(recommendations2.length)
      
      for (let i = 0; i < Math.min(recommendations1.length, recommendations2.length); i++) {
        expect(recommendations1[i].gpu.id).toBe(recommendations2[i].gpu.id)
        expect(recommendations1[i].score.overall).toBe(recommendations2[i].score.overall)
      }
    })
  })
})