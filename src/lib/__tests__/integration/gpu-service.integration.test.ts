/**
 * Integration tests for GPU Service using real database
 * These tests require the database to be running and seeded with data
 */

import { GPUService } from '../../gpu-service'

describe('GPUService Integration Tests', () => {
  // Skip these tests if we're in CI or don't have a database connection
  const shouldSkip = process.env.CI === 'true' || process.env.SKIP_INTEGRATION_TESTS === 'true'

  beforeAll(() => {
    if (shouldSkip) {
      console.log('Skipping integration tests - no database connection')
    }
  })

  describe('Real GPU Data Operations', () => {
    it('should fetch real GPUs from database', async () => {
      if (shouldSkip) return

      const result = await GPUService.getAllGPUs({ limit: 5 })

      expect(result.gpus).toBeDefined()
      expect(Array.isArray(result.gpus)).toBe(true)
      expect(result.total).toBeGreaterThanOrEqual(0)
      expect(result.page).toBe(1)
      expect(result.limit).toBe(5)

      // If we have GPUs, test their structure
      if (result.gpus.length > 0) {
        const gpu = result.gpus[0]
        expect(gpu).toHaveProperty('id')
        expect(gpu).toHaveProperty('name')
        expect(gpu).toHaveProperty('manufacturer')
        expect(['NVIDIA', 'AMD', 'Intel']).toContain(gpu.manufacturer)
      }
    })

    it('should search for real NVIDIA GPUs', async () => {
      if (shouldSkip) return

      const results = await GPUService.searchGPUs('NVIDIA', 3)

      expect(Array.isArray(results)).toBe(true)
      
      // If we have results, they should all be NVIDIA GPUs
      results.forEach(gpu => {
        expect(gpu.manufacturer).toBe('NVIDIA')
      })
    })

    it('should filter GPUs by manufacturer', async () => {
      if (shouldSkip) return

      const nvidiaGPUs = await GPUService.getGPUsByManufacturer('NVIDIA')
      const amdGPUs = await GPUService.getGPUsByManufacturer('AMD')

      expect(Array.isArray(nvidiaGPUs)).toBe(true)
      expect(Array.isArray(amdGPUs)).toBe(true)

      // All results should match the requested manufacturer
      nvidiaGPUs.forEach(gpu => {
        expect(gpu.manufacturer).toBe('NVIDIA')
      })

      amdGPUs.forEach(gpu => {
        expect(gpu.manufacturer).toBe('AMD')
      })
    })

    it('should get GPU statistics from real data', async () => {
      if (shouldSkip) return

      const stats = await GPUService.getGPUStatistics()

      expect(stats).toHaveProperty('total')
      expect(stats).toHaveProperty('byManufacturer')
      expect(typeof stats.total).toBe('number')
      expect(stats.total).toBeGreaterThanOrEqual(0)

      // Check manufacturer breakdown
      expect(stats.byManufacturer).toHaveProperty('NVIDIA')
      expect(stats.byManufacturer).toHaveProperty('AMD')
      expect(typeof stats.byManufacturer.NVIDIA).toBe('number')
      expect(typeof stats.byManufacturer.AMD).toBe('number')
    })

    it('should handle price range filtering with real data', async () => {
      if (shouldSkip) return

      const budgetGPUs = await GPUService.getGPUsByPriceRange(200, 600)
      const highEndGPUs = await GPUService.getGPUsByPriceRange(1000, 2000)

      expect(Array.isArray(budgetGPUs)).toBe(true)
      expect(Array.isArray(highEndGPUs)).toBe(true)

      // Check price ranges
      budgetGPUs.forEach(gpu => {
        if (gpu.originalMsrp) {
          expect(gpu.originalMsrp).toBeGreaterThanOrEqual(200)
          expect(gpu.originalMsrp).toBeLessThanOrEqual(600)
        }
      })

      highEndGPUs.forEach(gpu => {
        if (gpu.originalMsrp) {
          expect(gpu.originalMsrp).toBeGreaterThanOrEqual(1000)
          expect(gpu.originalMsrp).toBeLessThanOrEqual(2000)
        }
      })
    })

    it('should fetch specific GPU by ID', async () => {
      if (shouldSkip) return

      // First get a list of GPUs to get a real ID
      const gpuList = await GPUService.getAllGPUs({ limit: 1 })
      
      if (gpuList.gpus.length > 0) {
        const gpuId = gpuList.gpus[0].id
        const gpu = await GPUService.getGPUById(gpuId)

        expect(gpu).toBeDefined()
        expect(gpu?.id).toBe(gpuId)
        expect(gpu).toHaveProperty('name')
        expect(gpu).toHaveProperty('manufacturer')
      }
    })

    it('should handle pagination correctly with real data', async () => {
      if (shouldSkip) return

      const page1 = await GPUService.getAllGPUs({ page: 1, limit: 5 })
      const page2 = await GPUService.getAllGPUs({ page: 2, limit: 5 })

      expect(page1.page).toBe(1)
      expect(page2.page).toBe(2)
      expect(page1.limit).toBe(5)
      expect(page2.limit).toBe(5)

      // If we have enough data, pages should be different
      if (page1.total > 5) {
        expect(page1.gpus).not.toEqual(page2.gpus)
      }
    })
  })

  describe('Data Validation with Real Data', () => {
    it('should validate real GPU data structure', async () => {
      if (shouldSkip) return

      const result = await GPUService.getAllGPUs({ limit: 10 })

      result.gpus.forEach(gpu => {
        // Required fields
        expect(gpu.id).toBeDefined()
        expect(typeof gpu.id).toBe('string')
        expect(gpu.name).toBeDefined()
        expect(typeof gpu.name).toBe('string')
        expect(gpu.manufacturer).toBeDefined()
        expect(['NVIDIA', 'AMD', 'Intel']).toContain(gpu.manufacturer)

        // Optional but common fields
        if (gpu.specifications) {
          expect(typeof gpu.specifications).toBe('object')
          
          if (gpu.specifications.memory) {
            expect(typeof gpu.specifications.memory).toBe('object')
            if (gpu.specifications.memory.size) {
              expect(typeof gpu.specifications.memory.size).toBe('number')
              expect(gpu.specifications.memory.size).toBeGreaterThan(0)
            }
          }

          if (gpu.specifications.cores) {
            expect(typeof gpu.specifications.cores).toBe('object')
            // Should have either CUDA cores or stream processors
            const hasCores = gpu.specifications.cores.cudaCores || 
                           gpu.specifications.cores.streamProcessors ||
                           gpu.specifications.cores.executionUnits
            if (hasCores) {
              expect(typeof hasCores).toBe('number')
              expect(hasCores).toBeGreaterThan(0)
            }
          }
        }

        if (gpu.originalMsrp) {
          expect(typeof gpu.originalMsrp).toBe('number')
          expect(gpu.originalMsrp).toBeGreaterThan(0)
          expect(gpu.originalMsrp).toBeLessThan(10000) // Reasonable upper bound
        }
      })
    })

    it('should have consistent manufacturer naming', async () => {
      if (shouldSkip) return

      const allGPUs = await GPUService.getAllGPUs({ limit: 100 })
      const manufacturers = new Set(allGPUs.gpus.map(gpu => gpu.manufacturer))

      // Check that manufacturers are properly capitalized
      manufacturers.forEach(manufacturer => {
        expect(['NVIDIA', 'AMD', 'Intel', 'INTEL']).toContain(manufacturer)
      })
    })
  })

  describe('Performance Tests with Real Data', () => {
    it('should handle large result sets efficiently', async () => {
      if (shouldSkip) return

      const startTime = Date.now()
      const result = await GPUService.getAllGPUs({ limit: 50 })
      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
      expect(result.gpus.length).toBeLessThanOrEqual(50)
    })

    it('should handle search queries efficiently', async () => {
      if (shouldSkip) return

      const startTime = Date.now()
      const results = await GPUService.searchGPUs('RTX')
      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(3000) // Should complete within 3 seconds
      expect(Array.isArray(results)).toBe(true)
    })
  })
})