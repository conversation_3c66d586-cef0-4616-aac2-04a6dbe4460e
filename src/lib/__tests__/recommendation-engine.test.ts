import { RecommendationEngine } from '../recommendation-engine'

describe('RecommendationEngine', () => {
  const mockGPUs = [
    {
      id: 'rtx-4090',
      name: 'GeForce RTX 4090',
      manufacturer: 'NVIDIA',
      architecture: 'Ada Lovelace',
      originalMsrp: 1599,
      specifications: {
        memory: { size: 24, type: 'GDDR6X' },
        cores: { cudaCores: 16384 },
        clocks: { baseClock: 2230, boostClock: 2520 },
        rayTracing: { rtCores: 128 },
        aiAcceleration: { tensorCores: 512 }
      },
      powerRequirements: { tdp: 450 },
      benchmarks: {
        gaming: { fps1080p: 165, fps1440p: 120, fps4k: 85 },
        productivity: { blenderScore: 4500, renderScore: 9200 }
      }
    },
    {
      id: 'rtx-4070',
      name: 'GeForce RTX 4070',
      manufacturer: 'NVIDIA',
      architecture: 'Ada Lovelace',
      originalMsrp: 599,
      specifications: {
        memory: { size: 12, type: 'GDDR6X' },
        cores: { cudaCores: 5888 },
        clocks: { baseClock: 1920, boostClock: 2475 },
        rayTracing: { rtCores: 46 },
        aiAcceleration: { tensorCores: 184 }
      },
      powerRequirements: { tdp: 200 },
      benchmarks: {
        gaming: { fps1080p: 140, fps1440p: 95, fps4k: 55 },
        productivity: { blenderScore: 2800, renderScore: 5400 }
      }
    },
    {
      id: 'rx-7900xtx',
      name: 'Radeon RX 7900 XTX',
      manufacturer: 'AMD',
      architecture: 'RDNA 3',
      originalMsrp: 999,
      specifications: {
        memory: { size: 24, type: 'GDDR6' },
        cores: { streamProcessors: 6144 },
        clocks: { baseClock: 1855, boostClock: 2500 }
      },
      powerRequirements: { tdp: 355 },
      benchmarks: {
        gaming: { fps1080p: 155, fps1440p: 110, fps4k: 75 },
        productivity: { blenderScore: 3800, renderScore: 7200 }
      }
    }
  ]

  const mockPreferences = {
    workload: 'gaming' as const,
    budget: { min: 500, max: 1700 }, // Increased to include RTX 4090
    performance: { resolution: '1440p' as const, targetFps: 60 },
    features: ['rayTracing', 'dlss'],
    priorities: { performance: 8, value: 6, efficiency: 4 }
  }

  describe('getRecommendations', () => {
    it('should return recommendations sorted by score', async () => {
      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, mockPreferences)

      expect(recommendations).toHaveLength(3)
      expect(recommendations[0].score.overall).toBeGreaterThanOrEqual(recommendations[1].score.overall)
      expect(recommendations[1].score.overall).toBeGreaterThanOrEqual(recommendations[2].score.overall)
    })

    it('should filter by budget constraints', async () => {
      const budgetPreferences = {
        ...mockPreferences,
        budget: { min: 500, max: 700 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, budgetPreferences)

      recommendations.forEach(rec => {
        expect(rec.gpu.originalMsrp).toBeLessThanOrEqual(700)
        expect(rec.gpu.originalMsrp).toBeGreaterThanOrEqual(500)
      })
    })

    it('should prioritize gaming performance for gaming workload', async () => {
      const gamingPreferences = {
        ...mockPreferences,
        workload: 'gaming' as const,
        performance: { resolution: '1440p' as const, targetFps: 60 }
      }

      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, gamingPreferences)

      expect(recommendations[0].score.performance).toBeGreaterThan(70)
      expect(recommendations[0].reasoning).toContain('gaming')
    })

    it('should prioritize productivity performance for content creation', async () => {
      const contentPreferences = {
        ...mockPreferences,
        workload: 'content-creation' as const
      }

      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, contentPreferences)

      expect(recommendations[0].score.performance).toBeGreaterThan(70)
      expect(recommendations[0].reasoning).toContain('content creation')
    })

    it('should handle AI/ML workload preferences', async () => {
      const aiPreferences = {
        ...mockPreferences,
        workload: 'ai-ml' as const
      }

      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, aiPreferences)

      // Should prioritize GPUs with tensor cores for AI workloads
      const topRecommendation = recommendations[0]
      expect(topRecommendation.gpu.specifications.aiAcceleration?.tensorCores).toBeGreaterThan(0)
    })

    it('should calculate value scores correctly', async () => {
      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, mockPreferences)

      recommendations.forEach(rec => {
        expect(rec.score.value).toBeGreaterThanOrEqual(0)
        expect(rec.score.value).toBeLessThanOrEqual(100)
      })
    })

    it('should provide detailed reasoning', async () => {
      const recommendations = await RecommendationEngine.getRecommendations(mockGPUs, mockPreferences)

      recommendations.forEach(rec => {
        expect(rec.reasoning).toBeDefined()
        expect(rec.reasoning.length).toBeGreaterThan(0)
        expect(rec.pros).toBeDefined()
        expect(rec.cons).toBeDefined()
        expect(Array.isArray(rec.pros)).toBe(true)
        expect(Array.isArray(rec.cons)).toBe(true)
      })
    })
  })

  describe('calculateGamingScore', () => {
    it('should calculate gaming score based on FPS performance', () => {
      const gpu = mockGPUs[0] // RTX 4090
      const score = RecommendationEngine.calculateGamingScore(gpu, '1440p', 60)

      expect(score).toBeGreaterThan(80) // Should score high for 1440p gaming
    })

    it('should penalize GPUs that cannot meet target FPS', () => {
      const gpu = mockGPUs[1] // RTX 4070
      const score4K = RecommendationEngine.calculateGamingScore(gpu, '4k', 60)
      const score1440p = RecommendationEngine.calculateGamingScore(gpu, '1440p', 60)

      expect(score1440p).toBeGreaterThan(score4K) // Should score better at 1440p than 4K
    })

    it('should handle missing benchmark data gracefully', () => {
      const gpuWithoutBenchmarks = {
        ...mockGPUs[0],
        benchmarks: undefined
      }

      const score = RecommendationEngine.calculateGamingScore(gpuWithoutBenchmarks, '1440p', 60)

      expect(score).toBeGreaterThanOrEqual(0)
      expect(score).toBeLessThanOrEqual(100)
    })
  })

  describe('calculateProductivityScore', () => {
    it('should calculate productivity score based on render performance', () => {
      const gpu = mockGPUs[0] // RTX 4090
      const score = RecommendationEngine.calculateProductivityScore(gpu)

      expect(score).toBeGreaterThan(80) // Should score high for productivity
    })

    it('should consider VRAM size for content creation', () => {
      const highVramGpu = mockGPUs[0] // 24GB VRAM
      const lowVramGpu = mockGPUs[1] // 12GB VRAM

      const highVramScore = RecommendationEngine.calculateProductivityScore(highVramGpu)
      const lowVramScore = RecommendationEngine.calculateProductivityScore(lowVramGpu)

      expect(highVramScore).toBeGreaterThan(lowVramScore)
    })
  })

  describe('calculateValueScore', () => {
    it('should calculate value based on performance per dollar', () => {
      const gpu = mockGPUs[1] // RTX 4070 - better value
      const score = RecommendationEngine.calculateValueScore(gpu, 'gaming', '1440p')

      expect(score).toBeGreaterThan(50)
    })

    it('should penalize overpriced GPUs', () => {
      const expensiveGpu = mockGPUs[0] // RTX 4090 - expensive
      const midRangeGpu = mockGPUs[1] // RTX 4070 - better value

      const expensiveScore = RecommendationEngine.calculateValueScore(expensiveGpu, 'gaming', '1440p')
      const midRangeScore = RecommendationEngine.calculateValueScore(midRangeGpu, 'gaming', '1440p')

      expect(midRangeScore).toBeGreaterThan(expensiveScore)
    })
  })

  describe('generateRecommendationReasoning', () => {
    it('should generate comprehensive reasoning', () => {
      const gpu = mockGPUs[0]
      const scores = { overall: 85, performance: 90, value: 75, efficiency: 80 }
      
      const reasoning = RecommendationEngine.generateRecommendationReasoning(
        gpu, 
        scores, 
        mockPreferences
      )

      expect(reasoning.reasoning).toContain('gaming')
      expect(reasoning.pros).toContain('Excellent gaming performance')
      expect(reasoning.cons.length).toBeGreaterThan(0)
    })

    it('should highlight ray tracing capabilities when requested', () => {
      const preferences = {
        ...mockPreferences,
        features: ['rayTracing']
      }

      const reasoning = RecommendationEngine.generateRecommendationReasoning(
        mockGPUs[0],
        { overall: 85, performance: 90, value: 75, efficiency: 80 },
        preferences
      )

      expect(reasoning.pros.some(pro => pro.toLowerCase().includes('ray tracing'))).toBe(true)
    })

    it('should mention DLSS support for NVIDIA cards', () => {
      const preferences = {
        ...mockPreferences,
        features: ['dlss']
      }

      const reasoning = RecommendationEngine.generateRecommendationReasoning(
        mockGPUs[0], // NVIDIA GPU
        { overall: 85, performance: 90, value: 75, efficiency: 80 },
        preferences
      )

      expect(reasoning.pros.some(pro => pro.toLowerCase().includes('dlss'))).toBe(true)
    })

    it('should identify value concerns for expensive GPUs', () => {
      const reasoning = RecommendationEngine.generateRecommendationReasoning(
        mockGPUs[0], // Expensive RTX 4090
        { overall: 85, performance: 95, value: 60, efficiency: 70 },
        mockPreferences
      )

      expect(reasoning.cons.some(con => con.toLowerCase().includes('price') || con.toLowerCase().includes('expensive'))).toBe(true)
    })
  })

  describe('filterByBudget', () => {
    it('should filter GPUs within budget range', () => {
      const budget = { min: 500, max: 1000 }
      const filtered = RecommendationEngine.filterByBudget(mockGPUs, budget)

      expect(filtered).toHaveLength(2) // RTX 4070 and RX 7900 XTX
      filtered.forEach(gpu => {
        expect(gpu.originalMsrp).toBeGreaterThanOrEqual(500)
        expect(gpu.originalMsrp).toBeLessThanOrEqual(1000)
      })
    })

    it('should return empty array if no GPUs match budget', () => {
      const budget = { min: 2000, max: 3000 }
      const filtered = RecommendationEngine.filterByBudget(mockGPUs, budget)

      expect(filtered).toHaveLength(0)
    })

    it('should handle missing MSRP gracefully', () => {
      const gpusWithMissingMsrp = [
        { ...mockGPUs[0], originalMsrp: undefined },
        mockGPUs[1]
      ]

      const budget = { min: 500, max: 1000 }
      const filtered = RecommendationEngine.filterByBudget(gpusWithMissingMsrp, budget)

      expect(filtered).toHaveLength(2) // Both GPUs included (one with missing MSRP, one within budget)
    })
  })

  describe('getWorkloadMultipliers', () => {
    it('should return appropriate multipliers for gaming workload', () => {
      const multipliers = RecommendationEngine.getWorkloadMultipliers('gaming')

      expect(multipliers.gaming).toBeGreaterThan(multipliers.productivity)
      expect(multipliers.gaming).toBeGreaterThan(multipliers.aiMl)
    })

    it('should return appropriate multipliers for content creation', () => {
      const multipliers = RecommendationEngine.getWorkloadMultipliers('content-creation')

      expect(multipliers.productivity).toBeGreaterThan(multipliers.gaming)
      expect(multipliers.vramWeight).toBeGreaterThan(0.3) // Higher VRAM weight
    })

    it('should return appropriate multipliers for AI/ML workload', () => {
      const multipliers = RecommendationEngine.getWorkloadMultipliers('ai-ml')

      expect(multipliers.aiMl).toBeGreaterThan(multipliers.gaming)
      expect(multipliers.tensorWeight).toBeGreaterThan(0.4) // Higher tensor core weight
    })

    it('should return appropriate multipliers for mining workload', () => {
      const multipliers = RecommendationEngine.getWorkloadMultipliers('mining')

      expect(multipliers.efficiency).toBeGreaterThan(0.4) // Higher efficiency weight
      expect(multipliers.hashRate).toBeGreaterThan(0.3) // Hash rate consideration
    })
  })
})