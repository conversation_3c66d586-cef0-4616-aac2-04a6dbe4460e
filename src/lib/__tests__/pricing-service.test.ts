import { PricingService } from '../pricing-service'
import { prisma } from '../prisma'

// Mock Prisma
jest.mock('../prisma', () => ({
  prisma: {
    gPUPricing: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      deleteMany: jest.fn(),
    },
    priceAlert: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    gPU: {
      findUnique: jest.fn(),
    },
  },
}), { virtual: true })

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('PricingService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const mockPricingData = [
    {
      id: '1',
      gpuId: 'rtx-4090',
      retailer: 'Newegg',
      price: 1599,
      availability: 'in-stock' as const,
      url: 'https://newegg.com/rtx-4090',
      scrapedAt: new Date('2024-01-15T10:00:00Z')
    },
    {
      id: '2',
      gpuId: 'rtx-4090',
      retailer: 'Amazon',
      price: 1649,
      availability: 'limited' as const,
      url: 'https://amazon.com/rtx-4090',
      scrapedAt: new Date('2024-01-15T10:00:00Z')
    },
    {
      id: '3',
      gpuId: 'rtx-4090',
      retailer: 'Best Buy',
      price: 1699,
      availability: 'out-of-stock' as const,
      url: 'https://bestbuy.com/rtx-4090',
      scrapedAt: new Date('2024-01-15T10:00:00Z')
    }
  ]

  const mockPriceAlert = {
    id: 'alert-1',
    userId: 'user-1',
    gpuId: 'rtx-4090',
    targetPrice: 1500,
    isActive: true,
    triggeredAt: null,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  describe('getCurrentPricing', () => {
    it('should return current pricing for a GPU', async () => {
      mockPrisma.gPUPricing.findMany.mockResolvedValue(mockPricingData)

      const result = await PricingService.getCurrentPricing('rtx-4090')

      expect(result).toEqual(mockPricingData)
      expect(mockPrisma.gPUPricing.findMany).toHaveBeenCalledWith({
        where: { gpuId: 'rtx-4090' },
        orderBy: { scrapedAt: 'desc' },
        take: 10
      })
    })

    it('should return empty array for GPU with no pricing data', async () => {
      mockPrisma.gPUPricing.findMany.mockResolvedValue([])

      const result = await PricingService.getCurrentPricing('unknown-gpu')

      expect(result).toEqual([])
    })

    it('should handle database errors', async () => {
      mockPrisma.gPUPricing.findMany.mockRejectedValue(new Error('Database error'))

      await expect(PricingService.getCurrentPricing('rtx-4090')).rejects.toThrow('Database error')
    })
  })

  describe('getBestPrice', () => {
    it('should return the best available price', async () => {
      const availablePricing = mockPricingData.filter(p => p.availability !== 'out-of-stock')
      mockPrisma.gPUPricing.findFirst.mockResolvedValue(availablePricing[0])

      const result = await PricingService.getBestPrice('rtx-4090')

      expect(result).toEqual(availablePricing[0])
      expect(mockPrisma.gPUPricing.findFirst).toHaveBeenCalledWith({
        where: {
          gpuId: 'rtx-4090',
          availability: { in: ['in-stock', 'limited'] }
        },
        orderBy: { price: 'asc' }
      })
    })

    it('should return null if no available pricing', async () => {
      mockPrisma.gPUPricing.findFirst.mockResolvedValue(null)

      const result = await PricingService.getBestPrice('rtx-4090')

      expect(result).toBeNull()
    })
  })

  describe('getPriceHistory', () => {
    it('should return price history for a GPU', async () => {
      const historicalData = [
        { ...mockPricingData[0], scrapedAt: new Date('2024-01-10T10:00:00Z') },
        { ...mockPricingData[0], scrapedAt: new Date('2024-01-11T10:00:00Z') },
        { ...mockPricingData[0], scrapedAt: new Date('2024-01-12T10:00:00Z') }
      ]
      mockPrisma.gPUPricing.findMany.mockResolvedValue(historicalData)

      const result = await PricingService.getPriceHistory('rtx-4090', 7)

      expect(result).toEqual(historicalData)
      expect(mockPrisma.gPUPricing.findMany).toHaveBeenCalledWith({
        where: {
          gpuId: 'rtx-4090',
          scrapedAt: {
            gte: expect.any(Date)
          }
        },
        orderBy: { scrapedAt: 'asc' }
      })
    })

    it('should use default 30-day period', async () => {
      mockPrisma.gPUPricing.findMany.mockResolvedValue([])

      await PricingService.getPriceHistory('rtx-4090')

      const call = mockPrisma.gPUPricing.findMany.mock.calls[0][0]
      const daysAgo = Math.floor((Date.now() - call.where.scrapedAt.gte.getTime()) / (1000 * 60 * 60 * 24))
      expect(daysAgo).toBeCloseTo(30, 1)
    })
  })

  describe('addPricing', () => {
    it('should add new pricing data', async () => {
      const newPricing = {
        gpuId: 'rtx-4090',
        retailer: 'Micro Center',
        price: 1549,
        availability: 'in-stock' as const,
        url: 'https://microcenter.com/rtx-4090'
      }

      const createdPricing = { id: '4', ...newPricing, scrapedAt: new Date() }
      mockPrisma.gPUPricing.create.mockResolvedValue(createdPricing)

      const result = await PricingService.addPricing(newPricing)

      expect(result).toEqual(createdPricing)
      expect(mockPrisma.gPUPricing.create).toHaveBeenCalledWith({
        data: {
          ...newPricing,
          scrapedAt: expect.any(Date)
        }
      })
    })

    it('should handle creation errors', async () => {
      mockPrisma.gPUPricing.create.mockRejectedValue(new Error('Creation failed'))

      await expect(PricingService.addPricing({
        gpuId: 'rtx-4090',
        retailer: 'Test',
        price: 1000,
        availability: 'in-stock',
        url: 'https://test.com'
      })).rejects.toThrow('Creation failed')
    })
  })

  describe('cleanOldPricing', () => {
    it('should remove pricing data older than specified days', async () => {
      mockPrisma.gPUPricing.deleteMany.mockResolvedValue({ count: 50 })

      const result = await PricingService.cleanOldPricing(90)

      expect(result).toBe(50)
      expect(mockPrisma.gPUPricing.deleteMany).toHaveBeenCalledWith({
        where: {
          scrapedAt: {
            lt: expect.any(Date)
          }
        }
      })
    })

    it('should use default 180-day cleanup period', async () => {
      mockPrisma.gPUPricing.deleteMany.mockResolvedValue({ count: 25 })

      await PricingService.cleanOldPricing()

      const call = mockPrisma.gPUPricing.deleteMany.mock.calls[0][0]
      const daysAgo = Math.floor((Date.now() - call.where.scrapedAt.lt.getTime()) / (1000 * 60 * 60 * 24))
      expect(daysAgo).toBeCloseTo(180, 1)
    })
  })

  describe('createPriceAlert', () => {
    it('should create a new price alert', async () => {
      const alertData = {
        userId: 'user-1',
        gpuId: 'rtx-4090',
        targetPrice: 1400
      }

      mockPrisma.priceAlert.create.mockResolvedValue(mockPriceAlert)

      const result = await PricingService.createPriceAlert(alertData)

      expect(result).toEqual(mockPriceAlert)
      expect(mockPrisma.priceAlert.create).toHaveBeenCalledWith({
        data: {
          ...alertData,
          isActive: true
        }
      })
    })

    it('should handle creation errors', async () => {
      mockPrisma.priceAlert.create.mockRejectedValue(new Error('Alert creation failed'))

      await expect(PricingService.createPriceAlert({
        userId: 'user-1',
        gpuId: 'rtx-4090',
        targetPrice: 1400
      })).rejects.toThrow('Alert creation failed')
    })
  })

  describe('getUserPriceAlerts', () => {
    it('should return user price alerts', async () => {
      const userAlerts = [mockPriceAlert]
      const mockGPU = { id: 'rtx-4090', name: 'RTX 4090', manufacturer: 'NVIDIA' }

      mockPrisma.priceAlert.findMany.mockResolvedValue(userAlerts)
      mockPrisma.gPU.findUnique.mockResolvedValue(mockGPU)

      const result = await PricingService.getUserPriceAlerts('user-1')

      expect(result).toEqual([{ ...mockPriceAlert, gpu: mockGPU }])
      expect(mockPrisma.priceAlert.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        orderBy: { createdAt: 'desc' }
      })
      expect(mockPrisma.gPU.findUnique).toHaveBeenCalledWith({
        where: { id: mockPriceAlert.gpuId },
        select: { id: true, name: true, manufacturer: true }
      })
    })

    it('should return empty array for user with no alerts', async () => {
      mockPrisma.priceAlert.findMany.mockResolvedValue([])

      const result = await PricingService.getUserPriceAlerts('user-2')

      expect(result).toEqual([])
    })
  })

  describe('updatePriceAlert', () => {
    it('should update a price alert', async () => {
      const updatedAlert = { ...mockPriceAlert, targetPrice: 1300 }
      mockPrisma.priceAlert.update.mockResolvedValue(updatedAlert)

      const result = await PricingService.updatePriceAlert('alert-1', { targetPrice: 1300 })

      expect(result).toEqual(updatedAlert)
      expect(mockPrisma.priceAlert.update).toHaveBeenCalledWith({
        where: { id: 'alert-1' },
        data: { targetPrice: 1300 }
      })
    })

    it('should handle update errors', async () => {
      mockPrisma.priceAlert.update.mockRejectedValue(new Error('Update failed'))

      await expect(PricingService.updatePriceAlert('alert-1', { targetPrice: 1300 }))
        .rejects.toThrow('Update failed')
    })
  })

  describe('deletePriceAlert', () => {
    it('should delete a price alert', async () => {
      mockPrisma.priceAlert.delete.mockResolvedValue(mockPriceAlert)

      const result = await PricingService.deletePriceAlert('alert-1')

      expect(result).toEqual(mockPriceAlert)
      expect(mockPrisma.priceAlert.delete).toHaveBeenCalledWith({
        where: { id: 'alert-1' }
      })
    })

    it('should handle deletion errors', async () => {
      mockPrisma.priceAlert.delete.mockRejectedValue(new Error('Deletion failed'))

      await expect(PricingService.deletePriceAlert('alert-1')).rejects.toThrow('Deletion failed')
    })
  })

  describe('checkPriceAlerts', () => {
    it('should identify triggered price alerts', async () => {
      const activeAlerts = [mockPriceAlert]
      const currentPricing = mockPricingData[0] // $1599, alert target is $1500

      mockPrisma.priceAlert.findMany.mockResolvedValue(activeAlerts)
      mockPrisma.gPUPricing.findFirst.mockResolvedValue(currentPricing)

      const result = await PricingService.checkPriceAlerts()

      expect(result.checked).toBe(1)
      expect(result.triggered).toBe(0) // Price is above target
      expect(mockPrisma.priceAlert.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          triggeredAt: null
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true
            }
          }
        }
      })
    })

    it('should trigger alerts when price drops below target', async () => {
      const lowPriceAlert = { ...mockPriceAlert, targetPrice: 1700 } // Higher than current price
      const currentPricing = mockPricingData[0] // $1599

      mockPrisma.priceAlert.findMany.mockResolvedValue([lowPriceAlert])
      mockPrisma.gPUPricing.findFirst.mockResolvedValue(currentPricing)
      mockPrisma.priceAlert.update.mockResolvedValue({ ...lowPriceAlert, triggeredAt: new Date() })

      const result = await PricingService.checkPriceAlerts()

      expect(result.checked).toBe(1)
      expect(result.triggered).toBe(1)
      expect(mockPrisma.priceAlert.update).toHaveBeenCalledWith({
        where: { id: 'alert-1' },
        data: { triggeredAt: expect.any(Date) }
      })
    })

    it('should handle alerts with no current pricing', async () => {
      mockPrisma.priceAlert.findMany.mockResolvedValue([mockPriceAlert])
      mockPrisma.gPUPricing.findFirst.mockResolvedValue(null)

      const result = await PricingService.checkPriceAlerts()

      expect(result.checked).toBe(1)
      expect(result.triggered).toBe(0)
      expect(result.errors).toContain('No current pricing found for GPU rtx-4090')
    })
  })

  describe('getPricingStatistics', () => {
    it('should return pricing statistics', async () => {
      const mockStats = [
        { retailer: 'Newegg', _count: { id: 100 } },
        { retailer: 'Amazon', _count: { id: 85 } },
        { retailer: 'Best Buy', _count: { id: 75 } }
      ]

      mockPrisma.gPUPricing.findMany.mockResolvedValue(mockStats as any)

      const result = await PricingService.getPricingStatistics()

      expect(result.totalPricePoints).toBe(260)
      expect(result.retailerCounts).toEqual({
        'Newegg': 100,
        'Amazon': 85,
        'Best Buy': 75
      })
    })

    it('should handle empty statistics', async () => {
      mockPrisma.gPUPricing.findMany.mockResolvedValue([])

      const result = await PricingService.getPricingStatistics()

      expect(result.totalPricePoints).toBe(0)
      expect(result.retailerCounts).toEqual({})
    })
  })

  describe('getAvailabilityStatus', () => {
    it('should return availability status for a GPU', async () => {
      mockPrisma.gPUPricing.findMany.mockResolvedValue(mockPricingData)

      const result = await PricingService.getAvailabilityStatus('rtx-4090')

      expect(result.inStock).toBe(1)
      expect(result.limited).toBe(1)
      expect(result.outOfStock).toBe(1)
      expect(result.totalRetailers).toBe(3)
      expect(result.availabilityPercentage).toBeCloseTo(66.67, 2)
    })

    it('should handle GPU with no pricing data', async () => {
      mockPrisma.gPUPricing.findMany.mockResolvedValue([])

      const result = await PricingService.getAvailabilityStatus('unknown-gpu')

      expect(result.inStock).toBe(0)
      expect(result.limited).toBe(0)
      expect(result.outOfStock).toBe(0)
      expect(result.totalRetailers).toBe(0)
      expect(result.availabilityPercentage).toBe(0)
    })
  })
})