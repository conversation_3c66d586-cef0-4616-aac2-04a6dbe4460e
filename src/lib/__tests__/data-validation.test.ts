// Mock Prisma before any imports
const mockPrisma = {
  gPU: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
}

jest.mock('../prisma', () => ({
  prisma: mockPrisma, // This fails because mockPrisma isn't hoisted
}))

import { DataValidationService } from '../data-validation'
import { prisma } from '../prisma'

// Cast the mocked prisma for type safety
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('DataValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const validGPU = {
    id: 'valid-gpu-1',
    name: 'GeForce RTX 4090',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: new Date('2022-10-12'),
    originalMsrp: 1599.00,
    specifications: {
      memory: {
        size: 24,
        type: 'GDDR6X',
        bandwidth: 1008,
        busWidth: 384
      },
      cores: {
        cudaCores: 16384
      },
      clocks: {
        baseClock: 2230,
        boostClock: 2520,
        memoryClock: 21000
      }
    },
    physicalSpecs: {
      dimensions: {
        length: 304,
        width: 137,
        height: 61
      }
    },
    powerRequirements: {
      tdp: 450,
      recommendedPSU: 850,
      connectors: ['12VHPWR']
    }
  }

  const invalidGPU = {
    id: 'invalid-gpu-1',
    name: '', // Invalid: empty name
    manufacturer: 'InvalidManufacturer', // Invalid: not NVIDIA/AMD/Intel
    specifications: null, // Invalid: missing specifications
    physicalSpecs: null,
    powerRequirements: null
  }

  describe('validateGPU', () => {
    it('should validate a complete, valid GPU', () => {
      const result = DataValidationService.validateGPU(validGPU)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.missingSpecs).toBe(false)
    })

    it('should detect missing required fields', () => {
      const result = DataValidationService.validateGPU(invalidGPU)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Name is required')
      expect(result.errors).toContain('Valid manufacturer is required (NVIDIA, AMD, or Intel)')
      expect(result.errors).toContain('Specifications are required')
      expect(result.missingSpecs).toBe(true)
    })

    it('should detect invalid memory specifications', () => {
      const gpuWithInvalidMemory = {
        ...validGPU,
        specifications: {
          ...validGPU.specifications,
          memory: {
            size: 0, // Invalid: zero size
            type: '', // Missing type
            bandwidth: -100 // Invalid: negative bandwidth
          }
        }
      }

      const result = DataValidationService.validateGPU(gpuWithInvalidMemory)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Valid memory size is required')
      expect(result.warnings).toContain('Memory type is missing')
      expect(result.warnings).toContain('Memory bandwidth is missing or invalid')
    })

    it('should detect missing core specifications', () => {
      const gpuWithoutCores = {
        ...validGPU,
        specifications: {
          ...validGPU.specifications,
          cores: {} // No valid core counts
        }
      }

      const result = DataValidationService.validateGPU(gpuWithoutCores)

      expect(result.warnings).toContain('No valid core count found (CUDA cores, Stream Processors, or Execution Units)')
    })

    it('should detect invalid clock speeds', () => {
      const gpuWithInvalidClocks = {
        ...validGPU,
        specifications: {
          ...validGPU.specifications,
          clocks: {
            baseClock: 0, // Invalid
            boostClock: 0, // Invalid
            memoryClock: 21000
          }
        }
      }

      const result = DataValidationService.validateGPU(gpuWithInvalidClocks)

      expect(result.warnings).toContain('Base clock is missing or invalid')
      expect(result.warnings).toContain('Boost clock is missing or invalid')
    })

    it('should detect illogical clock relationships', () => {
      const gpuWithIllogicalClocks = {
        ...validGPU,
        specifications: {
          ...validGPU.specifications,
          clocks: {
            baseClock: 3000,
            boostClock: 2500, // Lower than base clock
            memoryClock: 21000
          }
        }
      }

      const result = DataValidationService.validateGPU(gpuWithIllogicalClocks)

      expect(result.warnings).toContain('Base clock should be lower than boost clock')
    })

    it('should detect invalid physical specifications', () => {
      const gpuWithInvalidPhysical = {
        ...validGPU,
        physicalSpecs: {
          dimensions: {
            length: 0, // Invalid
            width: -10, // Invalid
            height: 0 // Invalid
          }
        }
      }

      const result = DataValidationService.validateGPU(gpuWithInvalidPhysical)

      expect(result.warnings).toContain('GPU length is missing or invalid')
      expect(result.warnings).toContain('GPU width is missing or invalid')
      expect(result.warnings).toContain('GPU height is missing or invalid')
    })

    it('should detect invalid power requirements', () => {
      const gpuWithInvalidPower = {
        ...validGPU,
        powerRequirements: {
          tdp: 0, // Invalid
          recommendedPSU: 0, // Invalid
          connectors: []
        }
      }

      const result = DataValidationService.validateGPU(gpuWithInvalidPower)

      expect(result.warnings).toContain('TDP is missing or invalid')
      expect(result.warnings).toContain('Recommended PSU wattage is missing or invalid')
    })

    it('should detect illogical power relationships', () => {
      const gpuWithIllogicalPower = {
        ...validGPU,
        powerRequirements: {
          tdp: 500,
          recommendedPSU: 400, // Lower than TDP
          connectors: ['12VHPWR']
        }
      }

      const result = DataValidationService.validateGPU(gpuWithIllogicalPower)

      expect(result.warnings).toContain('Recommended PSU should be higher than TDP')
    })

    it('should detect unrealistic launch dates', () => {
      const gpuWithFutureLaunch = {
        ...validGPU,
        launchDate: new Date('2030-01-01') // Future date
      }

      const result = DataValidationService.validateGPU(gpuWithFutureLaunch)

      expect(result.warnings).toContain('Launch date is in the future')
    })

    it('should detect unrealistic MSRP', () => {
      const gpuWithUnrealisticMSRP = {
        ...validGPU,
        originalMsrp: 50000 // Unrealistically high
      }

      const result = DataValidationService.validateGPU(gpuWithUnrealisticMSRP)

      expect(result.warnings).toContain('Original MSRP seems unrealistic')
    })
  })

  describe('validateAllGPUs', () => {
    it('should validate all GPUs in database', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([validGPU, invalidGPU])

      const result = await DataValidationService.validateAllGPUs()

      expect(result.stats.totalGPUs).toBe(2)
      expect(result.stats.validGPUs).toBe(1)
      expect(result.stats.invalidGPUs).toBe(1)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should handle empty database', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([])

      const result = await DataValidationService.validateAllGPUs()

      expect(result.stats.totalGPUs).toBe(0)
      expect(result.stats.validGPUs).toBe(0)
      expect(result.stats.invalidGPUs).toBe(0)
      expect(result.isValid).toBe(true)
    })

    it('should handle database errors gracefully', async () => {
      mockPrisma.gPU.findMany.mockRejectedValue(new Error('Database connection failed'))

      const result = await DataValidationService.validateAllGPUs()

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Validation failed: Database connection failed')
    })
  })

  describe('findDuplicateGPUs', () => {
    it('should find duplicate GPUs by name', async () => {
      const duplicateGPUs = [
        { id: '1', name: 'GeForce RTX 4090', manufacturer: 'NVIDIA' },
        { id: '2', name: 'GeForce RTX 4090 ', manufacturer: 'NVIDIA' }, // Extra space
        { id: '3', name: 'Radeon RX 7900 XTX', manufacturer: 'AMD' }
      ]

      mockPrisma.gPU.findMany.mockResolvedValue(duplicateGPUs)

      const result = await DataValidationService.findDuplicateGPUs()

      expect(result).toHaveLength(1)
      expect(result[0].count).toBe(2)
      expect(result[0].ids).toContain('1')
      expect(result[0].ids).toContain('2')
    })

    it('should return empty array when no duplicates exist', async () => {
      const uniqueGPUs = [
        { id: '1', name: 'GeForce RTX 4090', manufacturer: 'NVIDIA' },
        { id: '2', name: 'Radeon RX 7900 XTX', manufacturer: 'AMD' }
      ]

      mockPrisma.gPU.findMany.mockResolvedValue(uniqueGPUs)

      const result = await DataValidationService.findDuplicateGPUs()

      expect(result).toHaveLength(0)
    })
  })

  describe('autoFixDataIssues', () => {
    it('should fix missing recommended PSU values', async () => {
      const gpusWithMissingPSU = [
        {
          id: '1',
          powerRequirements: { tdp: 300, recommendedPSU: null }
        }
      ]

      mockPrisma.gPU.findMany.mockResolvedValue(gpusWithMissingPSU)
      mockPrisma.gPU.update.mockResolvedValue({} as any)

      const result = await DataValidationService.autoFixDataIssues()

      expect(result.fixed).toBe(1)
      expect(mockPrisma.gPU.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: {
          powerRequirements: {
            tdp: 300,
            recommendedPSU: 600 // TDP * 2
          }
        }
      })
    })

    it('should fix missing slot counts', async () => {
      const gpusWithMissingSlots = [
        {
          id: '1',
          physicalSpecs: {
            dimensions: { height: 45 },
            slots: null
          }
        }
      ]

      mockPrisma.gPU.findMany
        .mockResolvedValueOnce([]) // First call for missing PSU
        .mockResolvedValueOnce(gpusWithMissingSlots) // Second call for missing slots

      mockPrisma.gPU.update.mockResolvedValue({} as any)

      const result = await DataValidationService.autoFixDataIssues()

      expect(result.fixed).toBe(1)
      expect(mockPrisma.gPU.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: {
          physicalSpecs: {
            dimensions: { height: 45 },
            slots: 3 // Math.ceil(45/20)
          }
        }
      })
    })

    it('should handle auto-fix errors gracefully', async () => {
      mockPrisma.gPU.findMany.mockRejectedValue(new Error('Database error'))

      const result = await DataValidationService.autoFixDataIssues()

      expect(result.fixed).toBe(0)
      expect(result.errors).toContain('Auto-fix failed: Database error')
    })
  })

  describe('generateDataQualityReport', () => {
    it('should generate comprehensive quality report', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([validGPU])

      const report = await DataValidationService.generateDataQualityReport()

      expect(report.summary.totalGPUs).toBe(1)
      expect(report.summary.dataCompleteness).toBeGreaterThan(0)
      expect(report.summary.validationScore).toBeGreaterThan(0)
      expect(report.details).toBeDefined()
      expect(Array.isArray(report.recommendations)).toBe(true)
    })

    it('should provide recommendations based on data quality', async () => {
      const problematicGPUs = [validGPU, invalidGPU]
      mockPrisma.gPU.findMany.mockResolvedValue(problematicGPUs)

      const report = await DataValidationService.generateDataQualityReport()

      expect(report.recommendations.length).toBeGreaterThan(0)
      expect(report.recommendations.some(rec => rec.includes('critical data errors'))).toBe(true)
    })
  })
})
