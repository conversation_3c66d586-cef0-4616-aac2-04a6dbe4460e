import { GPUService } from '../gpu-service'
import { prisma } from '../prisma'

// Mock Prisma
jest.mock('../prisma', () => ({
  prisma: {
    gPU: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  },
}), { virtual: true })

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('GPUService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const mockGPU = {
    id: 'rtx-4090',
    name: 'GeForce RTX 4090',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: new Date('2022-10-12'),
    originalMsrp: 1599,
    specifications: {
      memory: { size: 24, type: 'GDDR6X' },
      cores: { cudaCores: 16384 },
      clocks: { baseClock: 2230, boostClock: 2520 }
    },
    powerRequirements: { tdp: 450 },
    createdAt: new Date(),
    updatedAt: new Date()
  }

  const mockGPUs = [mockGPU, {
    id: 'rtx-4070',
    name: 'GeForce RTX 4070',
    manufacturer: 'NVIDIA',
    architecture: 'Ada Lovelace',
    launchDate: new Date('2023-04-13'),
    originalMsrp: 599,
    specifications: {
      memory: { size: 12, type: 'GDDR6X' },
      cores: { cudaCores: 5888 },
      clocks: { baseClock: 1920, boostClock: 2475 }
    },
    powerRequirements: { tdp: 200 },
    createdAt: new Date(),
    updatedAt: new Date()
  }]

  describe('getAllGPUs', () => {
    it('should return all GPUs with default pagination', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue(mockGPUs)
      mockPrisma.gPU.count.mockResolvedValue(2)

      const result = await GPUService.getAllGPUs()

      expect(result.gpus).toEqual(mockGPUs)
      expect(result.total).toBe(2)
      expect(result.page).toBe(1)
      expect(result.limit).toBe(20)
      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 20,
        where: {},
        orderBy: { name: 'asc' }
      })
    })

    it('should handle pagination correctly', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPU])
      mockPrisma.gPU.count.mockResolvedValue(25)

      const result = await GPUService.getAllGPUs({ page: 2, limit: 10 })

      expect(result.page).toBe(2)
      expect(result.limit).toBe(10)
      expect(result.total).toBe(25)
      expect(result.totalPages).toBe(3)
      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 10,
        take: 10,
        where: {},
        orderBy: { name: 'asc' }
      })
    })

    it('should apply manufacturer filter', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPU])
      mockPrisma.gPU.count.mockResolvedValue(1)

      await GPUService.getAllGPUs({ manufacturer: 'NVIDIA' })

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 20,
        where: { manufacturer: 'NVIDIA' },
        orderBy: { name: 'asc' }
      })
    })

    it('should apply architecture filter', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPU])
      mockPrisma.gPU.count.mockResolvedValue(1)

      await GPUService.getAllGPUs({ architecture: 'Ada Lovelace' })

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 20,
        where: { architecture: 'Ada Lovelace' },
        orderBy: { name: 'asc' }
      })
    })

    it('should apply price range filter', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPU])
      mockPrisma.gPU.count.mockResolvedValue(1)

      await GPUService.getAllGPUs({ minPrice: 500, maxPrice: 1000 })

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 20,
        where: {
          originalMsrp: {
            gte: 500,
            lte: 1000
          }
        },
        orderBy: { name: 'asc' }
      })
    })

    it('should apply memory size filter', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPU])
      mockPrisma.gPU.count.mockResolvedValue(1)

      await GPUService.getAllGPUs({ minMemory: 16 })

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 20,
        where: {
          specifications: {
            path: ['memory', 'size'],
            gte: 16
          }
        },
        orderBy: { name: 'asc' }
      })
    })

    it('should handle sorting options', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue(mockGPUs)
      mockPrisma.gPU.count.mockResolvedValue(2)

      await GPUService.getAllGPUs({ sortBy: 'price', sortOrder: 'desc' })

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 20,
        where: {},
        orderBy: { originalMsrp: 'desc' }
      })
    })

    it('should handle database errors gracefully', async () => {
      mockPrisma.gPU.findMany.mockRejectedValue(new Error('Database error'))

      await expect(GPUService.getAllGPUs()).rejects.toThrow('Database error')
    })
  })

  describe('getGPUById', () => {
    it('should return GPU by ID', async () => {
      mockPrisma.gPU.findUnique.mockResolvedValue(mockGPU)

      const result = await GPUService.getGPUById('rtx-4090')

      expect(result).toEqual(mockGPU)
      expect(mockPrisma.gPU.findUnique).toHaveBeenCalledWith({
        where: { id: 'rtx-4090' }
      })
    })

    it('should return null for non-existent GPU', async () => {
      mockPrisma.gPU.findUnique.mockResolvedValue(null)

      const result = await GPUService.getGPUById('non-existent')

      expect(result).toBeNull()
    })

    it('should handle database errors', async () => {
      mockPrisma.gPU.findUnique.mockRejectedValue(new Error('Database error'))

      await expect(GPUService.getGPUById('rtx-4090')).rejects.toThrow('Database error')
    })
  })

  describe('searchGPUs', () => {
    it('should search GPUs by name', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPU])

      const result = await GPUService.searchGPUs('RTX 4090')

      expect(result).toEqual([mockGPU])
      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'RTX 4090', mode: 'insensitive' } },
            { manufacturer: { contains: 'RTX 4090', mode: 'insensitive' } },
            { architecture: { contains: 'RTX 4090', mode: 'insensitive' } }
          ]
        },
        take: 10,
        orderBy: { name: 'asc' }
      })
    })

    it('should limit search results', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue(mockGPUs)

      await GPUService.searchGPUs('NVIDIA', 5)

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'NVIDIA', mode: 'insensitive' } },
            { manufacturer: { contains: 'NVIDIA', mode: 'insensitive' } },
            { architecture: { contains: 'NVIDIA', mode: 'insensitive' } }
          ]
        },
        take: 5,
        orderBy: { name: 'asc' }
      })
    })

    it('should return empty array for no matches', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([])

      const result = await GPUService.searchGPUs('nonexistent')

      expect(result).toEqual([])
    })
  })

  describe('createGPU', () => {
    it('should create a new GPU', async () => {
      const newGPUData = {
        name: 'GeForce RTX 4080',
        manufacturer: 'NVIDIA',
        architecture: 'Ada Lovelace',
        specifications: {
          memory: { size: 16, type: 'GDDR6X' },
          cores: { cudaCores: 9728 }
        }
      }

      const createdGPU = { id: 'rtx-4080', ...newGPUData, createdAt: new Date(), updatedAt: new Date() }
      mockPrisma.gPU.create.mockResolvedValue(createdGPU)

      const result = await GPUService.createGPU(newGPUData)

      expect(result).toEqual(createdGPU)
      expect(mockPrisma.gPU.create).toHaveBeenCalledWith({
        data: newGPUData
      })
    })

    it('should handle creation errors', async () => {
      mockPrisma.gPU.create.mockRejectedValue(new Error('Creation failed'))

      await expect(GPUService.createGPU({
        name: 'Test GPU',
        manufacturer: 'NVIDIA'
      })).rejects.toThrow('Creation failed')
    })
  })

  describe('updateGPU', () => {
    it('should update an existing GPU', async () => {
      const updateData = { originalMsrp: 1399 }
      const updatedGPU = { ...mockGPU, ...updateData }
      
      mockPrisma.gPU.update.mockResolvedValue(updatedGPU)

      const result = await GPUService.updateGPU('rtx-4090', updateData)

      expect(result).toEqual(updatedGPU)
      expect(mockPrisma.gPU.update).toHaveBeenCalledWith({
        where: { id: 'rtx-4090' },
        data: updateData
      })
    })

    it('should handle update errors', async () => {
      mockPrisma.gPU.update.mockRejectedValue(new Error('Update failed'))

      await expect(GPUService.updateGPU('rtx-4090', { originalMsrp: 1399 }))
        .rejects.toThrow('Update failed')
    })
  })

  describe('deleteGPU', () => {
    it('should delete a GPU', async () => {
      mockPrisma.gPU.delete.mockResolvedValue(mockGPU)

      const result = await GPUService.deleteGPU('rtx-4090')

      expect(result).toEqual(mockGPU)
      expect(mockPrisma.gPU.delete).toHaveBeenCalledWith({
        where: { id: 'rtx-4090' }
      })
    })

    it('should handle deletion errors', async () => {
      mockPrisma.gPU.delete.mockRejectedValue(new Error('Deletion failed'))

      await expect(GPUService.deleteGPU('rtx-4090')).rejects.toThrow('Deletion failed')
    })
  })

  describe('getGPUsByManufacturer', () => {
    it('should return GPUs by manufacturer', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue(mockGPUs)

      const result = await GPUService.getGPUsByManufacturer('NVIDIA')

      expect(result).toEqual(mockGPUs)
      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        where: { manufacturer: 'NVIDIA' },
        orderBy: { name: 'asc' }
      })
    })

    it('should return empty array for unknown manufacturer', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([])

      const result = await GPUService.getGPUsByManufacturer('Unknown')

      expect(result).toEqual([])
    })
  })

  describe('getGPUsByPriceRange', () => {
    it('should return GPUs within price range', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue([mockGPUs[1]]) // RTX 4070

      const result = await GPUService.getGPUsByPriceRange(500, 800)

      expect(result).toEqual([mockGPUs[1]])
      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        where: {
          originalMsrp: {
            gte: 500,
            lte: 800
          }
        },
        orderBy: { originalMsrp: 'asc' }
      })
    })

    it('should handle null price values', async () => {
      mockPrisma.gPU.findMany.mockResolvedValue(mockGPUs)

      await GPUService.getGPUsByPriceRange(null, 1000)

      expect(mockPrisma.gPU.findMany).toHaveBeenCalledWith({
        where: {
          originalMsrp: {
            lte: 1000
          }
        },
        orderBy: { originalMsrp: 'asc' }
      })
    })
  })

  describe('getGPUStatistics', () => {
    it('should return GPU statistics', async () => {
      mockPrisma.gPU.count
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(60)  // NVIDIA
        .mockResolvedValueOnce(35)  // AMD
        .mockResolvedValueOnce(5)   // Intel

      const result = await GPUService.getGPUStatistics()

      expect(result).toEqual({
        total: 100,
        byManufacturer: {
          NVIDIA: 60,
          AMD: 35,
          Intel: 5
        }
      })
    })

    it('should handle database errors in statistics', async () => {
      mockPrisma.gPU.count.mockRejectedValue(new Error('Stats error'))

      await expect(GPUService.getGPUStatistics()).rejects.toThrow('Stats error')
    })
  })
})