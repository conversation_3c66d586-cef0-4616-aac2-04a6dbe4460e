import { prisma } from './prisma'
import { GPUFilters } from '@/types/gpu'

export class GPUService {
  /**
   * Seed GPU data from external API (Google Apps Script)
   */
  static async seedGPUData() {
    const apiUrl = process.env.GPU_DATA_API_URL
    if (!apiUrl) {
      throw new Error('GPU_DATA_API_URL not configured')
    }

    try {
      // Fetch data for all brands
      const brands = ['nvidia', 'amd', 'intel']
      const allGPUs = []

      for (const brand of brands) {
        console.log(`Fetching ${brand} GPU data...`)
        const response = await fetch(`${apiUrl}?brand=${brand}`)
        
        if (!response.ok) {
          console.error(`Failed to fetch ${brand} data:`, response.statusText)
          continue
        }

        const response_data = await response.json()
        if (response_data.success && Array.isArray(response_data.data)) {
          allGPUs.push(...response_data.data.map(gpu => ({ ...gpu, manufacturer: brand.toUpperCase() })))
        }
      }

      console.log(`Processing ${allGPUs.length} GPUs...`)

      // Process and insert GPUs
      let inserted = 0
      let updated = 0

      for (const gpuData of allGPUs) {
        try {
          // Transform the external data to our schema format
          const transformedGPU = this.transformExternalGPUData(gpuData)
          
          // Skip if transformation returned null (invalid data)
          if (!transformedGPU) {
            continue
          }
          
          // Check if GPU already exists
          const existingGPU = await prisma.gPU.findFirst({
            where: {
              name: transformedGPU.name,
              manufacturer: transformedGPU.manufacturer,
            },
          })

          if (existingGPU) {
            // Update existing GPU
            await prisma.gPU.update({
              where: { id: existingGPU.id },
              data: transformedGPU,
            })
            updated++
          } else {
            // Create new GPU
            await prisma.gPU.create({
              data: transformedGPU,
            })
            inserted++
          }
        } catch (error) {
          console.error(`Error processing GPU ${gpuData.productName || gpuData.name}:`, error)
        }
      }

      return {
        total: allGPUs.length,
        inserted,
        updated,
        errors: allGPUs.length - inserted - updated,
      }
    } catch (error) {
      console.error('Error seeding GPU data:', error)
      throw error
    }
  }

  /**
   * Transform external GPU data to our schema format
   */
  private static transformExternalGPUData(externalData: any) {
    // Skip empty or header rows
    if (!externalData.productName || 
        externalData.productName === 'Product Name' || 
        externalData.productName === '' ||
        !externalData.id) {
      return null
    }

    // Parse memory information
    const memoryInfo = this.parseMemoryString(externalData.memory || '')
    
    // Parse GPU clocks
    const clockInfo = this.parseClockString(externalData.gpuClock || '')
    
    // Parse memory clock
    const memoryClockInfo = this.parseMemoryClockString(externalData.memoryClock || '')
    
    // Parse shaders/TMUs/ROPs
    const coreInfo = this.parseCoreString(externalData.shadersTMUsROPs || '')
    
    // Parse TDP
    const tdp = this.parseNumberFromString(externalData.tdp || '0')
    
    // Parse suggested PSU
    const suggestedPSU = this.parseNumberFromString(externalData.suggestedPSU || '0')
    
    // Parse dimensions
    const dimensions = this.parseDimensionsString(externalData.dimensions || '')
    
    // Parse launch price
    const launchPrice = this.parseNumberFromString(externalData.executionUnits || '0')
    
    return {
      name: externalData.productName,
      manufacturer: externalData.brand?.toUpperCase() || 'UNKNOWN',
      architecture: externalData.gpuChip || null,
      launchDate: externalData.released ? this.parseDate(externalData.released) : null,
      originalMsrp: launchPrice > 0 ? launchPrice : null,
      
      specifications: {
        memory: {
          size: memoryInfo.size,
          type: memoryInfo.type,
          bandwidth: memoryInfo.bandwidth,
          busWidth: memoryInfo.busWidth,
        },
        cores: {
          cudaCores: coreInfo.shaders || null,
          streamProcessors: coreInfo.shaders || null,
          executionUnits: null,
          rtCores: this.parseRTCores(externalData.computeUnits || ''),
          tensorCores: this.parseTensorCores(externalData.tensorCores || ''),
        },
        clocks: {
          baseClock: clockInfo.base,
          boostClock: clockInfo.boost,
          memoryClock: memoryClockInfo,
        },
        process: externalData.process || 'Unknown',
        transistors: null,
        dieSize: null,
      },
      
      physicalSpecs: {
        dimensions: {
          length: dimensions.length,
          width: dimensions.width,
          height: dimensions.height,
        },
        weight: null,
        slots: 2, // Default assumption
        cooling: {
          type: 'Air',
          fans: 2,
          heatpipes: null,
        },
        ports: this.parsePorts(externalData.ports || ''),
      },
      
      powerRequirements: {
        tdp: tdp,
        recommendedPSU: suggestedPSU,
        connectors: this.parsePowerConnectors(externalData.powerConnector || ''),
        peakPower: null,
        idlePower: null,
      },
    }
  }

  private static parseMemoryString(memoryStr: string) {
    // Example: "24 GB, GDDR6X, 384 bit, 912.4GB/s"
    const sizeMatch = memoryStr.match(/(\d+)\s*GB/)
    const typeMatch = memoryStr.match(/,\s*([A-Z0-9]+)/)
    const busWidthMatch = memoryStr.match(/(\d+)\s*bit/)
    const bandwidthMatch = memoryStr.match(/([\d.]+)\s*[GT]B\/s/)
    
    return {
      size: sizeMatch ? parseInt(sizeMatch[1]) : 0,
      type: typeMatch ? typeMatch[1] : 'Unknown',
      busWidth: busWidthMatch ? parseInt(busWidthMatch[1]) : 0,
      bandwidth: bandwidthMatch ? parseFloat(bandwidthMatch[1]) : 0,
    }
  }

  private static parseClockString(clockStr: string) {
    // Example: "1365 MHz - 1665MHz"
    const clocks = clockStr.match(/(\d+)\s*MHz\s*-\s*(\d+)\s*MHz/)
    
    return {
      base: clocks ? parseInt(clocks[1]) : 0,
      boost: clocks ? parseInt(clocks[2]) : 0,
    }
  }

  private static parseMemoryClockString(clockStr: string) {
    // Example: "1188 MHz"
    const match = clockStr.match(/(\d+)\s*MHz/)
    return match ? parseInt(match[1]) : 0
  }

  private static parseCoreString(coreStr: string) {
    // Example: "10240 / 320 / 112"
    const parts = coreStr.split('/')
    
    return {
      shaders: parts[0] ? parseInt(parts[0].trim()) : 0,
      tmus: parts[1] ? parseInt(parts[1].trim()) : 0,
      rops: parts[2] ? parseInt(parts[2].trim()) : 0,
    }
  }

  private static parseNumberFromString(str: string): number {
    const match = str.match(/(\d+)/)
    return match ? parseInt(match[1]) : 0
  }

  private static parseDimensionsString(dimStr: string) {
    // Example: "285mm*112mm" or "304mm*137mm"
    const match = dimStr.match(/(\d+)mm\s*[*x]\s*(\d+)mm/)
    
    return {
      length: match ? parseInt(match[1]) : 0,
      width: match ? parseInt(match[2]) : 0,
      height: 40, // Default slot height
    }
  }

  private static parseDate(dateStr: string): Date | null {
    if (!dateStr || dateStr === 'Unknown') return null
    
    try {
      // Handle various date formats
      if (dateStr.includes('T')) {
        return new Date(dateStr)
      }
      
      // Handle "Jan 27th, 2022" format
      const match = dateStr.match(/([A-Za-z]+)\s+(\d+)[a-z]*,?\s+(\d{4})/)
      if (match) {
        return new Date(`${match[1]} ${match[2]}, ${match[3]}`)
      }
      
      // Handle year only
      if (/^\d{4}$/.test(dateStr)) {
        return new Date(`${dateStr}-01-01`)
      }
      
      return new Date(dateStr)
    } catch {
      return null
    }
  }

  private static parseTensorCores(tensorStr: string): number | null {
    // Example: "320(3rd Gen)" or "456(4th Gen)"
    const match = tensorStr.match(/(\d+)/)
    return match ? parseInt(match[1]) : null
  }

  private static parseRTCores(rtStr: string): number | null {
    // Example: "80(2nd Gen)"
    const match = rtStr.match(/(\d+)/)
    return match ? parseInt(match[1]) : null
  }

  private static parsePorts(portsStr: string) {
    const displayPortMatch = portsStr.match(/(\d+)x?\s*DisplayPort/i)
    const hdmiMatch = portsStr.match(/(\d+)x?\s*HDMI/i)
    const dviMatch = portsStr.match(/(\d+)x?\s*DVI/i)
    const usbcMatch = portsStr.match(/(\d+)x?\s*USB[- ]?C/i)
    
    return {
      displayPort: displayPortMatch ? parseInt(displayPortMatch[1]) : 0,
      hdmi: hdmiMatch ? parseInt(hdmiMatch[1]) : 0,
      dvi: dviMatch ? parseInt(dviMatch[1]) : 0,
      usbc: usbcMatch ? parseInt(usbcMatch[1]) : 0,
    }
  }

  private static parsePowerConnectors(connectorStr: string): string[] {
    if (!connectorStr || connectorStr === 'none') return []
    
    const connectors = []
    
    if (connectorStr.includes('8-pin')) {
      const match = connectorStr.match(/(\d+)x?\s*8-pin/)
      const count = match ? parseInt(match[1]) : 1
      for (let i = 0; i < count; i++) {
        connectors.push('8-pin')
      }
    }
    
    if (connectorStr.includes('6-pin')) {
      const match = connectorStr.match(/(\d+)x?\s*6-pin/)
      const count = match ? parseInt(match[1]) : 1
      for (let i = 0; i < count; i++) {
        connectors.push('6-pin')
      }
    }
    
    if (connectorStr.includes('12-pin') || connectorStr.includes('16-pin')) {
      connectors.push('12VHPWR')
    }
    
    return connectors
  }

  /**
   * Get GPU statistics for dashboard
   */
  static async getGPUStats() {
    const [total, byManufacturer, byArchitecture, recentlyAdded] = await Promise.all([
      prisma.gPU.count(),
      
      prisma.gPU.groupBy({
        by: ['manufacturer'],
        _count: { manufacturer: true },
        orderBy: { _count: { manufacturer: 'desc' } },
      }),
      
      prisma.gPU.groupBy({
        by: ['architecture'],
        _count: { architecture: true },
        where: { architecture: { not: null } },
        orderBy: { _count: { architecture: 'desc' } },
        take: 10,
      }),
      
      prisma.gPU.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ])

    return {
      total,
      byManufacturer: byManufacturer.map(item => ({
        manufacturer: item.manufacturer,
        count: item._count.manufacturer,
      })),
      byArchitecture: byArchitecture.map(item => ({
        architecture: item.architecture,
        count: item._count.architecture,
      })),
      recentlyAdded,
    }
  }

  /**
   * Get popular GPUs based on wishlist count and reviews
   */
  static async getPopularGPUs(limit: number = 10) {
    return await prisma.gPU.findMany({
      take: limit,
      include: {
        _count: {
          select: {
            wishlists: true,
            reviews: true,
          },
        },
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 1,
          distinct: ['retailer'],
        },
      },
      orderBy: [
        { wishlists: { _count: 'desc' } },
        { reviews: { _count: 'desc' } },
      ],
    })
  }

  /**
   * Get GPUs by category/workload
   */
  static async getGPUsByCategory(category: string, limit: number = 20) {
    const where: any = {}
    
    // Define category-specific filters
    switch (category.toLowerCase()) {
      case 'gaming':
        // Focus on GPUs with good gaming performance
        where.specifications = {
          path: ['memory', 'size'],
          gte: 6, // At least 6GB VRAM for modern gaming
        }
        break
        
      case 'budget':
        // Focus on budget-friendly options
        where.originalMsrp = { lte: 400 }
        break
        
      case 'high-end':
        // Focus on high-end GPUs
        where.originalMsrp = { gte: 800 }
        break
        
      case 'content-creation':
        // Focus on GPUs good for content creation
        where.specifications = {
          path: ['memory', 'size'],
          gte: 8, // At least 8GB VRAM for content creation
        }
        break
        
      case 'latest':
        // Focus on recently launched GPUs
        const twoYearsAgo = new Date()
        twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2)
        where.launchDate = { gte: twoYearsAgo }
        break
    }

    return await prisma.gPU.findMany({
      where,
      take: limit,
      include: {
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 1,
          distinct: ['retailer'],
        },
        reviews: {
          select: { rating: true },
        },
        _count: {
          select: { reviews: true },
        },
      },
      orderBy: [
        { launchDate: 'desc' },
        { name: 'asc' },
      ],
    })
  }

  /**
   * Get all GPUs with pagination and filtering
   */
  static async getAllGPUs(options: {
    page?: number
    limit?: number
    manufacturer?: string
    architecture?: string
    minPrice?: number
    maxPrice?: number
    minMemory?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}) {
    const {
      page = 1,
      limit = 20,
      manufacturer,
      architecture,
      minPrice,
      maxPrice,
      minMemory,
      sortBy = 'name',
      sortOrder = 'asc'
    } = options

    const skip = (page - 1) * limit
    const where: any = {}

    // Apply filters
    if (manufacturer) {
      where.manufacturer = manufacturer
    }

    if (architecture) {
      where.architecture = architecture
    }

    if (minPrice || maxPrice) {
      where.originalMsrp = {}
      if (minPrice) where.originalMsrp.gte = minPrice
      if (maxPrice) where.originalMsrp.lte = maxPrice
    }

    if (minMemory) {
      where.specifications = {
        path: ['memory', 'size'],
        gte: minMemory
      }
    }

    // Determine sort order
    let orderBy: any = { name: sortOrder }
    if (sortBy === 'price') {
      orderBy = { originalMsrp: sortOrder }
    } else if (sortBy === 'launch') {
      orderBy = { launchDate: sortOrder }
    }

    const [gpus, total] = await Promise.all([
      prisma.gPU.findMany({
        skip,
        take: limit,
        where,
        orderBy
      }),
      prisma.gPU.count({ where })
    ])

    return {
      gpus: gpus.map(gpu => this.transformGPUData(gpu)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Get GPU by ID
   */
  static async getGPUById(id: string) {
    const gpu = await prisma.gPU.findUnique({
      where: { id }
    })
    return gpu ? this.transformGPUData(gpu) : null
  }

  /**
   * Search GPUs by name, manufacturer, or architecture
   */
  static async searchGPUs(query: string, limit: number = 10) {
    const gpus = await prisma.gPU.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { manufacturer: { contains: query, mode: 'insensitive' } },
          { architecture: { contains: query, mode: 'insensitive' } }
        ]
      },
      take: limit,
      orderBy: { name: 'asc' }
    })
    return gpus.map(gpu => this.transformGPUData(gpu))
  }

  /**
   * Create a new GPU
   */
  static async createGPU(data: any) {
    return await prisma.gPU.create({
      data
    })
  }

  /**
   * Update an existing GPU
   */
  static async updateGPU(id: string, data: any) {
    return await prisma.gPU.update({
      where: { id },
      data
    })
  }

  /**
   * Delete a GPU
   */
  static async deleteGPU(id: string) {
    return await prisma.gPU.delete({
      where: { id }
    })
  }

  /**
   * Get GPUs by manufacturer
   */
  static async getGPUsByManufacturer(manufacturer: string) {
    const gpus = await prisma.gPU.findMany({
      where: { manufacturer },
      orderBy: { name: 'asc' }
    })
    return gpus.map(gpu => this.transformGPUData(gpu))
  }

  /**
   * Get GPUs by price range
   */
  static async getGPUsByPriceRange(minPrice: number | null, maxPrice: number | null) {
    const where: any = {}
    
    if (minPrice || maxPrice) {
      where.originalMsrp = {}
      if (minPrice) where.originalMsrp.gte = minPrice
      if (maxPrice) where.originalMsrp.lte = maxPrice
    }

    const gpus = await prisma.gPU.findMany({
      where,
      orderBy: { originalMsrp: 'asc' }
    })
    
    return gpus.map(gpu => this.transformGPUData(gpu))
  }

  /**
   * Get GPU statistics
   */
  static async getGPUStatistics() {
    const [total, nvidiaCount, amdCount, intelCount] = await Promise.all([
      prisma.gPU.count(),
      prisma.gPU.count({ where: { manufacturer: 'NVIDIA' } }),
      prisma.gPU.count({ where: { manufacturer: 'AMD' } }),
      prisma.gPU.count({ where: { manufacturer: 'Intel' } })
    ])

    return {
      total,
      byManufacturer: {
        NVIDIA: nvidiaCount,
        AMD: amdCount,
        Intel: intelCount
      }
    }
  }

  /**
   * Transform GPU data to convert Decimal fields to numbers
   */
  private static transformGPUData(gpu: any) {
    return {
      ...gpu,
      originalMsrp: gpu.originalMsrp ? parseFloat(gpu.originalMsrp.toString()) : null
    }
  }
}
