import { prisma } from './prisma'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  stats: {
    totalGPUs: number
    validGPUs: number
    invalidGPUs: number
    missingSpecs: number
    duplicates: number
  }
}

export class DataValidationService {
  /**
   * Validate all GPU data in the database
   */
  static async validateAllGPUs(): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      stats: {
        totalGPUs: 0,
        validGPUs: 0,
        invalidGPUs: 0,
        missingSpecs: 0,
        duplicates: 0
      }
    }

    try {
      const gpus = await prisma.gPU.findMany()
      result.stats.totalGPUs = gpus.length

      // Check for duplicates
      const duplicates = await this.findDuplicateGPUs()
      result.stats.duplicates = duplicates.length
      
      if (duplicates.length > 0) {
        result.warnings.push(`Found ${duplicates.length} potential duplicate GPUs`)
        duplicates.forEach(dup => {
          result.warnings.push(`Duplicate: ${dup.name} (${dup.count} entries)`)
        })
      }

      // Validate each GPU
      for (const gpu of gpus) {
        const gpuValidation = this.validateGPU(gpu)
        
        if (gpuValidation.isValid) {
          result.stats.validGPUs++
        } else {
          result.stats.invalidGPUs++
          result.isValid = false
          result.errors.push(`GPU ${gpu.name} (${gpu.id}): ${gpuValidation.errors.join(', ')}`)
        }

        if (gpuValidation.warnings.length > 0) {
          result.warnings.push(`GPU ${gpu.name}: ${gpuValidation.warnings.join(', ')}`)
        }

        if (gpuValidation.missingSpecs) {
          result.stats.missingSpecs++
        }
      }

      return result
    } catch (error) {
      result.isValid = false
      result.errors.push(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  /**
   * Validate a single GPU object
   */
  static validateGPU(gpu: any): { isValid: boolean; errors: string[]; warnings: string[]; missingSpecs: boolean } {
    const errors: string[] = []
    const warnings: string[] = []
    let missingSpecs = false

    // Required fields validation
    if (!gpu.name || gpu.name.trim().length === 0) {
      errors.push('Name is required')
    }

    if (!gpu.manufacturer || !['NVIDIA', 'AMD', 'Intel'].includes(gpu.manufacturer)) {
      errors.push('Valid manufacturer is required (NVIDIA, AMD, or Intel)')
    }

    // Specifications validation
    if (!gpu.specifications) {
      errors.push('Specifications are required')
      missingSpecs = true
    } else {
      const specs = gpu.specifications as any

      // Memory validation
      if (!specs.memory) {
        errors.push('Memory specifications are required')
        missingSpecs = true
      } else {
        if (!specs.memory.size || specs.memory.size <= 0) {
          errors.push('Valid memory size is required')
        }
        if (!specs.memory.type) {
          warnings.push('Memory type is missing')
        }
        if (!specs.memory.bandwidth || specs.memory.bandwidth <= 0) {
          warnings.push('Memory bandwidth is missing or invalid')
        }
      }

      // Cores validation
      if (!specs.cores) {
        warnings.push('Core specifications are missing')
      } else {
        const hasCudaCores = specs.cores.cudaCores && specs.cores.cudaCores > 0
        const hasStreamProcessors = specs.cores.streamProcessors && specs.cores.streamProcessors > 0
        const hasExecutionUnits = specs.cores.executionUnits && specs.cores.executionUnits > 0

        if (!hasCudaCores && !hasStreamProcessors && !hasExecutionUnits) {
          warnings.push('No valid core count found (CUDA cores, Stream Processors, or Execution Units)')
        }
      }

      // Clock speeds validation
      if (!specs.clocks) {
        warnings.push('Clock speed specifications are missing')
      } else {
        if (!specs.clocks.baseClock || specs.clocks.baseClock <= 0) {
          warnings.push('Base clock is missing or invalid')
        }
        if (!specs.clocks.boostClock || specs.clocks.boostClock <= 0) {
          warnings.push('Boost clock is missing or invalid')
        }
        if (specs.clocks.baseClock && specs.clocks.boostClock && specs.clocks.baseClock >= specs.clocks.boostClock) {
          warnings.push('Base clock should be lower than boost clock')
        }
      }
    }

    // Physical specifications validation
    if (!gpu.physicalSpecs) {
      warnings.push('Physical specifications are missing')
    } else {
      const physSpecs = gpu.physicalSpecs as any
      
      if (!physSpecs.dimensions) {
        warnings.push('Physical dimensions are missing')
      } else {
        if (!physSpecs.dimensions.length || physSpecs.dimensions.length <= 0) {
          warnings.push('GPU length is missing or invalid')
        }
        if (!physSpecs.dimensions.width || physSpecs.dimensions.width <= 0) {
          warnings.push('GPU width is missing or invalid')
        }
        if (!physSpecs.dimensions.height || physSpecs.dimensions.height <= 0) {
          warnings.push('GPU height is missing or invalid')
        }
      }
    }

    // Power requirements validation
    if (!gpu.powerRequirements) {
      warnings.push('Power requirements are missing')
    } else {
      const powerReqs = gpu.powerRequirements as any
      
      if (!powerReqs.tdp || powerReqs.tdp <= 0) {
        warnings.push('TDP is missing or invalid')
      }
      if (!powerReqs.recommendedPSU || powerReqs.recommendedPSU <= 0) {
        warnings.push('Recommended PSU wattage is missing or invalid')
      }
      if (powerReqs.tdp && powerReqs.recommendedPSU && powerReqs.tdp >= powerReqs.recommendedPSU) {
        warnings.push('Recommended PSU should be higher than TDP')
      }
    }

    // Launch date validation
    if (gpu.launchDate) {
      const launchDate = new Date(gpu.launchDate)
      const now = new Date()
      const earliestGPU = new Date('2000-01-01') // Reasonable earliest date for modern GPUs

      if (launchDate > now) {
        warnings.push('Launch date is in the future')
      }
      if (launchDate < earliestGPU) {
        warnings.push('Launch date seems too early')
      }
    }

    // MSRP validation
    if (gpu.originalMsrp && (gpu.originalMsrp <= 0 || gpu.originalMsrp > 10000)) {
      warnings.push('Original MSRP seems unrealistic')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      missingSpecs
    }
  }

  /**
   * Find duplicate GPUs based on name similarity
   */
  static async findDuplicateGPUs(): Promise<Array<{ name: string; count: number; ids: string[] }>> {
    const gpus = await prisma.gPU.findMany({
      select: {
        id: true,
        name: true,
        manufacturer: true
      }
    })

    const duplicates: Array<{ name: string; count: number; ids: string[] }> = []
    const nameGroups: Record<string, string[]> = {}

    // Group GPUs by normalized name
    for (const gpu of gpus) {
      const normalizedName = this.normalizeGPUName(gpu.name)
      if (!nameGroups[normalizedName]) {
        nameGroups[normalizedName] = []
      }
      nameGroups[normalizedName].push(gpu.id)
    }

    // Find groups with more than one GPU
    for (const [name, ids] of Object.entries(nameGroups)) {
      if (ids.length > 1) {
        duplicates.push({
          name,
          count: ids.length,
          ids
        })
      }
    }

    return duplicates
  }

  /**
   * Normalize GPU name for duplicate detection
   */
  private static normalizeGPUName(name: string): string {
    return name
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .trim()
  }

  /**
   * Fix common data issues automatically
   */
  static async autoFixDataIssues(): Promise<{ fixed: number; errors: string[] }> {
    const result = { fixed: 0, errors: [] }

    try {
      // Fix missing recommended PSU values (set to TDP * 2)
      const gpusWithMissingPSU = await prisma.gPU.findMany({
        where: {
          powerRequirements: {
            path: ['recommendedPSU'],
            equals: null
          }
        }
      })

      for (const gpu of gpusWithMissingPSU) {
        const powerReqs = gpu.powerRequirements as any
        if (powerReqs?.tdp) {
          const updatedPowerReqs = {
            ...powerReqs,
            recommendedPSU: powerReqs.tdp * 2
          }

          await prisma.gPU.update({
            where: { id: gpu.id },
            data: { powerRequirements: updatedPowerReqs }
          })
          result.fixed++
        }
      }

      // Fix missing slot counts (estimate from height)
      const gpusWithMissingSlots = await prisma.gPU.findMany({
        where: {
          physicalSpecs: {
            path: ['slots'],
            equals: null
          }
        }
      })

      for (const gpu of gpusWithMissingSlots) {
        const physSpecs = gpu.physicalSpecs as any
        if (physSpecs?.dimensions?.height) {
          const estimatedSlots = Math.ceil(physSpecs.dimensions.height / 20) // 20mm per slot
          const updatedPhysSpecs = {
            ...physSpecs,
            slots: estimatedSlots
          }

          await prisma.gPU.update({
            where: { id: gpu.id },
            data: { physicalSpecs: updatedPhysSpecs }
          })
          result.fixed++
        }
      }

      return result
    } catch (error) {
      result.errors.push(`Auto-fix failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  /**
   * Generate data quality report
   */
  static async generateDataQualityReport(): Promise<{
    summary: {
      totalGPUs: number
      dataCompleteness: number
      duplicateRate: number
      validationScore: number
    }
    details: ValidationResult
    recommendations: string[]
  }> {
    const validation = await this.validateAllGPUs()
    const recommendations: string[] = []

    // Calculate metrics
    const dataCompleteness = validation.stats.totalGPUs > 0 
      ? ((validation.stats.totalGPUs - validation.stats.missingSpecs) / validation.stats.totalGPUs) * 100 
      : 0

    const duplicateRate = validation.stats.totalGPUs > 0 
      ? (validation.stats.duplicates / validation.stats.totalGPUs) * 100 
      : 0

    const validationScore = validation.stats.totalGPUs > 0 
      ? (validation.stats.validGPUs / validation.stats.totalGPUs) * 100 
      : 0

    // Generate recommendations
    if (validation.stats.duplicates > 0) {
      recommendations.push(`Remove ${validation.stats.duplicates} duplicate GPU entries`)
    }

    if (validation.stats.missingSpecs > 0) {
      recommendations.push(`Complete specifications for ${validation.stats.missingSpecs} GPUs`)
    }

    if (validation.errors.length > 0) {
      recommendations.push(`Fix ${validation.errors.length} critical data errors`)
    }

    if (dataCompleteness < 90) {
      recommendations.push('Improve data completeness by adding missing specifications')
    }

    if (validationScore < 95) {
      recommendations.push('Address validation errors to improve data quality')
    }

    return {
      summary: {
        totalGPUs: validation.stats.totalGPUs,
        dataCompleteness: Math.round(dataCompleteness * 100) / 100,
        duplicateRate: Math.round(duplicateRate * 100) / 100,
        validationScore: Math.round(validationScore * 100) / 100
      },
      details: validation,
      recommendations
    }
  }
}