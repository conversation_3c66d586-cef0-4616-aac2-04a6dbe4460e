import { prisma } from './prisma'

interface BenchmarkSeed {
  gpuName: string
  benchmarks: Array<{
    benchmarkType: 'gaming' | 'professional' | 'synthetic'
    testName: string
    resolution: string
    settings: string
    fps?: number
    score?: number
    source: string
  }>
}

const benchmarkData: BenchmarkSeed[] = [
  {
    gpuName: 'GeForce RTX 3060 Ti GDDR6X',
    benchmarks: [
      // Gaming benchmarks
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '4K', settings: 'Ultra', fps: 85, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1440p', settings: 'Ultra', fps: 142, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1080p', settings: 'Ultra', fps: 189, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '4K', settings: 'Ultra', fps: 156, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1440p', settings: 'Ultra', fps: 234, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1080p', settings: 'Ultra', fps: 298, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '4K', settings: 'Ultra', fps: 98, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1440p', settings: 'Ultra', fps: 156, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1080p', settings: 'Ultra', fps: 198, source: 'TechPowerUp' },
      
      // Synthetic benchmarks
      { benchmarkType: 'synthetic', testName: '3DMark Time Spy', resolution: '1440p', settings: 'Default', score: 28450, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: '3DMark Fire Strike', resolution: '1080p', settings: 'Default', score: 45680, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: 'Unigine Superposition', resolution: '4K', settings: 'Extreme', score: 18950, source: 'Unigine' },
      
      // Professional benchmarks
      { benchmarkType: 'professional', testName: 'Blender BMW27', resolution: '1080p', settings: 'CUDA', score: 245, source: 'Blender Open Data' },
      { benchmarkType: 'professional', testName: 'V-Ray Benchmark', resolution: '1080p', settings: 'CUDA', score: 2890, source: 'Chaos Group' }
    ]
  },
  {
    gpuName: 'GeForce RTX 3070 Ti 8 GB GA102',
    benchmarks: [
      // Gaming benchmarks
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '4K', settings: 'Ultra', fps: 68, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1440p', settings: 'Ultra', fps: 115, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1080p', settings: 'Ultra', fps: 156, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '4K', settings: 'Ultra', fps: 125, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1440p', settings: 'Ultra', fps: 189, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1080p', settings: 'Ultra', fps: 245, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '4K', settings: 'Ultra', fps: 78, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1440p', settings: 'Ultra', fps: 125, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1080p', settings: 'Ultra', fps: 168, source: 'TechPowerUp' },
      
      // Synthetic benchmarks
      { benchmarkType: 'synthetic', testName: '3DMark Time Spy', resolution: '1440p', settings: 'Default', score: 22890, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: '3DMark Fire Strike', resolution: '1080p', settings: 'Default', score: 36750, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: 'Unigine Superposition', resolution: '4K', settings: 'Extreme', score: 15240, source: 'Unigine' },
      
      // Professional benchmarks
      { benchmarkType: 'professional', testName: 'Blender BMW27', resolution: '1080p', settings: 'CUDA', score: 198, source: 'Blender Open Data' },
      { benchmarkType: 'professional', testName: 'V-Ray Benchmark', resolution: '1080p', settings: 'CUDA', score: 2340, source: 'Chaos Group' }
    ]
  },
  {
    gpuName: 'GeForce RTX 3060 8 GB',
    benchmarks: [
      // Gaming benchmarks
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '4K', settings: 'Ultra', fps: 45, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1440p', settings: 'Ultra', fps: 78, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1080p', settings: 'Ultra', fps: 112, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '4K', settings: 'Ultra', fps: 89, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1440p', settings: 'Ultra', fps: 134, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1080p', settings: 'Ultra', fps: 178, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '4K', settings: 'Ultra', fps: 56, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1440p', settings: 'Ultra', fps: 89, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1080p', settings: 'Ultra', fps: 125, source: 'TechPowerUp' },
      
      // Synthetic benchmarks
      { benchmarkType: 'synthetic', testName: '3DMark Time Spy', resolution: '1440p', settings: 'Default', score: 16780, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: '3DMark Fire Strike', resolution: '1080p', settings: 'Default', score: 27450, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: 'Unigine Superposition', resolution: '4K', settings: 'Extreme', score: 11230, source: 'Unigine' },
      
      // Professional benchmarks
      { benchmarkType: 'professional', testName: 'Blender BMW27', resolution: '1080p', settings: 'CUDA', score: 145, source: 'Blender Open Data' },
      { benchmarkType: 'professional', testName: 'V-Ray Benchmark', resolution: '1080p', settings: 'CUDA', score: 1780, source: 'Chaos Group' }
    ]
  },
  {
    gpuName: 'GeForce RTX 2060 12 GB',
    benchmarks: [
      // Gaming benchmarks
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '4K', settings: 'Ultra', fps: 32, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1440p', settings: 'Ultra', fps: 58, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Cyberpunk 2077', resolution: '1080p', settings: 'Ultra', fps: 85, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '4K', settings: 'Ultra', fps: 65, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1440p', settings: 'Ultra', fps: 98, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Call of Duty: Modern Warfare II', resolution: '1080p', settings: 'Ultra', fps: 134, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '4K', settings: 'Ultra', fps: 42, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1440p', settings: 'Ultra', fps: 68, source: 'TechPowerUp' },
      { benchmarkType: 'gaming', testName: 'Assassins Creed Valhalla', resolution: '1080p', settings: 'Ultra', fps: 95, source: 'TechPowerUp' },
      
      // Synthetic benchmarks
      { benchmarkType: 'synthetic', testName: '3DMark Time Spy', resolution: '1440p', settings: 'Default', score: 12450, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: '3DMark Fire Strike', resolution: '1080p', settings: 'Default', score: 19870, source: '3DMark Database' },
      { benchmarkType: 'synthetic', testName: 'Unigine Superposition', resolution: '4K', settings: 'Extreme', score: 8780, source: 'Unigine' },
      
      // Professional benchmarks
      { benchmarkType: 'professional', testName: 'Blender BMW27', resolution: '1080p', settings: 'CUDA', score: 98, source: 'Blender Open Data' },
      { benchmarkType: 'professional', testName: 'V-Ray Benchmark', resolution: '1080p', settings: 'CUDA', score: 1250, source: 'Chaos Group' }
    ]
  }
]

export async function seedBenchmarks() {
  console.log('Starting benchmark seeding...')

  for (const gpuData of benchmarkData) {
    // Find GPU by name
    const gpu = await prisma.gPU.findFirst({
      where: {
        name: {
          contains: gpuData.gpuName,
          mode: 'insensitive'
        }
      }
    })

    if (!gpu) {
      console.log(`GPU not found: ${gpuData.gpuName}`)
      continue
    }

    console.log(`Seeding benchmarks for ${gpu.name}...`)

    // Create benchmarks for this GPU
    for (const benchmark of gpuData.benchmarks) {
      try {
        await prisma.performanceBenchmark.create({
          data: {
            gpuId: gpu.id,
            benchmarkType: benchmark.benchmarkType,
            testName: benchmark.testName,
            resolution: benchmark.resolution,
            settings: benchmark.settings,
            fps: benchmark.fps ? parseFloat(benchmark.fps.toString()) : null,
            score: benchmark.score || null,
            testDate: new Date(),
            source: benchmark.source,
            benchmarkData: null
          }
        })
      } catch (error) {
        // Skip duplicates
        if (error instanceof Error && error.message.includes('Unique constraint')) {
          continue
        }
        console.error(`Error creating benchmark for ${gpu.name}:`, error)
      }
    }
  }

  console.log('Benchmark seeding completed!')
}

// Run this function to seed the benchmarks
if (require.main === module) {
  seedBenchmarks()
    .then(() => {
      console.log('Seeding completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}