import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

export class WebSocketServer {
  private io: SocketIOServer;
  private subscriber: Redis;

  constructor(httpServer: HTTPServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.subscriber = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
    this.setupEventHandlers();
    this.subscribeToRedis();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);

      // Handle GPU price subscription
      socket.on('subscribe-gpu-prices', (gpuId: string) => {
        socket.join(`gpu-prices-${gpuId}`);
        console.log(`Client ${socket.id} subscribed to GPU prices for ${gpuId}`);
      });

      // Handle unsubscription
      socket.on('unsubscribe-gpu-prices', (gpuId: string) => {
        socket.leave(`gpu-prices-${gpuId}`);
        console.log(`Client ${socket.id} unsubscribed from GPU prices for ${gpuId}`);
      });

      // Handle market trends subscription
      socket.on('subscribe-market-trends', () => {
        socket.join('market-trends');
        console.log(`Client ${socket.id} subscribed to market trends`);
      });

      socket.on('unsubscribe-market-trends', () => {
        socket.leave('market-trends');
        console.log(`Client ${socket.id} unsubscribed from market trends`);
      });

      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
      });
    });
  }

  private subscribeToRedis() {
    // Subscribe to price updates from Redis
    this.subscriber.subscribe('price-updates', (err) => {
      if (err) {
        console.error('Failed to subscribe to price updates:', err);
      } else {
        console.log('Subscribed to price updates channel');
      }
    });

    // Subscribe to market trend updates
    this.subscriber.subscribe('market-trends', (err) => {
      if (err) {
        console.error('Failed to subscribe to market trends:', err);
      } else {
        console.log('Subscribed to market trends channel');
      }
    });

    // Handle incoming messages
    this.subscriber.on('message', (channel, message) => {
      try {
        const data = JSON.parse(message);

        switch (channel) {
          case 'price-updates':
            this.handlePriceUpdate(data);
            break;
          case 'market-trends':
            this.handleMarketTrendUpdate(data);
            break;
          default:
            console.log('Unknown channel:', channel);
        }
      } catch (error) {
        console.error('Error processing Redis message:', error);
      }
    });
  }

  private handlePriceUpdate(data: any) {
    const { gpuId, retailer, price, availability, timestamp } = data;
    
    // Emit to clients subscribed to this GPU's prices
    this.io.to(`gpu-prices-${gpuId}`).emit('price-update', {
      gpuId,
      retailer,
      price,
      availability,
      timestamp
    });

    console.log(`Price update sent for GPU ${gpuId}: ${retailer} - $${price}`);
  }

  private handleMarketTrendUpdate(data: any) {
    // Emit to clients subscribed to market trends
    this.io.to('market-trends').emit('market-trend-update', data);
    console.log('Market trend update sent to subscribers');
  }

  // Method to broadcast price updates (called by pricing service)
  public broadcastPriceUpdate(gpuId: string, retailer: string, price: number, availability: string) {
    const data = {
      gpuId,
      retailer,
      price,
      availability,
      timestamp: new Date().toISOString()
    };

    this.io.to(`gpu-prices-${gpuId}`).emit('price-update', data);
  }

  // Method to broadcast market trend updates
  public broadcastMarketTrends(trends: any[]) {
    this.io.to('market-trends').emit('market-trend-update', {
      trends,
      timestamp: new Date().toISOString()
    });
  }

  public getIO() {
    return this.io;
  }
}

let websocketServer: WebSocketServer | null = null;

export function initializeWebSocketServer(httpServer: HTTPServer): WebSocketServer {
  if (!websocketServer) {
    websocketServer = new WebSocketServer(httpServer);
  }
  return websocketServer;
}

export function getWebSocketServer(): WebSocketServer | null {
  return websocketServer;
}