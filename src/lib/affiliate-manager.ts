import { prisma } from './prisma';

export interface AffiliateConfig {
  retailer: string;
  affiliateId: string;
  baseUrl: string;
  linkFormat: string; // Template for affiliate links
  commission: number; // Percentage
  isActive: boolean;
  trackingParams: Record<string, string>;
}

export interface AffiliateClick {
  id: string;
  retailer: string;
  gpuId: string;
  userId?: string;
  sessionId: string;
  clickedAt: Date;
  userAgent?: string;
  referrer?: string;
  ipAddress?: string;
}

export interface AffiliateStats {
  retailer: string;
  clicks: number;
  conversions: number;
  revenue: number;
  conversionRate: number;
  period: string;
}

// Affiliate configurations for different retailers
const AFFILIATE_CONFIGS: AffiliateConfig[] = [
  {
    retailer: 'Amazon',
    affiliateId: process.env.AMAZON_AFFILIATE_ID || 'gpulabs-20',
    baseUrl: 'https://www.amazon.com',
    linkFormat: '{baseUrl}/dp/{productId}?tag={affiliateId}&linkCode=ogi&th=1&psc=1',
    commission: 4.0, // 4% commission rate
    isActive: true,
    trackingParams: {
      tag: process.env.AMAZON_AFFILIATE_ID || 'gpulabs-20',
      linkCode: 'ogi',
      ref: 'gpulabs',
      utm_source: 'gpulabs',
      utm_medium: 'affiliate',
      utm_campaign: 'gpu_comparison'
    }
  },
  {
    retailer: 'Newegg',
    affiliateId: process.env.NEWEGG_AFFILIATE_ID || 'gpulabs',
    baseUrl: 'https://www.newegg.com',
    linkFormat: '{baseUrl}/p/{productId}?nm_mc=AFC-RAN-COM&cm_mmc=AFC-RAN-COM&utm_medium=affiliates&utm_source={affiliateId}',
    commission: 2.5, // 2.5% commission rate
    isActive: true,
    trackingParams: {
      nm_mc: 'AFC-RAN-COM',
      cm_mmc: 'AFC-RAN-COM',
      utm_medium: 'affiliates',
      utm_source: process.env.NEWEGG_AFFILIATE_ID || 'gpulabs',
      utm_campaign: 'gpu_deals'
    }
  },
  {
    retailer: 'Best Buy',
    affiliateId: process.env.BESTBUY_AFFILIATE_ID || 'gpulabs',
    baseUrl: 'https://www.bestbuy.com',
    linkFormat: '{baseUrl}/site/searchpage.jsp?st={productName}&intl=nosplash&ref={affiliateId}',
    commission: 3.0, // 3% commission rate
    isActive: true,
    trackingParams: {
      ref: process.env.BESTBUY_AFFILIATE_ID || 'gpulabs',
      utm_source: 'gpulabs',
      utm_medium: 'affiliate',
      utm_campaign: 'gpu_search'
    }
  },
  {
    retailer: 'Micro Center',
    affiliateId: process.env.MICROCENTER_AFFILIATE_ID || 'gpulabs',
    baseUrl: 'https://www.microcenter.com',
    linkFormat: '{baseUrl}/product/{productId}?ref={affiliateId}&utm_source=gpulabs',
    commission: 1.5, // 1.5% commission rate
    isActive: true,
    trackingParams: {
      ref: process.env.MICROCENTER_AFFILIATE_ID || 'gpulabs',
      utm_source: 'gpulabs',
      utm_medium: 'affiliate'
    }
  }
];

export class AffiliateManager {
  private readonly CLICK_EXPIRY_DAYS = 30; // Clicks expire after 30 days
  private readonly SESSION_TIMEOUT_HOURS = 24; // Session timeout

  /**
   * Generate affiliate link for a product
   */
  generateAffiliateLink(
    retailer: string, 
    originalUrl: string, 
    gpuId: string,
    productId?: string,
    productName?: string
  ): string {
    const config = this.getRetailerConfig(retailer);
    if (!config || !config.isActive) {
      return originalUrl; // Return original URL if no affiliate config
    }

    try {
      // Extract product ID from URL if not provided
      if (!productId) {
        productId = this.extractProductId(originalUrl, retailer);
      }

      // Build affiliate link using template
      let affiliateLink = config.linkFormat
        .replace('{baseUrl}', config.baseUrl)
        .replace('{affiliateId}', config.affiliateId)
        .replace('{productId}', productId || '')
        .replace('{productName}', encodeURIComponent(productName || ''));

      // Add tracking parameters
      const url = new URL(affiliateLink);
      Object.entries(config.trackingParams).forEach(([key, value]) => {
        url.searchParams.set(key, value);
      });

      // Add custom tracking parameters
      url.searchParams.set('gpulabs_gpu', gpuId);
      url.searchParams.set('gpulabs_timestamp', Date.now().toString());

      return url.toString();

    } catch (error) {
      console.error(`Error generating affiliate link for ${retailer}:`, error);
      return originalUrl; // Fallback to original URL
    }
  }

  /**
   * Track affiliate click
   */
  async trackClick(
    retailer: string,
    gpuId: string,
    sessionId: string,
    metadata: {
      userId?: string;
      userAgent?: string;
      referrer?: string;
      ipAddress?: string;
    }
  ): Promise<string> {
    try {
      const clickRecord = await prisma.affiliateClick.create({
        data: {
          retailer,
          gpuId,
          sessionId,
          userId: metadata.userId,
          userAgent: metadata.userAgent,
          referrer: metadata.referrer,
          ipAddress: metadata.ipAddress,
          clickedAt: new Date()
        }
      });

      console.log(`Tracked affiliate click: ${retailer} for GPU ${gpuId}`);
      return clickRecord.id;

    } catch (error) {
      console.error('Error tracking affiliate click:', error);
      throw error;
    }
  }

  /**
   * Get affiliate statistics
   */
  async getAffiliateStats(
    retailer?: string,
    days: number = 30
  ): Promise<AffiliateStats[]> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      const whereClause = {
        clickedAt: { gte: startDate },
        ...(retailer && { retailer })
      };

      // Get click counts by retailer
      const clickStats = await prisma.affiliateClick.groupBy({
        by: ['retailer'],
        where: whereClause,
        _count: { id: true }
      });

      // For now, we'll mock conversion data since we don't have actual purchase tracking
      // In production, you'd integrate with retailer APIs or use conversion tracking
      const stats: AffiliateStats[] = clickStats.map(stat => {
        const config = this.getRetailerConfig(stat.retailer);
        const clicks = stat._count.id;
        const estimatedConversions = Math.floor(clicks * 0.05); // Assume 5% conversion rate
        const estimatedRevenue = estimatedConversions * 500 * (config?.commission || 0) / 100; // Assume $500 avg order

        return {
          retailer: stat.retailer,
          clicks,
          conversions: estimatedConversions,
          revenue: estimatedRevenue,
          conversionRate: clicks > 0 ? (estimatedConversions / clicks) * 100 : 0,
          period: `${days} days`
        };
      });

      return stats;

    } catch (error) {
      console.error('Error getting affiliate stats:', error);
      throw error;
    }
  }

  /**
   * Get top performing GPUs by affiliate clicks
   */
  async getTopPerformingGPUs(days: number = 30, limit: number = 10): Promise<Array<{
    gpuId: string;
    gpuName: string;
    clicks: number;
    retailers: string[];
  }>> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const clicksByGPU = await prisma.affiliateClick.groupBy({
        by: ['gpuId'],
        where: {
          clickedAt: { gte: startDate }
        },
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: limit
      });

      // Get GPU details and retailer breakdown
      const results = await Promise.all(
        clicksByGPU.map(async (stat) => {
          const gpu = await prisma.gPU.findUnique({
            where: { id: stat.gpuId },
            select: { name: true }
          });

          const retailers = await prisma.affiliateClick.findMany({
            where: {
              gpuId: stat.gpuId,
              clickedAt: { gte: startDate }
            },
            select: { retailer: true },
            distinct: ['retailer']
          });

          return {
            gpuId: stat.gpuId,
            gpuName: gpu?.name || 'Unknown GPU',
            clicks: stat._count.id,
            retailers: retailers.map(r => r.retailer)
          };
        })
      );

      return results;

    } catch (error) {
      console.error('Error getting top performing GPUs:', error);
      throw error;
    }
  }

  /**
   * Clean up old click data
   */
  async cleanupOldClicks(): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - this.CLICK_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
      
      const result = await prisma.affiliateClick.deleteMany({
        where: {
          clickedAt: { lt: cutoffDate }
        }
      });

      console.log(`Cleaned up ${result.count} old affiliate clicks`);
      return result.count;

    } catch (error) {
      console.error('Error cleaning up old clicks:', error);
      return 0;
    }
  }

  /**
   * Get retailer configuration
   */
  private getRetailerConfig(retailer: string): AffiliateConfig | null {
    return AFFILIATE_CONFIGS.find(config => 
      config.retailer.toLowerCase() === retailer.toLowerCase()
    ) || null;
  }

  /**
   * Extract product ID from URL
   */
  private extractProductId(url: string, retailer: string): string {
    try {
      const urlObj = new URL(url);
      
      switch (retailer.toLowerCase()) {
        case 'amazon':
          // Amazon: /dp/PRODUCTID or /gp/product/PRODUCTID
          const amazonMatch = url.match(/\/(?:dp|gp\/product)\/([A-Z0-9]{10})/);
          return amazonMatch ? amazonMatch[1] : '';
          
        case 'newegg':
          // Newegg: /p/PRODUCTID
          const neweggMatch = url.match(/\/p\/([A-Z0-9-]+)/);
          return neweggMatch ? neweggMatch[1] : '';
          
        case 'best buy':
          // Best Buy: /site/productid.p?skuId=PRODUCTID
          const bestbuyMatch = url.match(/skuId=(\d+)/);
          return bestbuyMatch ? bestbuyMatch[1] : '';
          
        case 'micro center':
          // Micro Center: /product/PRODUCTID/name
          const microcenterMatch = url.match(/\/product\/(\d+)\//);
          return microcenterMatch ? microcenterMatch[1] : '';
          
        default:
          return '';
      }
    } catch (error) {
      console.error(`Error extracting product ID from ${url}:`, error);
      return '';
    }
  }

  /**
   * Get all active affiliate configurations
   */
  getActiveConfigs(): AffiliateConfig[] {
    return AFFILIATE_CONFIGS.filter(config => config.isActive);
  }

  /**
   * Validate affiliate configuration
   */
  validateConfig(retailer: string): boolean {
    const config = this.getRetailerConfig(retailer);
    return !!(config && config.isActive && config.affiliateId);
  }
}

export const affiliateManager = new AffiliateManager();
