import { prisma } from './prisma'

export async function seedForumCategories() {
  const categories = [
    {
      name: 'General Discussion',
      description: 'General GPU and hardware discussions',
      slug: 'general',
      color: '#3b82f6',
      icon: '💬',
      sortOrder: 1
    },
    {
      name: 'GPU Recommendations',
      description: 'Get help choosing the right GPU for your needs',
      slug: 'recommendations',
      color: '#10b981',
      icon: '🎯',
      sortOrder: 2
    },
    {
      name: 'Build Showcase',
      description: 'Show off your completed builds',
      slug: 'showcase',
      color: '#f59e0b',
      icon: '🏆',
      sortOrder: 3
    },
    {
      name: 'Troubleshooting',
      description: 'Get help with GPU and system issues',
      slug: 'troubleshooting',
      color: '#ef4444',
      icon: '🔧',
      sortOrder: 4
    },
    {
      name: 'News & Reviews',
      description: 'Latest GPU news and community reviews',
      slug: 'news',
      color: '#8b5cf6',
      icon: '📰',
      sortOrder: 5
    },
    {
      name: 'Overclocking',
      description: 'Overclocking guides, results, and discussions',
      slug: 'overclocking',
      color: '#f97316',
      icon: '⚡',
      sortOrder: 6
    }
  ]

  for (const category of categories) {
    await prisma.forumCategory.upsert({
      where: { slug: category.slug },
      update: category,
      create: {
        ...category,
        moderatorIds: [] // Will be populated when moderators are assigned
      }
    })
  }

  console.log('Forum categories seeded successfully')
}

// Run this function to seed the categories
if (require.main === module) {
  seedForumCategories()
    .then(() => {
      console.log('Seeding completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}