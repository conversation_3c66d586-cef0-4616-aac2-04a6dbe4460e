import { PrismaClient } from '@prisma/client';
import { Redis } from 'ioredis';

const prisma = new PrismaClient();
let redis: Redis | null = null;

// Lazy Redis connection
function getRedis(): Redis {
  if (!redis) {
    redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
  }
  return redis;
}

export type Workload = 'gaming' | 'content-creation' | 'ai-ml' | 'mining';

export interface RecommendationCriteria {
  workload: Workload;
  budget: {
    min: number;
    max: number;
  };
  performanceTargets?: {
    resolution?: '1080p' | '1440p' | '4K' | '8K';
    targetFPS?: number;
    rayTracing?: boolean;
    dlss?: boolean;
    vr?: boolean;
  };
  currentSystem?: {
    cpu?: string;
    psu?: number; // Watts
    case?: string;
    motherboard?: string;
  };
  futureProofing?: number; // Years (1-5)
  priorities?: {
    performance?: number; // Weight 0-100
    value?: number; // Weight 0-100
    futureProofing?: number; // Weight 0-100
    powerEfficiency?: number; // Weight 0-100
  };
  preferences?: {
    brand?: 'NVIDIA' | 'AMD' | 'Intel' | 'any';
    newOnly?: boolean;
    maxPowerDraw?: number;
    quietOperation?: boolean;
  };
}

export interface GPURecommendation {
  gpu: {
    id: string;
    name: string;
    manufacturer: string;
    architecture: string;
    specifications: any;
    pricing: any[];
  };
  score: {
    overall: number;
    performance: number;
    value: number;
    futureProofing: number;
    compatibility: number;
  };
  reasoning: string[];
  pros: string[];
  cons: string[];
  bestPrice: {
    price: number;
    retailer: string;
    availability: string;
  };
}

export interface RecommendationResult {
  criteria: RecommendationCriteria;
  recommendations: GPURecommendation[];
  totalFound: number;
  generatedAt: Date;
}

export class RecommendationEngine {
  // Main recommendation method
  async getRecommendations(criteria: RecommendationCriteria): Promise<RecommendationResult> {
    try {
      // Get GPUs within budget range
      const gpus = await this.getGPUsInBudget(criteria.budget);
      
      // Score each GPU based on workload
      const scoredGPUs = await Promise.all(
        gpus.map(gpu => this.scoreGPUForWorkload(gpu, criteria))
      );

      // Sort by overall score and take top recommendations
      const recommendations = scoredGPUs
        .sort((a, b) => b.score.overall - a.score.overall)
        .slice(0, 10); // Top 10 recommendations

      return {
        criteria,
        recommendations,
        totalFound: gpus.length,
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('Error generating recommendations:', error);
      throw new Error('Failed to generate recommendations');
    }
  }

  // Gaming-focused recommendations
  private async scoreForGaming(gpu: any, criteria: RecommendationCriteria): Promise<number> {
    const specs = gpu.specifications;
    let score = 0;

    // Base performance score (0-40 points)
    const memoryGB = specs.memory?.size || 0;
    const baseClock = specs.clocks?.base || 0;
    const boostClock = specs.clocks?.boost || 0;
    const cudaCores = specs.cores?.cuda || specs.cores?.stream || 0;

    // Memory importance for gaming
    if (memoryGB >= 16) score += 15;
    else if (memoryGB >= 12) score += 12;
    else if (memoryGB >= 8) score += 8;
    else if (memoryGB >= 6) score += 5;

    // Clock speeds
    if (boostClock > 2000) score += 10;
    else if (boostClock > 1800) score += 8;
    else if (boostClock > 1600) score += 6;

    // Core count (normalized)
    const coreScore = Math.min(15, (cudaCores / 100));
    score += coreScore;

    // Resolution-specific adjustments
    if (criteria.performanceTargets?.resolution) {
      switch (criteria.performanceTargets.resolution) {
        case '4K':
          if (memoryGB < 12) score -= 10; // Penalize low VRAM for 4K
          break;
        case '1440p':
          if (memoryGB < 8) score -= 5;
          break;
      }
    }

    // Ray tracing capability
    if (criteria.performanceTargets?.rayTracing && specs.features?.rayTracing) {
      score += 10;
    }

    // DLSS/FSR support
    if (criteria.performanceTargets?.dlss && specs.features?.dlss) {
      score += 8;
    }

    return Math.min(100, score);
  }

  // Content creation focused recommendations
  private async scoreForContentCreation(gpu: any, criteria: RecommendationCriteria): Promise<number> {
    const specs = gpu.specifications;
    let score = 0;

    // VRAM is crucial for content creation
    const memoryGB = specs.memory?.size || 0;
    if (memoryGB >= 24) score += 25;
    else if (memoryGB >= 16) score += 20;
    else if (memoryGB >= 12) score += 15;
    else if (memoryGB >= 8) score += 10;

    // Memory bandwidth
    const memoryBandwidth = specs.memory?.bandwidth || 0;
    if (memoryBandwidth > 800) score += 15;
    else if (memoryBandwidth > 600) score += 12;
    else if (memoryBandwidth > 400) score += 8;

    // CUDA cores for rendering
    const cudaCores = specs.cores?.cuda || 0;
    if (cudaCores > 10000) score += 20;
    else if (cudaCores > 7000) score += 15;
    else if (cudaCores > 5000) score += 10;

    // Professional features
    if (specs.features?.nvenc) score += 10; // Hardware encoding
    if (specs.features?.av1Encode) score += 8; // AV1 encoding
    if (specs.features?.nvlink) score += 5; // Multi-GPU support

    // Architecture bonus for newer features
    if (gpu.architecture?.includes('Ada Lovelace') || gpu.architecture?.includes('RDNA 3')) {
      score += 10;
    }

    return Math.min(100, score);
  }

  // AI/ML focused recommendations
  private async scoreForAIML(gpu: any, criteria: RecommendationCriteria): Promise<number> {
    const specs = gpu.specifications;
    let score = 0;

    // VRAM is critical for AI/ML
    const memoryGB = specs.memory?.size || 0;
    if (memoryGB >= 24) score += 30;
    else if (memoryGB >= 16) score += 25;
    else if (memoryGB >= 12) score += 20;
    else if (memoryGB >= 8) score += 10;
    else score -= 10; // Penalize low VRAM heavily

    // Tensor cores are essential
    const tensorCores = specs.cores?.tensor || 0;
    if (tensorCores > 0) {
      score += 25;
      if (tensorCores > 300) score += 10;
    } else {
      score -= 15; // Heavy penalty for no tensor cores
    }

    // Memory bandwidth for large models
    const memoryBandwidth = specs.memory?.bandwidth || 0;
    if (memoryBandwidth > 1000) score += 15;
    else if (memoryBandwidth > 800) score += 12;
    else if (memoryBandwidth > 600) score += 8;

    // CUDA cores
    const cudaCores = specs.cores?.cuda || 0;
    if (cudaCores > 10000) score += 15;
    else if (cudaCores > 7000) score += 10;

    // Professional AI features
    if (specs.features?.tensorRT) score += 10;
    if (specs.features?.nvlink) score += 8; // Multi-GPU training
    if (specs.precision?.fp16) score += 5;
    if (specs.precision?.int8) score += 5;

    return Math.min(100, score);
  }

  // Mining focused recommendations
  private async scoreForMining(gpu: any, criteria: RecommendationCriteria): Promise<number> {
    const specs = gpu.specifications;
    let score = 0;

    // Hash rate (if available in specs)
    const hashRate = specs.mining?.ethash || specs.mining?.kawpow || 0;
    if (hashRate > 100) score += 25;
    else if (hashRate > 80) score += 20;
    else if (hashRate > 60) score += 15;

    // Power efficiency (hash rate per watt)
    const tdp = specs.power?.tdp || 300;
    const efficiency = hashRate / tdp;
    if (efficiency > 0.4) score += 20;
    else if (efficiency > 0.3) score += 15;
    else if (efficiency > 0.2) score += 10;

    // Memory type and size (important for some algorithms)
    const memoryType = specs.memory?.type;
    const memoryGB = specs.memory?.size || 0;
    
    if (memoryType === 'GDDR6X' || memoryType === 'GDDR6') {
      score += 10;
    }
    
    if (memoryGB >= 12) score += 15;
    else if (memoryGB >= 8) score += 10;

    // Lower TDP is better for mining farms
    if (tdp < 200) score += 15;
    else if (tdp < 250) score += 10;
    else if (tdp < 300) score += 5;

    // ROI calculation (simplified)
    const currentPrice = await this.getCurrentGPUPrice(gpu.id);
    if (currentPrice && hashRate > 0) {
      const dailyRevenue = hashRate * 0.05; // Simplified calculation
      const paybackDays = currentPrice / dailyRevenue;
      
      if (paybackDays < 200) score += 15;
      else if (paybackDays < 300) score += 10;
      else if (paybackDays < 400) score += 5;
    }

    return Math.min(100, score);
  }

  // Score GPU for specific workload with enhanced scoring
  private async scoreGPUForWorkload(gpu: any, criteria: RecommendationCriteria): Promise<GPURecommendation> {
    let performanceScore = 0;
    let reasoning: string[] = [];
    let pros: string[] = [];
    let cons: string[] = [];

    // Apply brand preference filtering
    if (criteria.preferences?.brand && criteria.preferences.brand !== 'any') {
      if (gpu.manufacturer.toUpperCase() !== criteria.preferences.brand.toUpperCase()) {
        // Return low score for non-preferred brands
        return this.createLowScoreRecommendation(gpu, 'Brand preference not matched');
      }
    }

    // Apply budget filtering with current pricing
    const currentPrice = await this.getCurrentGPUPrice(gpu.id);
    if (currentPrice > 0 && (currentPrice < criteria.budget.min || currentPrice > criteria.budget.max)) {
      return this.createLowScoreRecommendation(gpu, 'Outside budget range');
    }

    // Get workload-specific performance score
    switch (criteria.workload) {
      case 'gaming':
        performanceScore = await this.scoreForGaming(gpu, criteria);
        reasoning.push('Optimized for gaming performance and frame rates');
        break;
      case 'content-creation':
        performanceScore = await this.scoreForContentCreation(gpu, criteria);
        reasoning.push('Optimized for content creation and professional workloads');
        break;
      case 'ai-ml':
        performanceScore = await this.scoreForAIML(gpu, criteria);
        reasoning.push('Optimized for AI/ML training and inference');
        break;
      case 'mining':
        performanceScore = await this.scoreForMining(gpu, criteria);
        reasoning.push('Optimized for cryptocurrency mining efficiency');
        break;
    }

    // Calculate enhanced scores
    const valueScore = this.calculateEnhancedValueScore(performanceScore, currentPrice, criteria);
    const futureProofingScore = this.calculateEnhancedFutureProofingScore(gpu, criteria);
    const compatibilityScore = await this.calculateCompatibilityScore(gpu, criteria);
    const powerEfficiencyScore = this.calculatePowerEfficiencyScore(gpu, criteria);

    // Apply user priority weights
    const weights = this.normalizeWeights(criteria.priorities);
    const overallScore = (
      performanceScore * weights.performance +
      valueScore * weights.value +
      futureProofingScore * weights.futureProofing +
      powerEfficiencyScore * weights.powerEfficiency +
      compatibilityScore * 0.1
    );

    // Generate enhanced pros and cons
    this.generateEnhancedProsAndCons(gpu, criteria, pros, cons);

    // Get best price
    const bestPrice = await this.getBestPrice(gpu.id);

    return {
      gpu: {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
        architecture: gpu.architecture,
        specifications: gpu.specifications,
        pricing: gpu.pricing || []
      },
      score: {
        overall: Math.round(overallScore),
        performance: Math.round(performanceScore),
        value: Math.round(valueScore),
        futureProofing: Math.round(futureProofingScore),
        compatibility: Math.round(compatibilityScore)
      },
      reasoning,
      pros,
      cons,
      bestPrice
    };
  }

  // Enhanced value scoring with market context
  private calculateEnhancedValueScore(performanceScore: number, price: number, criteria: RecommendationCriteria): number {
    if (!price || price === 0) return 50;
    
    // Base performance per dollar
    const performancePerDollar = performanceScore / (price / 100);
    
    // Adjust based on budget position
    const budgetMidpoint = (criteria.budget.min + criteria.budget.max) / 2;
    const budgetPosition = price / budgetMidpoint; // 1.0 = at midpoint
    
    let valueScore = performancePerDollar * 10;
    
    // Bonus for being in lower half of budget
    if (budgetPosition < 0.8) {
      valueScore += 15;
    } else if (budgetPosition < 1.0) {
      valueScore += 10;
    }
    
    // Penalty for being at top of budget
    if (budgetPosition > 1.2) {
      valueScore -= 10;
    }
    
    return Math.min(100, Math.max(0, valueScore));
  }

  // Enhanced future-proofing with user timeline
  private calculateEnhancedFutureProofingScore(gpu: any, criteria: RecommendationCriteria): number {
    let score = 50;
    const specs = gpu.specifications;
    const memoryGB = specs.memory?.size || 0;
    const architecture = gpu.architecture || '';
    const years = criteria.futureProofing || 3;
    
    // Memory scaling with timeline
    const memoryMultiplier = Math.min(1.5, 1 + (years - 3) * 0.1);
    if (memoryGB >= 24) score += 25 * memoryMultiplier;
    else if (memoryGB >= 16) score += 20 * memoryMultiplier;
    else if (memoryGB >= 12) score += 15 * memoryMultiplier;
    else if (memoryGB >= 8) score += 10 * memoryMultiplier;
    else score -= 15; // Heavy penalty for low VRAM
    
    // Architecture scoring with timeline consideration
    const currentYear = new Date().getFullYear();
    if (architecture.includes('Ada Lovelace') || architecture.includes('RDNA 3')) {
      score += 25; // Latest gen
    } else if (architecture.includes('Ampere') || architecture.includes('RDNA 2')) {
      score += 15 - (years * 2); // Previous gen, penalty increases with timeline
    } else {
      score -= years * 5; // Older architectures penalized more for longer timelines
    }
    
    // Modern feature bonuses
    if (specs.features?.rayTracing) score += 10;
    if (specs.features?.dlss || specs.features?.fsr) score += 10;
    if (specs.features?.av1Encode) score += 8;
    if (specs.features?.meshShaders) score += 5;
    
    return Math.min(100, Math.max(0, score));
  }

  // Power efficiency scoring
  private calculatePowerEfficiencyScore(gpu: any, criteria: RecommendationCriteria): number {
    const specs = gpu.specifications;
    const tdp = specs.power?.tdp || 300;
    let score = 50;
    
    // Base efficiency scoring
    if (tdp < 150) score += 25;
    else if (tdp < 200) score += 20;
    else if (tdp < 250) score += 15;
    else if (tdp < 300) score += 10;
    else if (tdp < 350) score += 5;
    else score -= 10;
    
    // User preference for max power draw
    if (criteria.preferences?.maxPowerDraw) {
      if (tdp <= criteria.preferences.maxPowerDraw) {
        score += 15;
      } else {
        score -= 20; // Heavy penalty for exceeding user limit
      }
    }
    
    // Quiet operation preference
    if (criteria.preferences?.quietOperation) {
      if (tdp < 200) score += 10; // Lower power usually means quieter
      else if (tdp > 300) score -= 15;
    }
    
    return Math.min(100, Math.max(0, score));
  }

  // Enhanced compatibility scoring
  private async calculateCompatibilityScore(gpu: any, criteria: RecommendationCriteria): number {
    let score = 85; // Base compatibility score
    
    const specs = gpu.specifications;
    const tdp = specs.power?.tdp || 300;
    
    // PSU compatibility
    if (criteria.currentSystem?.psu) {
      const requiredPSU = specs.power?.recommendedPSU || tdp * 2;
      if (criteria.currentSystem.psu >= requiredPSU) {
        score += 10;
      } else if (criteria.currentSystem.psu >= requiredPSU * 0.9) {
        score += 5;
      } else {
        score -= 20; // Major compatibility issue
      }
    }
    
    // Physical size considerations (simplified)
    const gpuLength = specs.physical?.dimensions?.length || 280;
    if (gpuLength > 320) score -= 5; // Long cards may have compatibility issues
    
    return Math.min(100, Math.max(0, score));
  }

  // Normalize user priority weights
  private normalizeWeights(priorities?: RecommendationCriteria['priorities']) {
    const defaultWeights = {
      performance: 40,
      value: 30,
      futureProofing: 20,
      powerEfficiency: 10
    };
    
    if (!priorities) return this.convertToDecimal(defaultWeights);
    
    const total = (priorities.performance || 0) + 
                  (priorities.value || 0) + 
                  (priorities.futureProofing || 0) + 
                  (priorities.powerEfficiency || 0);
    
    if (total === 0) return this.convertToDecimal(defaultWeights);
    
    return {
      performance: (priorities.performance || 0) / total,
      value: (priorities.value || 0) / total,
      futureProofing: (priorities.futureProofing || 0) / total,
      powerEfficiency: (priorities.powerEfficiency || 0) / total
    };
  }

  private convertToDecimal(weights: any) {
    const total = Object.values(weights).reduce((sum: number, val: any) => sum + val, 0);
    return {
      performance: weights.performance / total,
      value: weights.value / total,
      futureProofing: weights.futureProofing / total,
      powerEfficiency: weights.powerEfficiency / total
    };
  }

  // Create low score recommendation for filtered out GPUs
  private createLowScoreRecommendation(gpu: any, reason: string): GPURecommendation {
    return {
      gpu: {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
        architecture: gpu.architecture,
        specifications: gpu.specifications,
        pricing: gpu.pricing || []
      },
      score: {
        overall: 0,
        performance: 0,
        value: 0,
        futureProofing: 0,
        compatibility: 0
      },
      reasoning: [reason],
      pros: [],
      cons: [reason],
      bestPrice: {
        price: 0,
        retailer: 'Unknown',
        availability: 'out-of-stock'
      }
    };
  }

  // Enhanced pros and cons generation
  private generateEnhancedProsAndCons(gpu: any, criteria: RecommendationCriteria, pros: string[], cons: string[]) {
    const specs = gpu.specifications;
    const memoryGB = specs.memory?.size || 0;
    const tdp = specs.power?.tdp || 300;
    const architecture = gpu.architecture || '';
    
    // Memory pros/cons
    if (memoryGB >= 20) pros.push('Exceptional VRAM capacity for demanding applications');
    else if (memoryGB >= 16) pros.push('Excellent VRAM for future games and applications');
    else if (memoryGB >= 12) pros.push('Good VRAM for current and near-future requirements');
    else if (memoryGB < 8) cons.push('Limited VRAM may affect performance in modern games');
    
    // Architecture pros/cons
    if (architecture.includes('Ada Lovelace')) {
      pros.push('Latest NVIDIA architecture with cutting-edge features');
    } else if (architecture.includes('RDNA 3')) {
      pros.push('Latest AMD architecture with excellent efficiency');
    } else if (architecture.includes('Ampere') || architecture.includes('RDNA 2')) {
      pros.push('Mature architecture with proven performance');
    }
    
    // Power efficiency
    if (tdp < 200) pros.push('Excellent power efficiency');
    else if (tdp < 250) pros.push('Good power efficiency');
    else if (tdp > 350) cons.push('High power consumption requires robust PSU');
    
    // Feature-specific pros
    if (specs.features?.rayTracing) pros.push('Hardware ray tracing acceleration');
    if (specs.features?.dlss) pros.push('DLSS AI upscaling for better performance');
    if (specs.features?.fsr) pros.push('FSR upscaling support');
    if (specs.features?.av1Encode) pros.push('Modern AV1 encoding support');
    
    // Workload-specific enhanced pros/cons
    this.addWorkloadSpecificProsAndCons(gpu, criteria, pros, cons);
    
    // User preference alignment
    if (criteria.preferences?.quietOperation && tdp < 200) {
      pros.push('Low power draw supports quiet operation');
    }
    
    if (criteria.preferences?.brand && gpu.manufacturer.toUpperCase() === criteria.preferences.brand.toUpperCase()) {
      pros.push(`Matches your ${criteria.preferences.brand} brand preference`);
    }
  }

  private addWorkloadSpecificProsAndCons(gpu: any, criteria: RecommendationCriteria, pros: string[], cons: string[]) {
    const specs = gpu.specifications;
    const memoryGB = specs.memory?.size || 0;
    
    switch (criteria.workload) {
      case 'gaming':
        if (criteria.performanceTargets?.resolution === '4K' && memoryGB >= 16) {
          pros.push('Excellent for 4K gaming with sufficient VRAM');
        } else if (criteria.performanceTargets?.resolution === '4K' && memoryGB < 12) {
          cons.push('May struggle with 4K gaming due to limited VRAM');
        }
        
        if (criteria.performanceTargets?.vr && specs.features?.vrReady) {
          pros.push('VR-ready with optimized performance');
        }
        
        if (criteria.performanceTargets?.targetFPS && criteria.performanceTargets.targetFPS >= 120) {
          if (specs.clocks?.boost > 2000) {
            pros.push('High boost clocks support high refresh rate gaming');
          }
        }
        break;
        
      case 'content-creation':
        if (specs.features?.nvenc) pros.push('Hardware video encoding acceleration');
        if (specs.features?.quickSync) pros.push('Intel Quick Sync support');
        if (memoryGB >= 24) pros.push('Exceptional VRAM for 8K video editing');
        else if (memoryGB < 16) cons.push('Limited VRAM for professional content creation');
        
        if (specs.cores?.cuda > 8000) {
          pros.push('High CUDA core count for rendering acceleration');
        }
        break;
        
      case 'ai-ml':
        if (specs.cores?.tensor) {
          pros.push('Dedicated tensor cores for AI acceleration');
          if (specs.cores.tensor > 400) {
            pros.push('High tensor core count for large model training');
          }
        } else {
          cons.push('No tensor cores - limited AI/ML acceleration');
        }
        
        if (memoryGB >= 24) pros.push('Sufficient VRAM for large language models');
        else if (memoryGB < 16) cons.push('Insufficient VRAM for large AI models');
        
        if (specs.memory?.bandwidth > 1000) {
          pros.push('High memory bandwidth for data-intensive AI workloads');
        }
        break;
        
      case 'mining':
        const efficiency = specs.mining?.efficiency || 0;
        if (efficiency > 0.4) pros.push('Excellent mining efficiency');
        else if (efficiency > 0.3) pros.push('Good mining efficiency');
        else cons.push('Lower mining efficiency may affect profitability');
        
        if (specs.power?.tdp < 250) {
          pros.push('Lower power consumption reduces electricity costs');
        } else {
          cons.push('Higher power consumption increases operating costs');
        }
        break;
    }
  }

  // Helper methods
  private async getGPUsInBudget(budget: { min: number; max: number }) {
    return await prisma.gPU.findMany({
      include: {
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 5
        }
      }
    });
  }

  private async getCurrentGPUPrice(gpuId: string): Promise<number> {
    const pricing = await prisma.gPUPricing.findFirst({
      where: { gpuId },
      orderBy: { scrapedAt: 'desc' }
    });
    return pricing ? pricing.price.toNumber() : 0;
  }

  private calculateValueScore(performanceScore: number, price: number): number {
    if (!price || price === 0) return 50;
    
    // Performance per dollar calculation
    const performancePerDollar = performanceScore / (price / 100);
    
    // Normalize to 0-100 scale
    return Math.min(100, performancePerDollar * 10);
  }

  private calculateFutureProofingScore(gpu: any, years: number): number {
    let score = 50; // Base score
    
    const specs = gpu.specifications;
    const memoryGB = specs.memory?.size || 0;
    const architecture = gpu.architecture || '';
    
    // Memory future-proofing
    if (memoryGB >= 16) score += 20;
    else if (memoryGB >= 12) score += 15;
    else if (memoryGB >= 8) score += 10;
    else score -= 10;
    
    // Architecture age
    if (architecture.includes('Ada Lovelace') || architecture.includes('RDNA 3')) {
      score += 20;
    } else if (architecture.includes('Ampere') || architecture.includes('RDNA 2')) {
      score += 10;
    }
    
    // Modern features
    if (specs.features?.rayTracing) score += 10;
    if (specs.features?.dlss || specs.features?.fsr) score += 10;
    if (specs.features?.av1Encode) score += 5;
    
    return Math.min(100, score);
  }

  private generateProsAndCons(gpu: any, criteria: RecommendationCriteria, pros: string[], cons: string[]) {
    const specs = gpu.specifications;
    const memoryGB = specs.memory?.size || 0;
    
    // Common pros
    if (memoryGB >= 16) pros.push('Excellent VRAM for future games and applications');
    if (specs.features?.rayTracing) pros.push('Hardware ray tracing support');
    if (specs.features?.dlss) pros.push('DLSS support for improved performance');
    
    // Common cons
    if (memoryGB < 8) cons.push('Limited VRAM may affect future performance');
    if (specs.power?.tdp > 300) cons.push('High power consumption');
    
    // Workload-specific pros/cons
    switch (criteria.workload) {
      case 'gaming':
        if (specs.features?.dlss) pros.push('DLSS provides significant FPS boost');
        if (memoryGB < 12 && criteria.performanceTargets?.resolution === '4K') {
          cons.push('May struggle with 4K gaming due to limited VRAM');
        }
        break;
      case 'content-creation':
        if (specs.features?.nvenc) pros.push('Hardware video encoding acceleration');
        if (memoryGB < 16) cons.push('Limited VRAM for professional content creation');
        break;
      case 'ai-ml':
        if (specs.cores?.tensor) pros.push('Dedicated tensor cores for AI acceleration');
        if (memoryGB < 12) cons.push('Insufficient VRAM for large AI models');
        break;
    }
  }

  private async getBestPrice(gpuId: string) {
    const pricing = await prisma.gPUPricing.findFirst({
      where: { gpuId },
      orderBy: { price: 'asc' }
    });
    
    return pricing ? {
      price: pricing.price.toNumber(),
      retailer: pricing.retailer,
      availability: pricing.availability
    } : {
      price: 0,
      retailer: 'Unknown',
      availability: 'out-of-stock'
    };
  }
}

export const recommendationEngine = new RecommendationEngine();

// Static methods for testing compatibility
export class RecommendationEngineStatic {
  /**
   * Get recommendations using static method (for testing)
   */
  static async getRecommendations(gpus: any[], preferences: any) {
    const engine = new RecommendationEngine()
    
    // Transform preferences to match our criteria format
    const criteria: RecommendationCriteria = {
      workload: preferences.workload,
      budget: preferences.budget,
      performanceTargets: preferences.performance ? {
        resolution: preferences.performance.resolution,
        targetFPS: preferences.performance.targetFPS
      } : undefined,
      priorities: preferences.priorities,
      preferences: {
        brand: 'any'
      }
    }

    // Score each GPU
    const recommendations = []
    for (const gpu of gpus) {
      const scored = await engine['scoreGPUForWorkload'](gpu, criteria)
      recommendations.push({
        gpu: scored.gpu,
        score: scored.score,
        reasoning: scored.reasoning.join(' '),
        pros: scored.pros,
        cons: scored.cons
      })
    }

    // Sort by overall score
    return recommendations.sort((a, b) => b.score.overall - a.score.overall)
  }

  /**
   * Calculate gaming score
   */
  static calculateGamingScore(gpu: any, resolution: string, targetFps: number) {
    const engine = new RecommendationEngine()
    const criteria: RecommendationCriteria = {
      workload: 'gaming',
      budget: { min: 0, max: 10000 },
      performanceTargets: { resolution: resolution as any, targetFPS: targetFps }
    }
    
    return engine['scoreForGaming'](gpu, criteria)
  }

  /**
   * Calculate productivity score
   */
  static calculateProductivityScore(gpu: any) {
    const engine = new RecommendationEngine()
    const criteria: RecommendationCriteria = {
      workload: 'content-creation',
      budget: { min: 0, max: 10000 }
    }
    
    return engine['scoreForContentCreation'](gpu, criteria)
  }

  /**
   * Calculate value score
   */
  static calculateValueScore(gpu: any, workload: string, resolution: string) {
    const engine = new RecommendationEngine()
    const price = gpu.originalMsrp || 1000
    
    let performanceScore = 75 // Default
    if (workload === 'gaming') {
      const criteria: RecommendationCriteria = {
        workload: 'gaming',
        budget: { min: 0, max: 10000 },
        performanceTargets: { resolution: resolution as any }
      }
      performanceScore = engine['scoreForGaming'](gpu, criteria) as any
    }
    
    return engine['calculateValueScore'](performanceScore, price)
  }

  /**
   * Generate recommendation reasoning
   */
  static generateRecommendationReasoning(gpu: any, scores: any, preferences: any) {
    const pros: string[] = []
    const cons: string[] = []
    
    // Generate basic reasoning
    let reasoning = `This ${gpu.manufacturer} ${gpu.name} scores ${scores.overall}/100 overall for ${preferences.workload} workloads.`
    
    // Add performance reasoning
    if (scores.performance > 85) {
      reasoning += ' Excellent performance for demanding applications.'
      pros.push('Excellent gaming performance')
    } else if (scores.performance > 70) {
      reasoning += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}ng += ' Good performance for most applications.'
      pros.push('Good gaming performance')
    } else {
      reasoning += ' Adequate performance for basic applications.'
    }
    
    return {
      reasoning,
      pros,
      cons
    }
  }
}

// Export the static class for testing compatibility
// Note: The main RecommendationEngine class is exported above
          aiMl: 0.3,
          vramWeight: 0.6,
          tensorWeight: 0.2
        }
      case 'ai-ml':
        return {
          gaming: 0.2,
          productivity: 0.4,
          aiMl: 1.0,
          vramWeight: 0.4,
          tensorWeight: 0.8
        }
      case 'mining':
        return {
          gaming: 0.1,
          productivity: 0.1,
          aiMl: 0.1,
          efficiency: 0.8,
          hashRate: 0.6,
          vramWeight: 0.3,
          tensorWeight: 0.0
        }
      default:
        return {
          gaming: 0.5,
          productivity: 0.5,
          aiMl: 0.3,
          vramWeight: 0.3,
          tensorWeight: 0.2
        }
    }
  }
}

// Export the static class for testing compatibility
// Note: The main RecommendationEngine class is exported above