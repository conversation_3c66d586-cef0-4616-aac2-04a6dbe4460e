import { PrismaClient } from '@prisma/client';
import { Redis } from 'ioredis';

const prisma = new PrismaClient();
let redis: Redis | null = null;

// Lazy Redis connection
function getRedis(): Redis {
  if (!redis) {
    redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
  }
  return redis;
}

export type Workload = 'gaming' | 'content-creation' | 'ai-ml' | 'mining';

export interface RecommendationCriteria {
  workload: Workload;
  budget: {
    min: number;
    max: number;
  };
  performanceTargets?: {
    resolution?: '1080p' | '1440p' | '4K' | '8K';
    targetFPS?: number;
    rayTracing?: boolean;
    dlss?: boolean;
    vr?: boolean;
  };
  currentSystem?: {
    cpu?: string;
    psu?: number; // Watts
    case?: string;
    motherboard?: string;
  };
}

export interface GPURecommendation {
  gpu: {
    id: string;
    name: string;
    manufacturer: string;
    architecture: string;
    specifications: any;
    pricing: any[];
  };
  score: {
    overall: number;
    performance: number;
    value: number;
    futureProofing: number;
    compatibility: number;
  };
  reasoning: string[];
  pros: string[];
  cons: string[];
  bestPrice: {
    price: number;
    retailer: string;
    availability: string;
  };
}

export interface RecommendationResult {
  criteria: RecommendationCriteria;
  recommendations: GPURecommendation[];
  totalFound: number;
  generatedAt: Date;
}

export class SimpleRecommendationEngine {
  // Main recommendation method
  async getRecommendations(criteria: RecommendationCriteria): Promise<RecommendationResult> {
    try {
      // Get GPUs within budget range
      const gpus = await this.getGPUsInBudget(criteria.budget);
      
      // Score each GPU based on workload
      const scoredGPUs = await Promise.all(
        gpus.map(gpu => this.scoreGPUForWorkload(gpu, criteria))
      );

      // Sort by overall score and take top recommendations
      const recommendations = scoredGPUs
        .sort((a, b) => b.score.overall - a.score.overall)
        .slice(0, 10); // Top 10 recommendations

      return {
        criteria,
        recommendations,
        totalFound: gpus.length,
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('Error generating recommendations:', error);
      throw new Error('Failed to generate recommendations');
    }
  }

  // Helper methods
  private async getGPUsInBudget(budget: { min: number; max: number }) {
    return await prisma.gPU.findMany({
      include: {
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 5
        }
      }
    });
  }

  private async scoreGPUForWorkload(gpu: any, criteria: RecommendationCriteria): Promise<GPURecommendation> {
    // Simple scoring logic
    const performanceScore = this.calculatePerformanceScore(gpu, criteria);
    const valueScore = this.calculateValueScore(gpu);
    const futureProofingScore = this.calculateFutureProofingScore(gpu);
    const compatibilityScore = 85; // Default compatibility score

    const overallScore = (performanceScore + valueScore + futureProofingScore + compatibilityScore) / 4;

    return {
      gpu: {
        id: gpu.id,
        name: gpu.name,
        manufacturer: gpu.manufacturer,
        architecture: gpu.architecture,
        specifications: gpu.specifications,
        pricing: gpu.pricing || []
      },
      score: {
        overall: Math.round(overallScore),
        performance: Math.round(performanceScore),
        value: Math.round(valueScore),
        futureProofing: Math.round(futureProofingScore),
        compatibility: Math.round(compatibilityScore)
      },
      reasoning: [`Optimized for ${criteria.workload} workloads`],
      pros: ['Good performance', 'Reasonable price'],
      cons: [],
      bestPrice: {
        price: gpu.originalMsrp || 0,
        retailer: 'Unknown',
        availability: 'in-stock'
      }
    };
  }

  private calculatePerformanceScore(gpu: any, criteria: RecommendationCriteria): number {
    // Simple performance scoring based on workload
    const specs = gpu.specifications || {};
    let score = 50; // Base score

    switch (criteria.workload) {
      case 'gaming':
        if (specs.memory?.size >= 12) score += 20;
        if (specs.clocks?.boost > 2000) score += 15;
        break;
      case 'content-creation':
        if (specs.memory?.size >= 16) score += 25;
        if (specs.cores?.cuda > 5000) score += 15;
        break;
      case 'ai-ml':
        if (specs.memory?.size >= 24) score += 30;
        if (specs.cores?.tensor > 0) score += 20;
        break;
      case 'mining':
        if (specs.memory?.type === 'GDDR6X') score += 15;
        if (specs.power?.tdp < 250) score += 10;
        break;
    }

    return Math.min(100, score);
  }

  private calculateValueScore(gpu: any): number {
    // Simple value calculation
    const price = gpu.originalMsrp || 1000;
    if (price < 300) return 90;
    if (price < 600) return 75;
    if (price < 1000) return 60;
    return 40;
  }

  private calculateFutureProofingScore(gpu: any): number {
    // Simple future-proofing calculation
    const specs = gpu.specifications || {};
    let score = 50;

    if (specs.memory?.size >= 16) score += 20;
    if (specs.architecture?.includes('Ada Lovelace') || specs.architecture?.includes('RDNA 3')) score += 15;
    if (specs.features?.rayTracing) score += 10;

    return Math.min(100, score);
  }
}

export const simpleRecommendationEngine = new SimpleRecommendationEngine();