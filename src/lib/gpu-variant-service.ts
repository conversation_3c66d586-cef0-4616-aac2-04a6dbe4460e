import { prisma } from '@/lib/prisma'
import { BaseGPU, GPUVariant, GPUSpecifications } from '@/types/gpu'

export class GPUVariantService {
  
  // Create a base GPU (reference design)
  static async createBaseGPU(data: Omit<BaseGPU, 'id' | 'createdAt' | 'updatedAt' | 'variants'>): Promise<BaseGPU> {
    const baseGpu = await prisma.baseGPU.create({
      data: {
        name: data.name,
        chipManufacturer: data.chipManufacturer,
        architecture: data.architecture,
        launchDate: data.launchDate,
        originalMsrp: data.originalMsrp,
        specifications: data.specifications as any,
        powerRequirements: data.powerRequirements as any,
      },
      include: {
        variants: true
      }
    })
    
    return baseGpu as BaseGPU
  }
  
  // Create a GPU variant (board partner implementation)
  static async createGPUVariant(data: Omit<GPUVariant, 'id' | 'createdAt' | 'updatedAt' | 'baseGpu' | 'pricing'>): Promise<GPUVariant> {
    const variant = await prisma.gPUVariant.create({
      data: {
        baseGpuId: data.baseGpuId,
        boardPartner: data.boardPartner,
        modelName: data.modelName,
        sku: data.sku,
        customSpecs: data.customSpecs as any,
        physicalSpecs: data.physicalSpecs as any,
        powerRequirements: data.powerRequirements as any,
        features: data.features as any,
        warranty: data.warranty,
      },
      include: {
        baseGpu: true,
        pricing: true
      }
    })
    
    return variant as GPUVariant
  }
  
  // Get all base GPUs with their variants
  static async getAllBaseGPUsWithVariants(): Promise<BaseGPU[]> {
    const baseGpus = await prisma.baseGPU.findMany({
      include: {
        variants: {
          include: {
            pricing: {
              orderBy: { scrapedAt: 'desc' },
              take: 1 // Latest price only
            }
          }
        }
      },
      orderBy: { launchDate: 'desc' }
    })
    
    return baseGpus as BaseGPU[]
  }
  
  // Get a specific base GPU with all its variants
  static async getBaseGPUWithVariants(baseGpuId: string): Promise<BaseGPU | null> {
    const baseGpu = await prisma.baseGPU.findUnique({
      where: { id: baseGpuId },
      include: {
        variants: {
          include: {
            pricing: {
              orderBy: { scrapedAt: 'desc' },
              take: 5 // Latest 5 prices per variant
            }
          }
        }
      }
    })
    
    return baseGpu as BaseGPU | null
  }
  
  // Search for GPUs (both base and variants)
  static async searchGPUs(query: string, filters?: {
    chipManufacturer?: string
    boardPartner?: string
    priceRange?: { min: number; max: number }
  }): Promise<{ baseGpus: BaseGPU[]; variants: GPUVariant[] }> {
    
    const baseGpuWhere = {
      OR: [
        { name: { contains: query, mode: 'insensitive' as const } },
        { architecture: { contains: query, mode: 'insensitive' as const } }
      ],
      ...(filters?.chipManufacturer && { chipManufacturer: filters.chipManufacturer })
    }
    
    const variantWhere = {
      OR: [
        { modelName: { contains: query, mode: 'insensitive' as const } },
        { boardPartner: { contains: query, mode: 'insensitive' as const } },
        { baseGpu: { name: { contains: query, mode: 'insensitive' as const } } }
      ],
      ...(filters?.boardPartner && { boardPartner: filters.boardPartner })
    }
    
    const [baseGpus, variants] = await Promise.all([
      prisma.baseGPU.findMany({
        where: baseGpuWhere,
        include: { variants: true },
        take: 20
      }),
      prisma.gPUVariant.findMany({
        where: variantWhere,
        include: { 
          baseGpu: true,
          pricing: {
            orderBy: { scrapedAt: 'desc' },
            take: 1
          }
        },
        take: 20
      })
    ])
    
    return {
      baseGpus: baseGpus as BaseGPU[],
      variants: variants as GPUVariant[]
    }
  }
  
  // Get variants for a specific base GPU
  static async getVariantsForBaseGPU(baseGpuId: string): Promise<GPUVariant[]> {
    const variants = await prisma.gPUVariant.findMany({
      where: { baseGpuId },
      include: {
        baseGpu: true,
        pricing: {
          orderBy: { scrapedAt: 'desc' },
          take: 1
        }
      },
      orderBy: { modelName: 'asc' }
    })
    
    return variants as GPUVariant[]
  }
  
  // Migrate existing GPU to hierarchical structure
  static async migrateGPUToHierarchical(legacyGpuId: string): Promise<{ baseGpu: BaseGPU; variant: GPUVariant }> {
    const legacyGpu = await prisma.gPU.findUnique({
      where: { id: legacyGpuId },
      include: { pricing: true }
    })
    
    if (!legacyGpu) {
      throw new Error('Legacy GPU not found')
    }
    
    // Create base GPU from legacy data
    const baseGpu = await this.createBaseGPU({
      name: legacyGpu.name,
      chipManufacturer: legacyGpu.manufacturer,
      architecture: legacyGpu.architecture,
      launchDate: legacyGpu.launchDate,
      originalMsrp: legacyGpu.originalMsrp ? Number(legacyGpu.originalMsrp) : undefined,
      specifications: legacyGpu.specifications as GPUSpecifications,
      powerRequirements: legacyGpu.powerRequirements as any
    })
    
    // Create reference variant
    const variant = await this.createGPUVariant({
      baseGpuId: baseGpu.id,
      boardPartner: legacyGpu.manufacturer, // Use chip manufacturer as board partner for reference
      modelName: `${legacyGpu.name} (Reference)`,
      physicalSpecs: legacyGpu.physicalSpecs as any,
      powerRequirements: legacyGpu.powerRequirements as any
    })
    
    // Migrate pricing data
    if (legacyGpu.pricing.length > 0) {
      await prisma.gPUPricing.updateMany({
        where: { gpuId: legacyGpuId },
        data: { 
          variantId: variant.id,
          gpuId: null // Remove legacy reference
        }
      })
    }
    
    // Mark legacy GPU as migrated
    await prisma.gPU.update({
      where: { id: legacyGpuId },
      data: { migrated: true }
    })
    
    return { baseGpu, variant }
  }
  
  // Get popular board partners
  static async getBoardPartners(): Promise<string[]> {
    const partners = await prisma.gPUVariant.groupBy({
      by: ['boardPartner'],
      _count: { boardPartner: true },
      orderBy: { _count: { boardPartner: 'desc' } }
    })
    
    return partners.map(p => p.boardPartner)
  }
}
