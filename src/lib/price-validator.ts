import { prisma } from './prisma';

export interface PriceAnomaly {
  gpuId: string;
  retailer: string;
  price: number;
  average: number;
  deviation: number;
  severity: 'low' | 'medium' | 'high';
  reason: string;
}

export interface ValidationResult {
  isValid: boolean;
  reason?: string;
  confidence: number; // 0-1 scale
}

export class PriceValidator {
  private readonly MAX_PRICE_CHANGE_PERCENT = 50; // Flag changes > 50%
  private readonly MIN_PRICE_THRESHOLD = 50; // Minimum reasonable GPU price
  private readonly MAX_PRICE_THRESHOLD = 5000; // Maximum reasonable GPU price
  private readonly ANOMALY_THRESHOLD_MULTIPLIER = 2.5; // Standard deviations for anomaly detection

  /**
   * Validate a single price entry
   */
  async validatePrice(gpuId: string, retailer: string, price: number): Promise<ValidationResult> {
    // Basic range validation
    if (price < this.MIN_PRICE_THRESHOLD) {
      return {
        isValid: false,
        reason: `Price too low: $${price} (minimum: $${this.MIN_PRICE_THRESHOLD})`,
        confidence: 0.9
      };
    }

    if (price > this.MAX_PRICE_THRESHOLD) {
      return {
        isValid: false,
        reason: `Price too high: $${price} (maximum: $${this.MAX_PRICE_THRESHOLD})`,
        confidence: 0.9
      };
    }

    // Historical price validation
    const historicalValidation = await this.validateAgainstHistory(gpuId, retailer, price);
    if (!historicalValidation.isValid) {
      return historicalValidation;
    }

    // Market comparison validation
    const marketValidation = await this.validateAgainstMarket(gpuId, price);
    if (!marketValidation.isValid) {
      return marketValidation;
    }

    return {
      isValid: true,
      confidence: Math.min(historicalValidation.confidence, marketValidation.confidence)
    };
  }

  /**
   * Validate price against historical data for the same GPU and retailer
   */
  private async validateAgainstHistory(gpuId: string, retailer: string, price: number): Promise<ValidationResult> {
    try {
      // Get recent prices from the same retailer
      const recentPrices = await prisma.gPUPricing.findMany({
        where: {
          gpuId,
          retailer,
          scrapedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        orderBy: { scrapedAt: 'desc' },
        take: 20
      });

      if (recentPrices.length === 0) {
        return { isValid: true, confidence: 0.5 }; // No history, assume valid but low confidence
      }

      const prices = recentPrices.map(p => parseFloat(p.price.toString()));
      const average = prices.reduce((sum, p) => sum + p, 0) / prices.length;
      const percentChange = Math.abs((price - average) / average) * 100;

      if (percentChange > this.MAX_PRICE_CHANGE_PERCENT) {
        return {
          isValid: false,
          reason: `Price change too large: ${percentChange.toFixed(1)}% from recent average of $${average.toFixed(2)}`,
          confidence: 0.8
        };
      }

      return {
        isValid: true,
        confidence: Math.max(0.6, 1 - (percentChange / this.MAX_PRICE_CHANGE_PERCENT) * 0.4)
      };

    } catch (error) {
      console.error('Error validating against history:', error);
      return { isValid: true, confidence: 0.3 }; // Assume valid but very low confidence on error
    }
  }

  /**
   * Validate price against current market prices from other retailers
   */
  private async validateAgainstMarket(gpuId: string, price: number): Promise<ValidationResult> {
    try {
      // Get recent prices from all retailers for this GPU
      const marketPrices = await prisma.gPUPricing.findMany({
        where: {
          gpuId,
          scrapedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        },
        orderBy: { scrapedAt: 'desc' },
        take: 50
      });

      if (marketPrices.length < 3) {
        return { isValid: true, confidence: 0.5 }; // Not enough market data
      }

      const prices = marketPrices.map(p => parseFloat(p.price.toString()));
      const average = prices.reduce((sum, p) => sum + p, 0) / prices.length;
      const stdDev = Math.sqrt(prices.reduce((sum, p) => sum + Math.pow(p - average, 2), 0) / prices.length);
      
      const deviationFromMean = Math.abs(price - average);
      const standardDeviations = stdDev > 0 ? deviationFromMean / stdDev : 0;

      if (standardDeviations > this.ANOMALY_THRESHOLD_MULTIPLIER) {
        return {
          isValid: false,
          reason: `Price deviates ${standardDeviations.toFixed(1)} standard deviations from market average of $${average.toFixed(2)}`,
          confidence: 0.85
        };
      }

      return {
        isValid: true,
        confidence: Math.max(0.7, 1 - (standardDeviations / this.ANOMALY_THRESHOLD_MULTIPLIER) * 0.3)
      };

    } catch (error) {
      console.error('Error validating against market:', error);
      return { isValid: true, confidence: 0.3 };
    }
  }

  /**
   * Detect price anomalies across all GPUs
   */
  async detectPriceAnomalies(): Promise<PriceAnomaly[]> {
    const anomalies: PriceAnomaly[] = [];

    try {
      // Get all recent pricing data grouped by GPU and retailer
      const recentPrices = await prisma.gPUPricing.findMany({
        where: {
          scrapedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        },
        orderBy: { scrapedAt: 'desc' }
      });

      // Group prices by GPU and retailer
      const priceGroups = new Map<string, typeof recentPrices>();
      recentPrices.forEach(price => {
        const key = `${price.gpuId}-${price.retailer}`;
        if (!priceGroups.has(key)) {
          priceGroups.set(key, []);
        }
        priceGroups.get(key)!.push(price);
      });

      // Analyze each group for anomalies
      for (const [key, prices] of priceGroups) {
        if (prices.length < 3) continue; // Need at least 3 data points

        const [gpuId, retailer] = key.split('-');
        const priceValues = prices.map(p => parseFloat(p.price.toString()));
        const average = priceValues.reduce((sum, price) => sum + price, 0) / priceValues.length;
        const stdDev = Math.sqrt(priceValues.reduce((sum, price) => sum + Math.pow(price - average, 2), 0) / priceValues.length);

        // Check each price for anomalies
        prices.forEach(price => {
          const priceValue = parseFloat(price.price.toString());
          const deviationFromMean = Math.abs(priceValue - average);
          const standardDeviations = stdDev > 0 ? deviationFromMean / stdDev : 0;

          if (standardDeviations > this.ANOMALY_THRESHOLD_MULTIPLIER) {
            let severity: 'low' | 'medium' | 'high' = 'low';
            let reason = `Price deviates ${standardDeviations.toFixed(1)} standard deviations from average`;

            if (standardDeviations > 4) {
              severity = 'high';
              reason += ' (extreme outlier)';
            } else if (standardDeviations > 3) {
              severity = 'medium';
              reason += ' (significant outlier)';
            }

            anomalies.push({
              gpuId,
              retailer,
              price: priceValue,
              average,
              deviation: deviationFromMean,
              severity,
              reason
            });
          }
        });
      }

    } catch (error) {
      console.error('Error detecting price anomalies:', error);
    }

    return anomalies.sort((a, b) => b.deviation - a.deviation); // Sort by deviation (highest first)
  }

  /**
   * Clean up stale price data
   */
  async cleanupStaleData(): Promise<number> {
    try {
      const staleThreshold = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days old
      
      const result = await prisma.gPUPricing.deleteMany({
        where: {
          scrapedAt: {
            lt: staleThreshold
          }
        }
      });

      console.log(`Cleaned up ${result.count} stale price records`);
      return result.count;

    } catch (error) {
      console.error('Error cleaning up stale data:', error);
      return 0;
    }
  }

  /**
   * Get price statistics for a GPU
   */
  async getPriceStatistics(gpuId: string, days: number = 30): Promise<{
    average: number;
    median: number;
    min: number;
    max: number;
    stdDev: number;
    count: number;
  } | null> {
    try {
      const prices = await prisma.gPUPricing.findMany({
        where: {
          gpuId,
          scrapedAt: {
            gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
          }
        },
        select: { price: true }
      });

      if (prices.length === 0) return null;

      const priceValues = prices.map(p => parseFloat(p.price.toString())).sort((a, b) => a - b);
      const sum = priceValues.reduce((acc, price) => acc + price, 0);
      const average = sum / priceValues.length;
      const median = priceValues[Math.floor(priceValues.length / 2)];
      const min = priceValues[0];
      const max = priceValues[priceValues.length - 1];
      const stdDev = Math.sqrt(priceValues.reduce((sum, price) => sum + Math.pow(price - average, 2), 0) / priceValues.length);

      return {
        average,
        median,
        min,
        max,
        stdDev,
        count: priceValues.length
      };

    } catch (error) {
      console.error('Error getting price statistics:', error);
      return null;
    }
  }
}

export const priceValidator = new PriceValidator();
