import { prisma } from './prisma'

export interface OverclockingAnalysis {
  gpuId: string
  overclockingPotential: number // 1-5 rating
  thermalPerformance: number // 1-5 rating
  powerEfficiency: number // 1-5 rating
  stabilityScore: number // 1-5 rating
  averagePerformanceGain: number
  recommendedSettings: {
    coreClockOffset: number
    memoryClockOffset: number
    powerLimit: number
  }
  thermalData: {
    averageStockTemp: number
    averageOCTemp: number
    thermalHeadroom: number
  }
  powerData: {
    averageStockPower: number
    averageOCPower: number
    powerIncrease: number
  }
}

export interface ThermalProfile {
  gpuId: string
  coolingType: string
  temperatureData: Array<{
    setting: string
    temperature: number
    power: number
    performance: number
  }>
  thermalThrottlingRisk: 'low' | 'medium' | 'high'
  recommendedCooling: string[]
}

export class OverclockingService {
  /**
   * Get comprehensive overclocking analysis for a GPU
   */
  async getOverclockingAnalysis(gpuId: string): Promise<OverclockingAnalysis | null> {
    try {
      const overclockingData = await prisma.overclockingData.findMany({
        where: { gpuId },
        include: {
          gpu: {
            select: {
              name: true,
              manufacturer: true,
              architecture: true
            }
          }
        }
      })

      if (overclockingData.length === 0) {
        return null
      }

      // Calculate averages and ratings
      const validPerformanceGains = overclockingData
        .filter(d => d.performanceGain !== null)
        .map(d => parseFloat(d.performanceGain!.toString()))

      const validStabilityRatings = overclockingData
        .filter(d => d.stabilityRating !== null)
        .map(d => d.stabilityRating!)

      const validStockTemps = overclockingData
        .filter(d => d.stockTemperature !== null)
        .map(d => d.stockTemperature!)

      const validOCTemps = overclockingData
        .filter(d => d.ocTemperature !== null)
        .map(d => d.ocTemperature!)

      const validStockPower = overclockingData
        .filter(d => d.stockPower !== null)
        .map(d => d.stockPower!)

      const validOCPower = overclockingData
        .filter(d => d.ocPower !== null)
        .map(d => d.ocPower!)

      const averagePerformanceGain = validPerformanceGains.length > 0
        ? validPerformanceGains.reduce((sum, gain) => sum + gain, 0) / validPerformanceGains.length
        : 0

      const averageStabilityRating = validStabilityRatings.length > 0
        ? validStabilityRatings.reduce((sum, rating) => sum + rating, 0) / validStabilityRatings.length
        : 0

      const averageStockTemp = validStockTemps.length > 0
        ? validStockTemps.reduce((sum, temp) => sum + temp, 0) / validStockTemps.length
        : 0

      const averageOCTemp = validOCTemps.length > 0
        ? validOCTemps.reduce((sum, temp) => sum + temp, 0) / validOCTemps.length
        : 0

      const averageStockPower = validStockPower.length > 0
        ? validStockPower.reduce((sum, power) => sum + power, 0) / validStockPower.length
        : 0

      const averageOCPower = validOCPower.length > 0
        ? validOCPower.reduce((sum, power) => sum + power, 0) / validOCPower.length
        : 0

      // Calculate recommended settings (median of stable overclocks)
      const stableOverclocks = overclockingData.filter(d => 
        d.stabilityRating && d.stabilityRating >= 4
      )

      const recommendedCoreOffset = this.calculateMedian(
        stableOverclocks
          .filter(d => d.coreClockOffset !== null)
          .map(d => d.coreClockOffset!)
      )

      const recommendedMemoryOffset = this.calculateMedian(
        stableOverclocks
          .filter(d => d.memoryClockOffset !== null)
          .map(d => d.memoryClockOffset!)
      )

      const recommendedPowerLimit = this.calculateMedian(
        stableOverclocks
          .filter(d => d.powerLimit !== null)
          .map(d => d.powerLimit!)
      )

      return {
        gpuId,
        overclockingPotential: this.calculateOverclockingPotential(averagePerformanceGain, averageStabilityRating),
        thermalPerformance: this.calculateThermalPerformance(averageStockTemp, averageOCTemp),
        powerEfficiency: this.calculatePowerEfficiency(averagePerformanceGain, averageStockPower, averageOCPower),
        stabilityScore: Math.round(averageStabilityRating),
        averagePerformanceGain,
        recommendedSettings: {
          coreClockOffset: recommendedCoreOffset,
          memoryClockOffset: recommendedMemoryOffset,
          powerLimit: recommendedPowerLimit
        },
        thermalData: {
          averageStockTemp,
          averageOCTemp,
          thermalHeadroom: Math.max(0, 83 - averageOCTemp) // Assuming 83°C as safe limit
        },
        powerData: {
          averageStockPower,
          averageOCPower,
          powerIncrease: averageOCPower - averageStockPower
        }
      }
    } catch (error) {
      console.error('Error getting overclocking analysis:', error)
      throw error
    }
  }

  /**
   * Get thermal profiles for different cooling solutions
   */
  async getThermalProfiles(gpuId: string): Promise<ThermalProfile[]> {
    try {
      const overclockingData = await prisma.overclockingData.findMany({
        where: { gpuId },
        orderBy: { coolingType: 'asc' }
      })

      // Group by cooling type
      const coolingGroups = overclockingData.reduce((groups, data) => {
        const coolingType = data.coolingType || 'Unknown'
        if (!groups[coolingType]) {
          groups[coolingType] = []
        }
        groups[coolingType].push(data)
        return groups
      }, {} as Record<string, typeof overclockingData>)

      return Object.entries(coolingGroups).map(([coolingType, data]) => {
        const temperatureData = data
          .filter(d => d.stockTemperature && d.ocTemperature)
          .map(d => ({
            setting: `+${d.coreClockOffset || 0}MHz Core`,
            temperature: d.ocTemperature!,
            power: d.ocPower || 0,
            performance: parseFloat(d.performanceGain?.toString() || '0')
          }))

        const maxTemp = Math.max(...temperatureData.map(d => d.temperature))
        const thermalThrottlingRisk = maxTemp > 85 ? 'high' : maxTemp > 80 ? 'medium' : 'low'

        const recommendedCooling = this.getRecommendedCooling(maxTemp, coolingType)

        return {
          gpuId,
          coolingType,
          temperatureData,
          thermalThrottlingRisk,
          recommendedCooling
        }
      })
    } catch (error) {
      console.error('Error getting thermal profiles:', error)
      throw error
    }
  }

  /**
   * Get overclocking statistics for dashboard
   */
  async getOverclockingStatistics(): Promise<any> {
    try {
      const [totalEntries, avgPerformanceGain, coolingDistribution, stabilityDistribution] = await Promise.all([
        prisma.overclockingData.count(),
        prisma.overclockingData.aggregate({
          _avg: { performanceGain: true }
        }),
        prisma.overclockingData.groupBy({
          by: ['coolingType'],
          _count: { id: true }
        }),
        prisma.overclockingData.groupBy({
          by: ['stabilityRating'],
          _count: { id: true }
        })
      ])

      return {
        totalEntries,
        averagePerformanceGain: avgPerformanceGain._avg.performanceGain || 0,
        coolingDistribution: coolingDistribution.reduce((acc, item) => {
          acc[item.coolingType || 'Unknown'] = item._count.id
          return acc
        }, {} as Record<string, number>),
        stabilityDistribution: stabilityDistribution.reduce((acc, item) => {
          acc[`${item.stabilityRating || 0} stars`] = item._count.id
          return acc
        }, {} as Record<string, number>)
      }
    } catch (error) {
      console.error('Error getting overclocking statistics:', error)
      throw error
    }
  }

  /**
   * Calculate overclocking potential rating (1-5)
   */
  private calculateOverclockingPotential(avgPerformanceGain: number, avgStabilityRating: number): number {
    // Weight performance gain and stability
    const performanceScore = Math.min(5, avgPerformanceGain / 5) // 25% gain = 5 stars
    const stabilityScore = avgStabilityRating
    
    return Math.round((performanceScore + stabilityScore) / 2)
  }

  /**
   * Calculate thermal performance rating (1-5)
   */
  private calculateThermalPerformance(avgStockTemp: number, avgOCTemp: number): number {
    if (avgOCTemp === 0) return 3 // No data

    const tempIncrease = avgOCTemp - avgStockTemp
    
    if (tempIncrease <= 5) return 5 // Excellent
    if (tempIncrease <= 10) return 4 // Good
    if (tempIncrease <= 15) return 3 // Fair
    if (tempIncrease <= 20) return 2 // Poor
    return 1 // Very poor
  }

  /**
   * Calculate power efficiency rating (1-5)
   */
  private calculatePowerEfficiency(avgPerformanceGain: number, avgStockPower: number, avgOCPower: number): number {
    if (avgStockPower === 0 || avgOCPower === 0) return 3 // No data

    const powerIncrease = ((avgOCPower - avgStockPower) / avgStockPower) * 100
    const efficiencyRatio = avgPerformanceGain / powerIncrease

    if (efficiencyRatio >= 1) return 5 // Performance gain >= power increase
    if (efficiencyRatio >= 0.8) return 4
    if (efficiencyRatio >= 0.6) return 3
    if (efficiencyRatio >= 0.4) return 2
    return 1
  }

  /**
   * Calculate median value from array
   */
  private calculateMedian(values: number[]): number {
    if (values.length === 0) return 0
    
    const sorted = values.sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid]
  }

  /**
   * Get recommended cooling solutions based on temperature
   */
  private getRecommendedCooling(maxTemp: number, currentCooling: string): string[] {
    const recommendations: string[] = []

    if (maxTemp > 85) {
      recommendations.push('Custom Loop Water Cooling')
      recommendations.push('High-End AIO (280mm+)')
    } else if (maxTemp > 80) {
      recommendations.push('AIO Water Cooling (240mm+)')
      recommendations.push('High-End Air Cooling')
    } else if (maxTemp > 75) {
      recommendations.push('Mid-Range Air Cooling')
      recommendations.push('AIO Water Cooling (120mm+)')
    } else {
      recommendations.push('Stock Cooling Adequate')
      recommendations.push('Basic Air Cooling')
    }

    // Filter out current cooling if it's already adequate
    return recommendations.filter(rec => 
      !rec.toLowerCase().includes(currentCooling.toLowerCase())
    )
  }
}

export const overclockingService = new OverclockingService()