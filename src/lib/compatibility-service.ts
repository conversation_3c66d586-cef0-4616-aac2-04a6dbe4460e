import { CompatibilityResult, SystemSpecs } from '@/types/gpu'

export class CompatibilityService {
  /**
   * Main compatibility checking function
   */
  static async checkCompatibility(gpu: any, system: SystemSpecs): Promise<CompatibilityResult> {
    const powerCompatibility = this.validatePowerSupply(gpu, system.psu)
    const physicalCompatibility = this.checkPhysicalFit(gpu, system.case)
    const bottleneckAnalysis = this.analyzeCPUBottleneck(gpu, system.cpu)

    // Determine overall compatibility
    const compatibilityStatuses = [
      powerCompatibility.status,
      physicalCompatibility.status,
    ]

    let overall: 'compatible' | 'warning' | 'incompatible' = 'compatible'
    
    if (compatibilityStatuses.includes('incompatible')) {
      overall = 'incompatible'
    } else if (compatibilityStatuses.includes('warning')) {
      overall = 'warning'
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(
      powerCompatibility,
      physicalCompatibility,
      bottleneckAnalysis
    )

    return {
      overall,
      power: powerCompatibility,
      physical: physicalCompatibility,
      bottleneck: bottleneckAnalysis,
      recommendations,
    }
  }

  /**
   * Validate power supply compatibility
   */
  static validatePowerSupply(gpu: any, psu: SystemSpecs['psu']) {
    const gpuSpecs = gpu.specifications as any
    const powerReqs = gpu.powerRequirements as any

    const requiredWattage = powerReqs?.tdp || 0
    const recommendedPSU = powerReqs?.recommendedPSU || requiredWattage * 2
    const requiredConnectors = powerReqs?.connectors || []

    // Check wattage
    let status: 'compatible' | 'warning' | 'incompatible' = 'compatible'
    
    if (psu.wattage < recommendedPSU) {
      if (psu.wattage < requiredWattage * 1.5) {
        status = 'incompatible'
      } else {
        status = 'warning'
      }
    }

    // Check power connectors
    const availableConnectors = psu.connectors || []
    const missingConnectors = requiredConnectors.filter(required => {
      const availableCount = availableConnectors.filter(conn => 
        conn.toLowerCase().includes(required.toLowerCase()) ||
        (required === '12VHPWR' && (conn.includes('12-pin') || conn.includes('16-pin')))
      ).length
      
      const requiredCount = requiredConnectors.filter(conn => conn === required).length
      return availableCount < requiredCount
    })

    if (missingConnectors.length > 0) {
      status = 'incompatible'
    }

    return {
      status,
      required: recommendedPSU,
      available: psu.wattage,
      connectors: {
        required: requiredConnectors,
        available: availableConnectors,
        missing: missingConnectors,
      },
    }
  }

  /**
   * Check physical clearance compatibility
   */
  static checkPhysicalFit(gpu: any, pcCase: SystemSpecs['case']) {
    const physicalSpecs = gpu.physicalSpecs as any
    const dimensions = physicalSpecs?.dimensions || {}

    const gpuLength = dimensions.length || 0
    const gpuWidth = dimensions.width || 0
    const gpuHeight = dimensions.height || 40 // Default dual-slot height

    let status: 'compatible' | 'warning' | 'incompatible' = 'compatible'

    // Check length clearance
    if (pcCase.maxGPULength && gpuLength > pcCase.maxGPULength) {
      status = 'incompatible'
    } else if (pcCase.maxGPULength && gpuLength > pcCase.maxGPULength * 0.95) {
      if (status !== 'incompatible') status = 'warning'
    }

    // Check width clearance
    if (pcCase.maxGPUWidth && gpuWidth > pcCase.maxGPUWidth) {
      status = 'incompatible'
    } else if (pcCase.maxGPUWidth && gpuWidth > pcCase.maxGPUWidth * 0.95) {
      if (status !== 'incompatible') status = 'warning'
    }

    // Check height clearance (slot count)
    if (pcCase.maxGPUHeight && gpuHeight > pcCase.maxGPUHeight) {
      status = 'incompatible'
    }

    return {
      status,
      clearance: {
        length: {
          required: gpuLength,
          available: pcCase.maxGPULength || 0,
        },
        width: {
          required: gpuWidth,
          available: pcCase.maxGPUWidth || 0,
        },
        height: {
          required: gpuHeight,
          available: pcCase.maxGPUHeight || 0,
        },
      },
    }
  }

  /**
   * Analyze CPU bottleneck potential
   */
  static analyzeCPUBottleneck(gpu: any, cpu: SystemSpecs['cpu']) {
    const gpuSpecs = gpu.specifications as any
    
    // Simple bottleneck analysis based on GPU tier and CPU performance
    const gpuTier = this.getGPUTier(gpu)
    const cpuScore = this.getCPUScore(cpu)
    
    // Calculate bottleneck percentage (simplified algorithm)
    let bottleneckPercentage = 0
    let severity: 'none' | 'mild' | 'moderate' | 'severe' = 'none'
    let recommendation = 'Your CPU and GPU are well balanced.'

    if (gpuTier === 'high-end' && cpuScore < 70) {
      bottleneckPercentage = Math.max(0, 80 - cpuScore)
      severity = bottleneckPercentage > 30 ? 'severe' : bottleneckPercentage > 15 ? 'moderate' : 'mild'
      recommendation = 'Consider upgrading your CPU to avoid bottlenecking this high-end GPU.'
    } else if (gpuTier === 'mid-range' && cpuScore < 50) {
      bottleneckPercentage = Math.max(0, 60 - cpuScore)
      severity = bottleneckPercentage > 20 ? 'moderate' : 'mild'
      recommendation = 'Your CPU may limit this GPU\'s performance in CPU-intensive games.'
    } else if (gpuTier === 'entry-level' && cpuScore < 30) {
      bottleneckPercentage = Math.max(0, 40 - cpuScore)
      severity = 'mild'
      recommendation = 'Minor CPU bottleneck possible in some scenarios.'
    }

    return {
      cpuBottleneck: bottleneckPercentage,
      severity,
      recommendation,
    }
  }

  /**
   * Get GPU performance tier
   */
  private static getGPUTier(gpu: any): 'entry-level' | 'mid-range' | 'high-end' {
    const specs = gpu.specifications as any
    const powerReqs = gpu.powerRequirements as any
    
    const memorySize = specs?.memory?.size || 0
    const tdp = powerReqs?.tdp || 0
    const cudaCores = specs?.cores?.cudaCores || specs?.cores?.streamProcessors || 0
    
    // High-end GPUs
    if (memorySize >= 16 || tdp >= 300 || cudaCores >= 8000) {
      return 'high-end'
    }
    
    // Mid-range GPUs
    if (memorySize >= 8 || tdp >= 150 || cudaCores >= 3000) {
      return 'mid-range'
    }
    
    // Entry-level GPUs
    return 'entry-level'
  }

  /**
   * Get CPU performance score (simplified)
   */
  private static getCPUScore(cpu: SystemSpecs['cpu']): number {
    let score = 0
    
    // Base score from core count
    score += Math.min(cpu.cores * 8, 64) // Max 64 points from cores
    
    // Thread bonus
    if (cpu.threads > cpu.cores) {
      score += Math.min((cpu.threads - cpu.cores) * 2, 16) // Max 16 points from hyperthreading
    }
    
    // Clock speed bonus
    score += Math.min((cpu.baseClock - 2000) / 100, 20) // Max 20 points from clock speed
    
    // CPU generation/architecture bonus (simplified by model name)
    const model = cpu.model.toLowerCase()
    if (model.includes('i9') || model.includes('ryzen 9') || model.includes('threadripper')) {
      score += 20
    } else if (model.includes('i7') || model.includes('ryzen 7')) {
      score += 15
    } else if (model.includes('i5') || model.includes('ryzen 5')) {
      score += 10
    } else if (model.includes('i3') || model.includes('ryzen 3')) {
      score += 5
    }
    
    return Math.min(score, 100) // Cap at 100
  }

  /**
   * Generate compatibility recommendations
   */
  private static generateRecommendations(
    power: any,
    physical: any,
    bottleneck: any
  ): string[] {
    const recommendations: string[] = []

    // Power recommendations
    if (power.status === 'incompatible') {
      if (power.connectors.missing.length > 0) {
        recommendations.push(
          `Upgrade your PSU to one with ${power.connectors.missing.join(', ')} connector(s)`
        )
      }
      if (power.available < power.required) {
        recommendations.push(
          `Upgrade to a ${power.required}W or higher PSU (currently ${power.available}W)`
        )
      }
    } else if (power.status === 'warning') {
      recommendations.push(
        `Consider upgrading to a ${power.required}W PSU for optimal performance and headroom`
      )
    }

    // Physical recommendations
    if (physical.status === 'incompatible') {
      if (physical.clearance.length.required > physical.clearance.length.available) {
        recommendations.push(
          `GPU is too long (${physical.clearance.length.required}mm) for your case (${physical.clearance.length.available}mm max)`
        )
      }
      if (physical.clearance.width.required > physical.clearance.width.available) {
        recommendations.push(
          `GPU is too wide (${physical.clearance.width.required}mm) for your case (${physical.clearance.width.available}mm max)`
        )
      }
      if (physical.clearance.height.required > physical.clearance.height.available) {
        recommendations.push(
          `GPU requires ${Math.ceil(physical.clearance.height.required / 20)} slots but case only supports ${Math.floor(physical.clearance.height.available / 20)}`
        )
      }
    }

    // Bottleneck recommendations
    if (bottleneck.severity === 'severe') {
      recommendations.push(bottleneck.recommendation)
    } else if (bottleneck.severity === 'moderate') {
      recommendations.push(`${bottleneck.recommendation} (${bottleneck.cpuBottleneck}% potential bottleneck)`)
    }

    // General recommendations
    if (recommendations.length === 0) {
      recommendations.push('Your system is compatible with this GPU!')
    }

    return recommendations
  }

  /**
   * Get GPU requirements summary
   */
  static getGPURequirements(gpu: any) {
    const powerReqs = gpu.powerRequirements as any
    const physicalSpecs = gpu.physicalSpecs as any
    const dimensions = physicalSpecs?.dimensions || {}

    return {
      power: {
        tdp: powerReqs?.tdp || 0,
        recommendedPSU: powerReqs?.recommendedPSU || (powerReqs?.tdp || 0) * 2,
        connectors: powerReqs?.connectors || [],
      },
      physical: {
        length: dimensions.length || 0,
        width: dimensions.width || 0,
        height: dimensions.height || 40,
        slots: Math.ceil((dimensions.height || 40) / 20),
      },
      motherboard: {
        pciSlot: 'PCIe x16',
        minimumVersion: 'PCIe 3.0',
      },
    }
  }

  /**
   * Get compatibility tips for a specific workload
   */
  static getWorkloadCompatibilityTips(workload: string): string[] {
    const tips: Record<string, string[]> = {
      gaming: [
        'Ensure your PSU can handle power spikes during gaming',
        'Check that your case has adequate airflow for cooling',
        'Verify your monitor supports the GPU\'s output ports',
        'Consider CPU bottleneck at your target resolution',
      ],
      'content-creation': [
        'Ensure adequate PSU headroom for sustained workloads',
        'Check that your case supports the GPU\'s length and cooling requirements',
        'Verify sufficient system RAM for GPU-accelerated workflows',
        'Consider multi-GPU setups if your motherboard supports it',
      ],
      'ai-ml': [
        'Ensure PSU can handle 100% GPU utilization for extended periods',
        'Check cooling requirements for 24/7 operation',
        'Verify PCIe bandwidth for data transfer intensive workloads',
        'Consider system memory requirements for large datasets',
      ],
      mining: [
        'Calculate power efficiency and electricity costs',
        'Ensure robust cooling for continuous operation',
        'Check PSU efficiency rating for 24/7 operation',
        'Consider multiple GPU compatibility and spacing',
      ],
    }

    return tips[workload] || tips.gaming
  }
}