import { prisma } from './prisma'
import { RecommendationCriteria } from '@/types/gpu'

export class UserService {
  /**
   * Create a new user
   */
  static async createUser(email: string, username?: string, passwordHash?: string) {
    return await prisma.user.create({
      data: {
        email,
        username,
        passwordHash,
        preferences: {
          workload: 'gaming',
          budget: { min: 200, max: 1000 },
          notifications: {
            email: true,
            push: false,
          },
        },
      },
    })
  }

  /**
   * Get user by ID with all related data
   */
  static async getUserById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
      include: {
        reviews: {
          include: { gpu: true },
          orderBy: { createdAt: 'desc' },
        },
        wishlists: {
          include: { gpu: true },
          orderBy: { createdAt: 'desc' },
        },
        priceAlerts: {
          where: { isActive: true },
          orderBy: { createdAt: 'desc' },
        },
      },
    })
  }

  /**
   * Update user preferences
   */
  static async updateUserPreferences(userId: string, preferences: any) {
    return await prisma.user.update({
      where: { id: userId },
      data: { preferences },
    })
  }

  /**
   * Add GPU to user's wishlist
   */
  static async addToWishlist(userId: string, gpuId: string, targetPrice?: number, notes?: string) {
    return await prisma.userWishlist.upsert({
      where: {
        userId_gpuId: {
          userId,
          gpuId,
        },
      },
      update: {
        targetPrice,
        notes,
      },
      create: {
        userId,
        gpuId,
        targetPrice,
        notes,
      },
    })
  }

  /**
   * Remove GPU from user's wishlist
   */
  static async removeFromWishlist(userId: string, gpuId: string) {
    return await prisma.userWishlist.delete({
      where: {
        userId_gpuId: {
          userId,
          gpuId,
        },
      },
    })
  }

  /**
   * Get user's wishlist
   */
  static async getUserWishlist(userId: string) {
    return await prisma.userWishlist.findMany({
      where: { userId },
      include: {
        gpu: true,
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  /**
   * Create a user review
   */
  static async createReview(
    userId: string,
    gpuId: string,
    rating: number,
    title?: string,
    content?: string,
    systemSpecs?: any,
    performanceData?: any
  ) {
    return await prisma.userReview.create({
      data: {
        userId,
        gpuId,
        rating,
        title,
        content,
        systemSpecs,
        performanceData,
      },
    })
  }

  /**
   * Get reviews for a GPU
   */
  static async getGPUReviews(gpuId: string, limit: number = 10, offset: number = 0) {
    return await prisma.userReview.findMany({
      where: {
        gpuId,
        isHidden: false,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            createdAt: true,
          },
        },
      },
      orderBy: [
        { isVerified: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
      skip: offset,
    })
  }

  /**
   * Get user's reviews
   */
  static async getUserReviews(userId: string) {
    return await prisma.userReview.findMany({
      where: { userId },
      include: {
        gpu: {
          select: {
            id: true,
            name: true,
            manufacturer: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  /**
   * Update review
   */
  static async updateReview(
    reviewId: string,
    userId: string,
    updates: {
      rating?: number
      title?: string
      content?: string
      systemSpecs?: any
      performanceData?: any
    }
  ) {
    return await prisma.userReview.update({
      where: {
        id: reviewId,
        userId, // Ensure user owns the review
      },
      data: updates,
    })
  }

  /**
   * Delete review
   */
  static async deleteReview(reviewId: string, userId: string) {
    return await prisma.userReview.delete({
      where: {
        id: reviewId,
        userId, // Ensure user owns the review
      },
    })
  }

  /**
   * Get review statistics for a GPU
   */
  static async getGPUReviewStats(gpuId: string) {
    const stats = await prisma.userReview.aggregate({
      where: {
        gpuId,
        isHidden: false,
      },
      _avg: {
        rating: true,
      },
      _count: {
        rating: true,
      },
    })

    const ratingDistribution = await prisma.userReview.groupBy({
      by: ['rating'],
      where: {
        gpuId,
        isHidden: false,
      },
      _count: {
        rating: true,
      },
    })

    return {
      averageRating: stats._avg.rating || 0,
      totalReviews: stats._count.rating || 0,
      ratingDistribution: ratingDistribution.reduce((acc, item) => {
        acc[item.rating] = item._count.rating
        return acc
      }, {} as Record<number, number>),
    }
  }

  /**
   * Get user's recommendation preferences
   */
  static async getUserRecommendationCriteria(userId: string): Promise<RecommendationCriteria | null> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true },
    })

    if (!user?.preferences) return null

    const prefs = user.preferences as any
    return {
      workload: prefs.workload || 'gaming',
      budget: prefs.budget || { min: 200, max: 1000 },
      currentSystem: prefs.currentSystem,
      performanceTargets: prefs.performanceTargets,
      futureProofing: prefs.futureProofing || 2,
      priorities: prefs.priorities || {
        performance: 8,
        value: 7,
        efficiency: 6,
        features: 5,
      },
    }
  }

  /**
   * Save user's recommendation criteria
   */
  static async saveRecommendationCriteria(userId: string, criteria: RecommendationCriteria) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true },
    })

    const currentPrefs = (user?.preferences as any) || {}
    const updatedPrefs = {
      ...currentPrefs,
      ...criteria,
    }

    return await prisma.user.update({
      where: { id: userId },
      data: { preferences: updatedPrefs },
    })
  }
}