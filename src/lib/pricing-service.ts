import { prisma } from './prisma'
import { priceValidator } from './price-validator'

export interface RetailerPrice {
  retailer: string
  price: number
  availability: 'in-stock' | 'limited' | 'out-of-stock'
  url: string
  shipping?: number
  condition?: 'new' | 'used' | 'refurbished'
  lastUpdated: Date
}

export interface PriceHistory {
  date: Date
  price: number
  retailer: string
  availability: string
}

export interface PriceTrend {
  gpuId: string
  currentPrice: number
  lowestPrice: number
  highestPrice: number
  averagePrice: number
  priceChange24h: number
  priceChange7d: number
  priceChange30d: number
  trend: 'rising' | 'falling' | 'stable'
  lastUpdated: Date
}

export interface PriceAlert {
  id: string
  userId: string
  gpuId: string
  targetPrice: number
  currentPrice: number
  isTriggered: boolean
  emailAlert: boolean
  pushAlert: boolean
}

export class PricingService {
  private readonly PRICE_STALENESS_HOURS = 6 // Consider prices stale after 6 hours
  private readonly MAX_PRICE_CHANGE_PERCENT = 50 // Flag suspicious price changes

  /**
   * Get current pricing for a GPU (static method for tests)
   */
  static async getCurrentPricing(gpuId: string): Promise<any[]> {
    try {
      return await prisma.gPUPricing.findMany({
        where: { gpuId },
        orderBy: { scrapedAt: 'desc' },
        take: 10
      })
    } catch (error) {
      console.error('Error getting current pricing:', error)
      throw error
    }
  }

  /**
   * Get best price for a GPU (static method for tests)
   */
  static async getBestPrice(gpuId: string): Promise<any | null> {
    try {
      return await prisma.gPUPricing.findFirst({
        where: { 
          gpuId,
          availability: { in: ['in-stock', 'limited'] }
        },
        orderBy: { price: 'asc' }
      })
    } catch (error) {
      console.error('Error getting best price:', error)
      throw error
    }
  }

  /**
   * Get price history for a GPU (static method for tests)
   */
  static async getPriceHistory(gpuId: string, days: number = 30): Promise<any[]> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      return await prisma.gPUPricing.findMany({
        where: {
          gpuId,
          scrapedAt: { gte: startDate }
        },
        orderBy: { scrapedAt: 'asc' }
      })
    } catch (error) {
      console.error('Error getting price history:', error)
      throw error
    }
  }

  /**
   * Add pricing data (static method for tests)
   */
  static async addPricing(pricingData: any): Promise<any> {
    try {
      return await prisma.gPUPricing.create({
        data: {
          ...pricingData,
          scrapedAt: new Date()
        }
      })
    } catch (error) {
      console.error('Error adding pricing:', error)
      throw error
    }
  }

  /**
   * Clean old pricing data (static method for tests)
   */
  static async cleanOldPricing(days: number = 180): Promise<number> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      const result = await prisma.gPUPricing.deleteMany({
        where: {
          scrapedAt: { lt: cutoffDate }
        }
      })

      return result.count
    } catch (error) {
      console.error('Error cleaning old pricing:', error)
      throw error
    }
  }

  /**
   * Create price alert (static method for tests)
   */
  static async createPriceAlert(alertData: any): Promise<any> {
    try {
      return await prisma.priceAlert.create({
        data: {
          ...alertData,
          isActive: true
        }
      })
    } catch (error) {
      console.error('Error creating price alert:', error)
      throw error
    }
  }

  /**
   * Get current prices for a GPU from all retailers
   */
  async getCurrentPrices(gpuId: string): Promise<RetailerPrice[]> {
    try {
      // Get latest prices for each retailer
      const latestPrices = await prisma.gPUPricing.findMany({
        where: { gpuId },
        orderBy: { scrapedAt: 'desc' },
        take: 20 // Get recent prices from multiple retailers
      })

      // Group by retailer and get the most recent price for each
      const retailerPrices = new Map<string, typeof latestPrices[0]>()
      
      latestPrices.forEach(price => {
        if (!retailerPrices.has(price.retailer) || 
            price.scrapedAt > retailerPrices.get(price.retailer)!.scrapedAt) {
          retailerPrices.set(price.retailer, price)
        }
      })

      return Array.from(retailerPrices.values()).map(price => ({
        retailer: price.retailer,
        price: parseFloat(price.price.toString()),
        availability: price.availability as 'in-stock' | 'limited' | 'out-of-stock',
        url: price.url || '',
        lastUpdated: price.scrapedAt
      }))
    } catch (error) {
      console.error('Error getting current prices:', error)
      throw error
    }
  }

  /**
   * Get price history for a GPU over a specified timeframe
   */
  async getPriceHistory(gpuId: string, days: number = 30): Promise<PriceHistory[]> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const priceHistory = await prisma.gPUPricing.findMany({
        where: {
          gpuId,
          scrapedAt: { gte: startDate }
        },
        orderBy: { scrapedAt: 'asc' }
      })

      return priceHistory.map(price => ({
        date: price.scrapedAt,
        price: parseFloat(price.price.toString()),
        retailer: price.retailer,
        availability: price.availability
      }))
    } catch (error) {
      console.error('Error getting price history:', error)
      throw error
    }
  }

  /**
   * Calculate price trends and statistics
   */
  async getPriceTrend(gpuId: string): Promise<PriceTrend | null> {
    try {
      const now = new Date()
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

      // Get current lowest price
      const currentPrices = await this.getCurrentPrices(gpuId)
      if (currentPrices.length === 0) return null

      const currentPrice = Math.min(...currentPrices.map(p => p.price))

      // Get historical price points
      const [price24h, price7d, price30d, priceStats] = await Promise.all([
        this.getAveragePriceAt(gpuId, oneDayAgo),
        this.getAveragePriceAt(gpuId, oneWeekAgo),
        this.getAveragePriceAt(gpuId, oneMonthAgo),
        this.getPriceStatistics(gpuId, oneMonthAgo)
      ])

      // Calculate price changes
      const priceChange24h = price24h ? ((currentPrice - price24h) / price24h) * 100 : 0
      const priceChange7d = price7d ? ((currentPrice - price7d) / price7d) * 100 : 0
      const priceChange30d = price30d ? ((currentPrice - price30d) / price30d) * 100 : 0

      // Determine trend
      let trend: 'rising' | 'falling' | 'stable' = 'stable'
      if (priceChange7d > 5) trend = 'rising'
      else if (priceChange7d < -5) trend = 'falling'

      return {
        gpuId,
        currentPrice,
        lowestPrice: priceStats.lowest,
        highestPrice: priceStats.highest,
        averagePrice: priceStats.average,
        priceChange24h,
        priceChange7d,
        priceChange30d,
        trend,
        lastUpdated: now
      }
    } catch (error) {
      console.error('Error calculating price trend:', error)
      throw error
    }
  }

  /**
   * Add new price data (from scraping or API)
   */
  async addPriceData(gpuId: string, retailerPrices: Omit<RetailerPrice, 'lastUpdated'>[]): Promise<void> {
    try {
      let validCount = 0;
      let rejectedCount = 0;

      for (const price of retailerPrices) {
        // Basic data validation
        if (!this.validatePriceData(price)) {
          rejectedCount++;
          continue;
        }

        // Advanced price validation using the price validator
        const validation = await priceValidator.validatePrice(gpuId, price.retailer, price.price);

        if (!validation.isValid) {
          console.warn(`Price rejected for ${gpuId} at ${price.retailer}: $${price.price} - ${validation.reason}`);
          rejectedCount++;
          continue;
        }

        if (validation.confidence < 0.5) {
          console.warn(`Low confidence price for ${gpuId} at ${price.retailer}: $${price.price} (confidence: ${validation.confidence})`);
        }

        // Insert validated price data
        await prisma.gPUPricing.create({
          data: {
            gpuId,
            retailer: price.retailer,
            price: price.price,
            url: price.url || '',
            scrapedAt: new Date()
          }
        });

        validCount++;
      }

      console.log(`Price data for ${gpuId}: ${validCount} valid, ${rejectedCount} rejected`);

      // Check and trigger price alerts
      await this.checkPriceAlerts(gpuId);
    } catch (error) {
      console.error('Error adding price data:', error);
      throw error;
    }
  }

  /**
   * Get price alerts for a user
   */
  static async getUserPriceAlerts(userId: string): Promise<any[]> {
    try {
      const alerts = await prisma.priceAlert.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' }
      })

      // Manually fetch GPU data for each alert
      const alertsWithGPU = await Promise.all(
        alerts.map(async (alert) => {
          const gpu = await prisma.gPU.findUnique({
            where: { id: alert.gpuId },
            select: { id: true, name: true, manufacturer: true }
          })
          return { ...alert, gpu }
        })
      )

      return alertsWithGPU
    } catch (error) {
      console.error('Error getting user price alerts:', error)
      throw error
    }
  }

  /**
   * Update a price alert
   */
  static async updatePriceAlert(alertId: string, updates: { targetPrice?: number; emailAlert?: boolean; pushAlert?: boolean }): Promise<any> {
    try {
      return await prisma.priceAlert.update({
        where: { id: alertId },
        data: updates
      })
    } catch (error) {
      console.error('Error updating price alert:', error)
      throw error
    }
  }

  /**
   * Delete a price alert
   */
  static async deletePriceAlert(alertId: string): Promise<any> {
    try {
      return await prisma.priceAlert.delete({
        where: { id: alertId }
      })
    } catch (error) {
      console.error('Error deleting price alert:', error)
      throw error
    }
  }

  /**
   * Check all price alerts and trigger notifications
   */
  static async checkPriceAlerts(): Promise<{ checked: number; triggered: number; errors?: string[] }> {
    try {
      const activeAlerts = await prisma.priceAlert.findMany({
        where: { isActive: true, triggeredAt: null },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      })

      let triggered = 0
      const errors: string[] = []

      for (const alert of activeAlerts) {
        const currentPricing = await prisma.gPUPricing.findFirst({
          where: { gpuId: alert.gpuId },
          orderBy: { scrapedAt: 'desc' }
        })

        if (!currentPricing) {
          errors.push(`No current pricing found for GPU ${alert.gpuId}`)
          continue
        }

        if (parseFloat(currentPricing.price.toString()) <= parseFloat(alert.targetPrice.toString())) {
          await prisma.priceAlert.update({
            where: { id: alert.id },
            data: { triggeredAt: new Date() }
          })
          triggered++
        }
      }

      const result: { checked: number; triggered: number; errors?: string[] } = {
        checked: activeAlerts.length,
        triggered
      }

      if (errors.length > 0) {
        result.errors = errors
      }

      return result
    } catch (error) {
      console.error('Error checking price alerts:', error)
      throw error
    }
  }

  /**
   * Get pricing statistics
   */
  static async getPricingStatistics(): Promise<{ totalPricePoints: number; retailerCounts: Record<string, number>; averagePrice: number }> {
    try {
      const pricingData = await prisma.gPUPricing.findMany()
      
      const retailerCounts: Record<string, number> = {}
      let totalPrice = 0
      let totalCount = 0

      pricingData.forEach(price => {
        // Handle aggregated data format from tests
        if (price._count && price._count.id) {
          retailerCounts[price.retailer] = price._count.id
          totalCount += price._count.id
        } else {
          // Handle individual pricing records
          retailerCounts[price.retailer] = (retailerCounts[price.retailer] || 0) + 1
          totalCount++
          // Handle both string and number price types safely
          const priceValue = typeof price.price === 'string' ? parseFloat(price.price) : price.price
          totalPrice += priceValue || 0
        }
      })

      return {
        totalPricePoints: totalCount,
        retailerCounts,
        averagePrice: totalCount > 0 ? totalPrice / totalCount : 0
      }
    } catch (error) {
      console.error('Error getting pricing statistics:', error)
      throw error
    }
  }

  /**
   * Get availability status for a GPU
   */
  static async getAvailabilityStatus(gpuId: string): Promise<{ inStock: number; limited: number; outOfStock: number; totalRetailers: number; availabilityPercentage: number }> {
    try {
      const pricingData = await prisma.gPUPricing.findMany({
        where: { gpuId },
        orderBy: { scrapedAt: 'desc' },
        take: 10 // Get recent data from multiple retailers
      })

      const counts = {
        inStock: 0,
        limited: 0,
        outOfStock: 0
      }

      pricingData.forEach(price => {
        switch (price.availability) {
          case 'in-stock':
            counts.inStock++
            break
          case 'limited':
            counts.limited++
            break
          case 'out-of-stock':
            counts.outOfStock++
            break
        }
      })

      const totalRetailers = pricingData.length
      const availableCount = counts.inStock + counts.limited
      const availabilityPercentage = totalRetailers > 0 ? (availableCount / totalRetailers) * 100 : 0

      return {
        ...counts,
        totalRetailers,
        availabilityPercentage: Math.round(availabilityPercentage * 100) / 100 // Round to 2 decimal places
      }
    } catch (error) {
      console.error('Error getting availability status:', error)
      throw error
    }
  }

  /**
   * Create a new price alert
   */
  async createPriceAlert(userId: string, gpuId: string, targetPrice: number, emailAlert = true, pushAlert = false): Promise<void> {
    try {
      await prisma.priceAlert.create({
        data: {
          userId,
          gpuId,
          targetPrice,
          emailAlert,
          pushAlert,
          isActive: true
        }
      })
    } catch (error) {
      console.error('Error creating price alert:', error)
      throw error
    }
  }

  /**
   * Get market trends across all GPUs
   */
  async getMarketTrends(): Promise<any> {
    try {
      const [totalGPUs, avgPriceChange, topRising, topFalling] = await Promise.all([
        prisma.gPU.count(),
        this.getAverageMarketChange(),
        this.getTopPriceMovers('rising'),
        this.getTopPriceMovers('falling')
      ])

      return {
        totalGPUs,
        averagePriceChange: avgPriceChange,
        topRising,
        topFalling,
        lastUpdated: new Date()
      }
    } catch (error) {
      console.error('Error getting market trends:', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async getAveragePriceAt(gpuId: string, date: Date): Promise<number | null> {
    const prices = await prisma.gPUPricing.findMany({
      where: {
        gpuId,
        scrapedAt: {
          gte: new Date(date.getTime() - 2 * 60 * 60 * 1000), // 2 hours before
          lte: new Date(date.getTime() + 2 * 60 * 60 * 1000)  // 2 hours after
        }
      }
    })

    if (prices.length === 0) return null

    const avgPrice = prices.reduce((sum, price) => sum + parseFloat(price.price.toString()), 0) / prices.length
    return avgPrice
  }

  private async getPriceStatistics(gpuId: string, since: Date): Promise<{ lowest: number; highest: number; average: number }> {
    const result = await prisma.gPUPricing.aggregate({
      where: {
        gpuId,
        scrapedAt: { gte: since }
      },
      _min: { price: true },
      _max: { price: true },
      _avg: { price: true }
    })

    return {
      lowest: parseFloat(result._min.price?.toString() || '0'),
      highest: parseFloat(result._max.price?.toString() || '0'),
      average: parseFloat(result._avg.price?.toString() || '0')
    }
  }

  private validatePriceData(price: Omit<RetailerPrice, 'lastUpdated'>): boolean {
    return !!(
      price.retailer &&
      price.price > 0 &&
      price.price < 10000 && // Reasonable upper limit
      price.availability &&
      price.url
    )
  }

  private async validatePriceChange(gpuId: string, retailer: string, newPrice: number): Promise<boolean> {
    const lastPrice = await prisma.gPUPricing.findFirst({
      where: { gpuId, retailer },
      orderBy: { scrapedAt: 'desc' }
    })

    if (!lastPrice) return true // No previous price to compare

    const priceChange = Math.abs((newPrice - parseFloat(lastPrice.price.toString())) / parseFloat(lastPrice.price.toString())) * 100
    return priceChange <= this.MAX_PRICE_CHANGE_PERCENT
  }

  private async checkPriceAlerts(gpuId: string): Promise<void> {
    const currentPrices = await this.getCurrentPrices(gpuId)
    if (currentPrices.length === 0) return

    const lowestPrice = Math.min(...currentPrices.map(p => p.price))

    const triggeredAlerts = await prisma.priceAlert.findMany({
      where: {
        gpuId,
        isActive: true,
        targetPrice: { gte: lowestPrice }
      },
      include: {
        user: {
          select: { email: true }
        }
      }
    })

    // TODO: Send notifications for triggered alerts
    for (const alert of triggeredAlerts) {
      console.log(`Price alert triggered for user ${alert.userId}: GPU ${gpuId} is now $${lowestPrice}`)
      
      // Mark alert as triggered
      await prisma.priceAlert.update({
        where: { id: alert.id },
        data: { triggeredAt: new Date() }
      })
    }
  }

  private async getAverageMarketChange(): Promise<number> {
    // This would calculate average price change across all GPUs
    // Simplified implementation for now
    return 0
  }

  private async getTopPriceMovers(direction: 'rising' | 'falling'): Promise<any[]> {
    // This would get GPUs with biggest price movements
    // Simplified implementation for now
    return []
  }
}

export const pricingService = new PricingService()