import { prisma } from './prisma'

export interface MigrationResult {
  success: boolean
  migratedCount: number
  errors: string[]
  warnings: string[]
}

export class DataMigrationService {
  /**
   * Migrate GPU data from old format to new format
   */
  static async migrateGPUSpecifications(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migratedCount: 0,
      errors: [],
      warnings: []
    }

    try {
      // Find GPUs that might need migration (missing certain fields)
      const gpus = await prisma.gPU.findMany()

      for (const gpu of gpus) {
        let needsUpdate = false
        let updatedSpecs = { ...gpu.specifications }
        let updatedPhysicalSpecs = { ...gpu.physicalSpecs }
        let updatedPowerReqs = { ...gpu.powerRequirements }

        // Migration 1: Ensure all specifications have proper structure
        if (!updatedSpecs || typeof updatedSpecs !== 'object') {
          updatedSpecs = {}
          needsUpdate = true
        }

        // Migration 2: Add missing memory structure
        if (!updatedSpecs.memory) {
          updatedSpecs.memory = {
            size: 0,
            type: 'Unknown',
            bandwidth: 0,
            busWidth: 0
          }
          needsUpdate = true
          result.warnings.push(`Added missing memory structure for ${gpu.name}`)
        }

        // Migration 3: Add missing cores structure
        if (!updatedSpecs.cores) {
          updatedSpecs.cores = {}
          needsUpdate = true
          result.warnings.push(`Added missing cores structure for ${gpu.name}`)
        }

        // Migration 4: Add missing clocks structure
        if (!updatedSpecs.clocks) {
          updatedSpecs.clocks = {
            baseClock: 0,
            boostClock: 0,
            memoryClock: 0
          }
          needsUpdate = true
          result.warnings.push(`Added missing clocks structure for ${gpu.name}`)
        }

        // Migration 5: Ensure physical specs structure
        if (!updatedPhysicalSpecs || typeof updatedPhysicalSpecs !== 'object') {
          updatedPhysicalSpecs = {
            dimensions: {
              length: 0,
              width: 0,
              height: 40 // Default dual-slot height
            },
            slots: 2,
            cooling: {
              type: 'Air',
              fans: 2
            },
            ports: {}
          }
          needsUpdate = true
          result.warnings.push(`Added missing physical specs for ${gpu.name}`)
        }

        // Migration 6: Ensure power requirements structure
        if (!updatedPowerReqs || typeof updatedPowerReqs !== 'object') {
          updatedPowerReqs = {
            tdp: 0,
            recommendedPSU: 0,
            connectors: []
          }
          needsUpdate = true
          result.warnings.push(`Added missing power requirements for ${gpu.name}`)
        }

        // Migration 7: Fix connector format (ensure array)
        if (updatedPowerReqs.connectors && !Array.isArray(updatedPowerReqs.connectors)) {
          updatedPowerReqs.connectors = []
          needsUpdate = true
        }

        // Migration 8: Add missing slot count based on height
        if (updatedPhysicalSpecs.dimensions?.height && !updatedPhysicalSpecs.slots) {
          updatedPhysicalSpecs.slots = Math.ceil(updatedPhysicalSpecs.dimensions.height / 20)
          needsUpdate = true
        }

        // Migration 9: Add missing recommended PSU based on TDP
        if (updatedPowerReqs.tdp && !updatedPowerReqs.recommendedPSU) {
          updatedPowerReqs.recommendedPSU = updatedPowerReqs.tdp * 2
          needsUpdate = true
        }

        // Apply updates if needed
        if (needsUpdate) {
          await prisma.gPU.update({
            where: { id: gpu.id },
            data: {
              specifications: updatedSpecs,
              physicalSpecs: updatedPhysicalSpecs,
              powerRequirements: updatedPowerReqs
            }
          })
          result.migratedCount++
        }
      }

      return result
    } catch (error) {
      result.success = false
      result.errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  /**
   * Update GPU specifications from external data source
   */
  static async updateGPUSpecifications(updates: Array<{
    gpuId: string
    specifications?: any
    physicalSpecs?: any
    powerRequirements?: any
  }>): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migratedCount: 0,
      errors: [],
      warnings: []
    }

    try {
      for (const update of updates) {
        // Verify GPU exists
        const gpu = await prisma.gPU.findUnique({
          where: { id: update.gpuId }
        })

        if (!gpu) {
          result.errors.push(`GPU with ID ${update.gpuId} not found`)
          continue
        }

        // Prepare update data
        const updateData: any = {}
        
        if (update.specifications) {
          updateData.specifications = {
            ...gpu.specifications,
            ...update.specifications
          }
        }

        if (update.physicalSpecs) {
          updateData.physicalSpecs = {
            ...gpu.physicalSpecs,
            ...update.physicalSpecs
          }
        }

        if (update.powerRequirements) {
          updateData.powerRequirements = {
            ...gpu.powerRequirements,
            ...update.powerRequirements
          }
        }

        // Apply update
        if (Object.keys(updateData).length > 0) {
          await prisma.gPU.update({
            where: { id: update.gpuId },
            data: updateData
          })
          result.migratedCount++
        }
      }

      return result
    } catch (error) {
      result.success = false
      result.errors.push(`Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  /**
   * Backup GPU data to JSON format
   */
  static async backupGPUData(): Promise<{
    success: boolean
    backup: any
    error?: string
  }> {
    try {
      const gpus = await prisma.gPU.findMany({
        include: {
          pricing: {
            take: 5,
            orderBy: {
              scrapedAt: 'desc'
            }
          },
          reviews: {
            take: 10,
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  username: true
                }
              }
            }
          }
        }
      })

      const backup = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        gpuCount: gpus.length,
        data: gpus
      }

      return {
        success: true,
        backup
      }
    } catch (error) {
      return {
        success: false,
        backup: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Restore GPU data from backup
   */
  static async restoreGPUData(backupData: any): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migratedCount: 0,
      errors: [],
      warnings: []
    }

    try {
      // Validate backup format
      if (!backupData.data || !Array.isArray(backupData.data)) {
        result.success = false
        result.errors.push('Invalid backup format')
        return result
      }

      // Clear existing data (be careful!)
      await prisma.gPUPricing.deleteMany()
      await prisma.userReview.deleteMany()
      await prisma.gPU.deleteMany()

      // Restore GPUs
      for (const gpuData of backupData.data) {
        // Create GPU without relations first
        const gpu = await prisma.gPU.create({
          data: {
            name: gpuData.name,
            manufacturer: gpuData.manufacturer,
            architecture: gpuData.architecture,
            launchDate: gpuData.launchDate ? new Date(gpuData.launchDate) : null,
            originalMsrp: gpuData.originalMsrp,
            specifications: gpuData.specifications,
            physicalSpecs: gpuData.physicalSpecs,
            powerRequirements: gpuData.powerRequirements
          }
        })

        // Restore pricing data
        if (gpuData.pricing && Array.isArray(gpuData.pricing)) {
          for (const pricing of gpuData.pricing) {
            await prisma.gPUPricing.create({
              data: {
                gpuId: gpu.id,
                retailer: pricing.retailer,
                price: pricing.price,
                availability: pricing.availability,
                url: pricing.url,
                scrapedAt: new Date(pricing.scrapedAt)
              }
            })
          }
        }

        result.migratedCount++
      }

      return result
    } catch (error) {
      result.success = false
      result.errors.push(`Restore failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  /**
   * Clean up orphaned data
   */
  static async cleanupOrphanedData(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migratedCount: 0,
      errors: [],
      warnings: []
    }

    try {
      // Clean up pricing data for non-existent GPUs
      const orphanedPricing = await prisma.gPUPricing.findMany({
        where: {
          gpu: null
        }
      })

      if (orphanedPricing.length > 0) {
        await prisma.gPUPricing.deleteMany({
          where: {
            gpu: null
          }
        })
        result.migratedCount += orphanedPricing.length
        result.warnings.push(`Removed ${orphanedPricing.length} orphaned pricing entries`)
      }

      // Clean up reviews for non-existent GPUs
      const orphanedReviews = await prisma.userReview.findMany({
        where: {
          gpu: null
        }
      })

      if (orphanedReviews.length > 0) {
        await prisma.userReview.deleteMany({
          where: {
            gpu: null
          }
        })
        result.migratedCount += orphanedReviews.length
        result.warnings.push(`Removed ${orphanedReviews.length} orphaned reviews`)
      }

      // Clean up wishlist items for non-existent GPUs
      const orphanedWishlist = await prisma.userWishlist.findMany({
        where: {
          gpu: null
        }
      })

      if (orphanedWishlist.length > 0) {
        await prisma.userWishlist.deleteMany({
          where: {
            gpu: null
          }
        })
        result.migratedCount += orphanedWishlist.length
        result.warnings.push(`Removed ${orphanedWishlist.length} orphaned wishlist items`)
      }

      return result
    } catch (error) {
      result.success = false
      result.errors.push(`Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }
}