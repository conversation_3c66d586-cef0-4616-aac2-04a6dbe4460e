import { prisma } from './prisma'

export interface BenchmarkData {
  gpuId: string
  benchmarkType: 'gaming' | 'professional' | 'synthetic'
  testName: string
  resolution: string
  settings: string
  fps?: number
  score?: number
  source: string
  benchmarkData?: any
  testDate?: Date
}

export interface BenchmarkAggregation {
  gpuId: string
  benchmarkType: string
  averageFps: number
  averageScore: number
  benchmarkCount: number
  performanceRating: number
}

export class BenchmarkService {
  /**
   * Ingest benchmark data from external sources
   */
  async ingestBenchmarkData(benchmarks: BenchmarkData[]): Promise<void> {
    try {
      // Validate and clean data
      const validBenchmarks = benchmarks.filter(this.validateBenchmarkData)

      // Batch insert benchmarks
      await prisma.performanceBenchmark.createMany({
        data: validBenchmarks.map(benchmark => ({
          gpuId: benchmark.gpuId,
          benchmarkType: benchmark.benchmarkType,
          testName: benchmark.testName,
          resolution: benchmark.resolution,
          settings: benchmark.settings,
          fps: benchmark.fps ? parseFloat(benchmark.fps.toString()) : null,
          score: benchmark.score || null,
          testDate: benchmark.testDate || new Date(),
          source: benchmark.source,
          benchmarkData: benchmark.benchmarkData || null
        })),
        skipDuplicates: true
      })

      console.log(`Ingested ${validBenchmarks.length} benchmark records`)
    } catch (error) {
      console.error('Error ingesting benchmark data:', error)
      throw error
    }
  }

  /**
   * Get performance aggregation for a GPU
   */
  async getGPUPerformanceAggregation(gpuId: string): Promise<BenchmarkAggregation[]> {
    try {
      const aggregations = await prisma.performanceBenchmark.groupBy({
        by: ['benchmarkType'],
        where: { gpuId },
        _avg: {
          fps: true,
          score: true
        },
        _count: {
          id: true
        }
      })

      return aggregations.map(agg => ({
        gpuId,
        benchmarkType: agg.benchmarkType,
        averageFps: agg._avg.fps || 0,
        averageScore: agg._avg.score || 0,
        benchmarkCount: agg._count.id,
        performanceRating: this.calculatePerformanceRating(agg._avg.fps || 0, agg.benchmarkType)
      }))
    } catch (error) {
      console.error('Error getting GPU performance aggregation:', error)
      throw error
    }
  }

  /**
   * Get performance comparison between GPUs
   */
  async compareGPUPerformance(gpuIds: string[], benchmarkType?: string): Promise<any[]> {
    try {
      const where: any = { gpuId: { in: gpuIds } }
      if (benchmarkType) {
        where.benchmarkType = benchmarkType
      }

      const benchmarks = await prisma.performanceBenchmark.findMany({
        where,
        include: {
          gpu: {
            select: {
              id: true,
              name: true,
              manufacturer: true,
              architecture: true
            }
          }
        }
      })

      // Group by GPU and calculate averages
      const gpuPerformance: Record<string, any> = {}

      benchmarks.forEach(benchmark => {
        const gpuId = benchmark.gpuId
        
        if (!gpuPerformance[gpuId]) {
          gpuPerformance[gpuId] = {
            gpu: benchmark.gpu,
            benchmarks: [],
            totalFps: 0,
            totalScore: 0,
            fpsCount: 0,
            scoreCount: 0
          }
        }

        gpuPerformance[gpuId].benchmarks.push(benchmark)

        if (benchmark.fps) {
          gpuPerformance[gpuId].totalFps += parseFloat(benchmark.fps.toString())
          gpuPerformance[gpuId].fpsCount++
        }

        if (benchmark.score) {
          gpuPerformance[gpuId].totalScore += benchmark.score
          gpuPerformance[gpuId].scoreCount++
        }
      })

      // Calculate averages and return sorted results
      return Object.values(gpuPerformance)
        .map((gpu: any) => ({
          gpu: gpu.gpu,
          averageFps: gpu.fpsCount > 0 ? gpu.totalFps / gpu.fpsCount : 0,
          averageScore: gpu.scoreCount > 0 ? gpu.totalScore / gpu.scoreCount : 0,
          benchmarkCount: gpu.benchmarks.length,
          performanceRating: this.calculatePerformanceRating(
            gpu.fpsCount > 0 ? gpu.totalFps / gpu.fpsCount : 0,
            benchmarkType || 'gaming'
          )
        }))
        .sort((a, b) => b.averageFps - a.averageFps)
    } catch (error) {
      console.error('Error comparing GPU performance:', error)
      throw error
    }
  }

  /**
   * Get performance per dollar analysis
   */
  async getPerformancePerDollarAnalysis(gpuIds: string[]): Promise<any[]> {
    try {
      // Get performance data
      const performanceData = await this.compareGPUPerformance(gpuIds, 'gaming')

      // Get current pricing
      const pricing = await prisma.gPUPricing.findMany({
        where: {
          gpuId: { in: gpuIds }
        },
        orderBy: { scrapedAt: 'desc' },
        distinct: ['gpuId']
      })

      const pricingMap = new Map(pricing.map(p => [p.gpuId, parseFloat(p.price.toString())]))

      // Calculate performance per dollar
      return performanceData.map(gpu => {
        const currentPrice = pricingMap.get(gpu.gpu.id)
        const performancePerDollar = currentPrice && gpu.averageFps > 0 
          ? gpu.averageFps / currentPrice 
          : null

        return {
          ...gpu,
          currentPrice,
          performancePerDollar,
          valueRating: this.calculateValueRating(performancePerDollar)
        }
      }).sort((a, b) => (b.performancePerDollar || 0) - (a.performancePerDollar || 0))
    } catch (error) {
      console.error('Error calculating performance per dollar:', error)
      throw error
    }
  }

  /**
   * Get benchmark statistics for dashboard
   */
  async getBenchmarkStatistics(): Promise<any> {
    try {
      const [totalBenchmarks, benchmarksByType, recentBenchmarks] = await Promise.all([
        prisma.performanceBenchmark.count(),
        prisma.performanceBenchmark.groupBy({
          by: ['benchmarkType'],
          _count: { id: true }
        }),
        prisma.performanceBenchmark.findMany({
          take: 10,
          orderBy: { testDate: 'desc' },
          include: {
            gpu: {
              select: {
                name: true,
                manufacturer: true
              }
            }
          }
        })
      ])

      return {
        totalBenchmarks,
        benchmarksByType: benchmarksByType.reduce((acc, item) => {
          acc[item.benchmarkType] = item._count.id
          return acc
        }, {} as Record<string, number>),
        recentBenchmarks
      }
    } catch (error) {
      console.error('Error getting benchmark statistics:', error)
      throw error
    }
  }

  /**
   * Validate benchmark data
   */
  private validateBenchmarkData(benchmark: BenchmarkData): boolean {
    return !!(
      benchmark.gpuId &&
      benchmark.benchmarkType &&
      benchmark.testName &&
      benchmark.resolution &&
      benchmark.source &&
      (benchmark.fps || benchmark.score)
    )
  }

  /**
   * Calculate performance rating based on FPS and benchmark type
   */
  private calculatePerformanceRating(fps: number, benchmarkType: string): number {
    if (!fps) return 0

    // Different rating scales for different benchmark types
    const scales = {
      gaming: { excellent: 120, good: 60, fair: 30 },
      professional: { excellent: 100, good: 50, fair: 25 },
      synthetic: { excellent: 15000, good: 10000, fair: 5000 }
    }

    const scale = scales[benchmarkType as keyof typeof scales] || scales.gaming

    if (fps >= scale.excellent) return 5
    if (fps >= scale.good) return 4
    if (fps >= scale.fair) return 3
    if (fps > 0) return 2
    return 1
  }

  /**
   * Calculate value rating based on performance per dollar
   */
  private calculateValueRating(performancePerDollar: number | null): number {
    if (!performancePerDollar) return 0

    // Rating scale for performance per dollar (fps per dollar)
    if (performancePerDollar >= 0.3) return 5  // Excellent value
    if (performancePerDollar >= 0.2) return 4  // Good value
    if (performancePerDollar >= 0.15) return 3 // Fair value
    if (performancePerDollar >= 0.1) return 2  // Poor value
    return 1 // Very poor value
  }
}

export const benchmarkService = new BenchmarkService()