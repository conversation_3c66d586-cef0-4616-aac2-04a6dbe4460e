import axios from 'axios';
import * as cheerio from 'cheerio';

export interface ScrapedPrice {
  retailer: string;
  price: number;
  url: string;
  title: string;
  inStock: boolean;
  condition: 'new' | 'used' | 'refurbished';
}

export interface RetailerConfig {
  name: string;
  baseUrl: string;
  searchPath: string;
  selectors: {
    price: string;
    title: string;
    link: string;
    availability?: string;
  };
  headers?: Record<string, string>;
  rateLimit: number; // requests per minute
  priceRegex?: RegExp;
  titleFilter?: RegExp;
}

// Real retailer configurations for GPU price scraping
const RETAILER_CONFIGS: RetailerConfig[] = [
  {
    name: 'Newegg',
    baseUrl: 'https://www.newegg.com',
    searchPath: '/p/pl',
    selectors: {
      price: '.price-current',
      title: '.item-title',
      link: '.item-title',
      availability: '.btn-primary'
    },
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    },
    rateLimit: 30,
    priceRegex: /\$?([\d,]+\.?\d*)/,
    titleFilter: /RTX|GTX|RX|Arc/i
  },
  {
    name: 'Amazon',
    baseUrl: 'https://www.amazon.com',
    searchPath: '/s',
    selectors: {
      price: '.a-price-whole',
      title: '[data-component-type="s-search-result"] h2 a span',
      link: '[data-component-type="s-search-result"] h2 a',
      availability: '.a-color-success'
    },
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    },
    rateLimit: 20,
    priceRegex: /\$?([\d,]+\.?\d*)/,
    titleFilter: /RTX|GTX|RX|Arc/i
  },
  {
    name: 'Best Buy',
    baseUrl: 'https://www.bestbuy.com',
    searchPath: '/site/searchpage.jsp',
    selectors: {
      price: '.sr-price',
      title: '.sku-header',
      link: '.sku-item',
      availability: '.fulfillment-add-to-cart-button'
    },
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    },
    rateLimit: 25,
    priceRegex: /\$?([\d,]+\.?\d*)/,
    titleFilter: /RTX|GTX|RX|Arc/i
  }
];

export class WebScraper {
  private readonly MAX_RETRIES = 3;
  private readonly TIMEOUT_MS = 10000;
  private readonly MIN_DELAY_MS = 1000;
  private readonly MAX_DELAY_MS = 3000;

  /**
   * Scrape prices for a specific GPU from all configured retailers
   */
  async scrapeGPUPrices(gpuName: string): Promise<ScrapedPrice[]> {
    const results: ScrapedPrice[] = [];
    
    for (const retailer of RETAILER_CONFIGS) {
      try {
        console.log(`Scraping ${retailer.name} for ${gpuName}...`);
        
        const prices = await this.scrapeRetailer(gpuName, retailer);
        results.push(...prices);
        
        // Respect rate limits
        await this.delay(60000 / retailer.rateLimit);
        
      } catch (error) {
        console.error(`Error scraping ${retailer.name} for ${gpuName}:`, error);
      }
    }
    
    return results;
  }

  /**
   * Scrape a specific retailer for GPU prices
   */
  private async scrapeRetailer(gpuName: string, config: RetailerConfig): Promise<ScrapedPrice[]> {
    const searchUrl = this.buildSearchUrl(gpuName, config);
    const html = await this.fetchPage(searchUrl, config.headers);
    
    if (!html) {
      throw new Error(`Failed to fetch page from ${config.name}`);
    }
    
    return this.parseResults(html, config);
  }

  /**
   * Build search URL for a retailer
   */
  private buildSearchUrl(gpuName: string, config: RetailerConfig): string {
    const searchTerm = encodeURIComponent(gpuName);
    
    switch (config.name) {
      case 'Newegg':
        return `${config.baseUrl}${config.searchPath}?d=${searchTerm}&N=100007709`;
      case 'Amazon':
        return `${config.baseUrl}${config.searchPath}?k=${searchTerm}&rh=n%3A284822`;
      case 'Best Buy':
        return `${config.baseUrl}${config.searchPath}?st=${searchTerm}&_dyncharset=UTF-8&id=pcat17071&type=page&sc=Global&cp=1&nrp=&sp=&qp=&list=n&af=true&iht=y&usc=All+Categories&ks=960&keys=keys`;
      default:
        return `${config.baseUrl}${config.searchPath}?q=${searchTerm}`;
    }
  }

  /**
   * Fetch page content with retries and error handling
   */
  private async fetchPage(url: string, headers?: Record<string, string>): Promise<string | null> {
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        const response = await axios.get(url, {
          headers: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            ...headers
          },
          timeout: this.TIMEOUT_MS,
          maxRedirects: 5
        });
        
        return response.data;
        
      } catch (error) {
        console.warn(`Attempt ${attempt} failed for ${url}:`, error);
        
        if (attempt < this.MAX_RETRIES) {
          await this.delay(this.MIN_DELAY_MS * attempt);
        }
      }
    }
    
    return null;
  }

  /**
   * Parse HTML results to extract price information
   */
  private parseResults(html: string, config: RetailerConfig): ScrapedPrice[] {
    const $ = cheerio.load(html);
    const results: ScrapedPrice[] = [];
    
    // Find all product containers (this varies by retailer)
    const productSelectors = this.getProductContainerSelectors(config.name);
    
    $(productSelectors).each((index, element) => {
      try {
        const $element = $(element);
        
        // Extract title
        const title = this.extractText($element, config.selectors.title);
        if (!title || !this.isValidGPUTitle(title, config.titleFilter)) {
          return; // Skip non-GPU products
        }
        
        // Extract price
        const priceText = this.extractText($element, config.selectors.price);
        const price = this.parsePrice(priceText, config.priceRegex);
        if (!price || price < 50 || price > 5000) {
          return; // Skip invalid prices
        }
        
        // Extract link
        const link = this.extractLink($element, config.selectors.link, config.baseUrl);
        if (!link) {
          return; // Skip products without links
        }
        
        // Check availability
        const inStock = this.checkAvailability($element, config.selectors.availability);
        
        results.push({
          retailer: config.name,
          price,
          url: link,
          title: title.trim(),
          inStock,
          condition: 'new' // Assume new unless specified otherwise
        });
        
      } catch (error) {
        console.warn(`Error parsing product element:`, error);
      }
    });
    
    return results.slice(0, 10); // Limit to top 10 results
  }

  /**
   * Get product container selectors for different retailers
   */
  private getProductContainerSelectors(retailerName: string): string {
    switch (retailerName) {
      case 'Newegg':
        return '.item-container';
      case 'Amazon':
        return '[data-component-type="s-search-result"]';
      case 'Best Buy':
        return '.sku-item';
      default:
        return '.product-item, .product-container, .item-container';
    }
  }

  /**
   * Extract text content from element using selector
   */
  private extractText($element: cheerio.Cheerio<cheerio.Element>, selector: string): string {
    return $element.find(selector).first().text().trim();
  }

  /**
   * Extract link from element
   */
  private extractLink($element: cheerio.Cheerio<cheerio.Element>, selector: string, baseUrl: string): string {
    const href = $element.find(selector).first().attr('href');
    if (!href) return '';
    
    return href.startsWith('http') ? href : `${baseUrl}${href}`;
  }

  /**
   * Parse price from text using regex
   */
  private parsePrice(priceText: string, regex?: RegExp): number | null {
    if (!priceText) return null;
    
    const defaultRegex = /\$?([\d,]+\.?\d*)/;
    const match = priceText.match(regex || defaultRegex);
    
    if (match && match[1]) {
      const cleanPrice = match[1].replace(/,/g, '');
      const price = parseFloat(cleanPrice);
      return isNaN(price) ? null : price;
    }
    
    return null;
  }

  /**
   * Check if title is a valid GPU product
   */
  private isValidGPUTitle(title: string, filter?: RegExp): boolean {
    const defaultFilter = /RTX|GTX|RX|Arc|GeForce|Radeon/i;
    const regex = filter || defaultFilter;
    
    return regex.test(title) && !/(case|cable|bracket|mount|cooler|fan)/i.test(title);
  }

  /**
   * Check product availability
   */
  private checkAvailability($element: cheerio.Cheerio<cheerio.Element>, selector?: string): boolean {
    if (!selector) return true; // Assume in stock if no availability selector
    
    const availabilityText = $element.find(selector).text().toLowerCase();
    return !/(out of stock|unavailable|sold out|notify me)/i.test(availabilityText);
  }

  /**
   * Add random delay to avoid being blocked
   */
  private async delay(ms: number): Promise<void> {
    const randomDelay = ms + Math.random() * (this.MAX_DELAY_MS - this.MIN_DELAY_MS);
    return new Promise(resolve => setTimeout(resolve, randomDelay));
  }

  /**
   * Validate scraped price data
   */
  validatePriceData(price: ScrapedPrice): boolean {
    return (
      price.price > 0 &&
      price.price < 10000 &&
      price.title.length > 0 &&
      price.url.startsWith('http') &&
      price.retailer.length > 0
    );
  }
}

export const webScraper = new WebScraper();
