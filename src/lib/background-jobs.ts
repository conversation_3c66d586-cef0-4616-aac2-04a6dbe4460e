import { pricingService } from './pricing-service'
import { prisma } from './prisma'
import { webScraper, ScrapedPrice } from './web-scraper'

export interface ScrapingJob {
  id: string
  type: 'price-scraping' | 'data-validation' | 'alert-check'
  status: 'pending' | 'running' | 'completed' | 'failed'
  data: any
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  error?: string
}

export interface RetailerConfig {
  name: string
  baseUrl: string
  searchPath: string
  selectors: {
    price: string
    availability: string
    title: string
  }
  headers?: Record<string, string>
  rateLimit: number // requests per minute
}

// Mock retailer configurations (in production, these would be more sophisticated)
const RETAILER_CONFIGS: RetailerConfig[] = [
  {
    name: 'Newegg',
    baseUrl: 'https://www.newegg.com',
    searchPath: '/p/pl',
    selectors: {
      price: '.price-current',
      availability: '.btn-primary',
      title: '.item-title'
    },
    rateLimit: 30
  },
  {
    name: 'Amazon',
    baseUrl: 'https://www.amazon.com',
    searchPath: '/s',
    selectors: {
      price: '.a-price-whole',
      availability: '.a-color-success',
      title: '.a-size-medium'
    },
    rateLimit: 20
  },
  {
    name: 'Best Buy',
    baseUrl: 'https://www.bestbuy.com',
    searchPath: '/site/searchpage.jsp',
    selectors: {
      price: '.sr-price',
      availability: '.fulfillment-add-to-cart-button',
      title: '.sku-header'
    },
    rateLimit: 25
  }
]

export class BackgroundJobProcessor {
  private jobQueue: ScrapingJob[] = []
  private isProcessing = false
  private readonly MAX_CONCURRENT_JOBS = 3
  private readonly JOB_TIMEOUT_MS = 30000 // 30 seconds

  constructor() {
    // Start processing jobs
    this.startJobProcessor()
  }

  /**
   * Add a price scraping job to the queue
   */
  async addPriceScrapingJob(gpuIds: string[]): Promise<string> {
    const jobId = this.generateJobId()
    
    const job: ScrapingJob = {
      id: jobId,
      type: 'price-scraping',
      status: 'pending',
      data: { gpuIds },
      createdAt: new Date()
    }

    this.jobQueue.push(job)
    console.log(`Added price scraping job ${jobId} for ${gpuIds.length} GPUs`)
    
    return jobId
  }

  /**
   * Add a data validation job
   */
  async addDataValidationJob(): Promise<string> {
    const jobId = this.generateJobId()
    
    const job: ScrapingJob = {
      id: jobId,
      type: 'data-validation',
      status: 'pending',
      data: {},
      createdAt: new Date()
    }

    this.jobQueue.push(job)
    console.log(`Added data validation job ${jobId}`)
    
    return jobId
  }

  /**
   * Add an alert checking job
   */
  async addAlertCheckJob(): Promise<string> {
    const jobId = this.generateJobId()
    
    const job: ScrapingJob = {
      id: jobId,
      type: 'alert-check',
      status: 'pending',
      data: {},
      createdAt: new Date()
    }

    this.jobQueue.push(job)
    console.log(`Added alert check job ${jobId}`)
    
    return jobId
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): ScrapingJob | null {
    return this.jobQueue.find(job => job.id === jobId) || null
  }

  /**
   * Start the job processor
   */
  private startJobProcessor(): void {
    setInterval(async () => {
      if (!this.isProcessing && this.jobQueue.length > 0) {
        await this.processNextJob()
      }
    }, 5000) // Check every 5 seconds
  }

  /**
   * Process the next job in the queue
   */
  private async processNextJob(): Promise<void> {
    const job = this.jobQueue.find(j => j.status === 'pending')
    if (!job) return

    this.isProcessing = true
    job.status = 'running'
    job.startedAt = new Date()

    try {
      console.log(`Processing job ${job.id} of type ${job.type}`)

      switch (job.type) {
        case 'price-scraping':
          await this.processPriceScrapingJob(job)
          break
        case 'data-validation':
          await this.processDataValidationJob(job)
          break
        case 'alert-check':
          await this.processAlertCheckJob(job)
          break
        default:
          throw new Error(`Unknown job type: ${job.type}`)
      }

      job.status = 'completed'
      job.completedAt = new Date()
      console.log(`Completed job ${job.id}`)

    } catch (error) {
      job.status = 'failed'
      job.error = error instanceof Error ? error.message : 'Unknown error'
      job.completedAt = new Date()
      console.error(`Job ${job.id} failed:`, error)

    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Process price scraping job
   */
  private async processPriceScrapingJob(job: ScrapingJob): Promise<void> {
    const { gpuIds } = job.data

    for (const gpuId of gpuIds) {
      // Get GPU details
      const gpu = await prisma.gPU.findUnique({
        where: { id: gpuId },
        select: { name: true, manufacturer: true }
      })

      if (!gpu) {
        console.warn(`GPU not found: ${gpuId}`)
        continue
      }

      // Scrape prices using the web scraper (it handles all retailers)
      try {
        const allPrices = await webScraper.scrapeGPUPrices(gpu.name);

        if (allPrices.length > 0) {
          // Convert scraped prices to the format expected by pricing service
          const formattedPrices = allPrices.map(price => ({
            retailer: price.retailer,
            price: price.price,
            url: price.url,
            condition: price.condition
          }));

          await pricingService.addPriceData(gpuId, formattedPrices);
          console.log(`Scraped ${allPrices.length} total prices for ${gpu.name} from all retailers`);
        }

        // Add delay between GPU scraping to be respectful
        await this.delay(5000); // 5 second delay between GPUs

      } catch (error) {
        console.error(`Error scraping prices for ${gpu.name}:`, error);
      }
    }
  }

  /**
   * Process data validation job
   */
  private async processDataValidationJob(job: ScrapingJob): Promise<void> {
    console.log('Running data validation...')

    // Check for anomalous price data
    const anomalies = await this.detectPriceAnomalies()
    
    if (anomalies.length > 0) {
      console.warn(`Found ${anomalies.length} price anomalies`)
      
      // Flag or remove anomalous data
      for (const anomaly of anomalies) {
        console.warn(`Price anomaly detected: ${anomaly.retailer} - $${anomaly.price} for GPU ${anomaly.gpuId}`)
        // Could implement automatic flagging or removal here
      }
    }

    // Check for stale data
    const staleDataCount = await this.cleanupStaleData()
    console.log(`Cleaned up ${staleDataCount} stale price records`)
  }

  /**
   * Process alert checking job
   */
  private async processAlertCheckJob(job: ScrapingJob): Promise<void> {
    console.log('Checking price alerts...')

    // Get all active alerts
    const activeAlerts = await prisma.priceAlert.findMany({
      where: { isActive: true },
      include: {
        user: {
          select: { email: true }
        }
      }
    })

    let triggeredCount = 0

    for (const alert of activeAlerts) {
      const currentPrices = await pricingService.getCurrentPrices(alert.gpuId)
      
      if (currentPrices.length > 0) {
        const lowestPrice = Math.min(...currentPrices.map(p => p.price))
        
        if (lowestPrice <= parseFloat(alert.targetPrice.toString())) {
          // Alert triggered!
          await prisma.priceAlert.update({
            where: { id: alert.id },
            data: { triggeredAt: new Date() }
          })
          
          // TODO: Send email/push notification
          console.log(`Price alert triggered for user ${alert.userId}: GPU ${alert.gpuId} is now $${lowestPrice}`)
          triggeredCount++
        }
      }
    }

    console.log(`Processed ${activeAlerts.length} alerts, ${triggeredCount} triggered`)
  }

  /**
   * Real price scraping function using web scraper
   */
  private async scrapeRetailerPrices(gpuName: string, retailer: RetailerConfig): Promise<ScrapedPrice[]> {
    try {
      // Use the real web scraper to get prices
      const scrapedPrices = await webScraper.scrapeGPUPrices(gpuName);

      // Filter results for this specific retailer
      const retailerPrices = scrapedPrices.filter(price =>
        price.retailer.toLowerCase() === retailer.name.toLowerCase()
      );

      // Validate and clean the data
      const validPrices = retailerPrices.filter(price =>
        webScraper.validatePriceData(price)
      );

      console.log(`Scraped ${validPrices.length} valid prices for ${gpuName} from ${retailer.name}`);

      return validPrices;

    } catch (error) {
      console.error(`Error scraping ${retailer.name} for ${gpuName}:`, error);
      return [];
    }
  }

  /**
   * Detect price anomalies
   */
  private async detectPriceAnomalies(): Promise<any[]> {
    const anomalies = []
    
    // Find prices that are significantly different from recent averages
    const recentPrices = await prisma.gPUPricing.findMany({
      where: {
        scrapedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      }
    })

    // Group by GPU and retailer
    const priceGroups = new Map<string, typeof recentPrices>()
    
    recentPrices.forEach(price => {
      const key = `${price.gpuId}-${price.retailer}`
      if (!priceGroups.has(key)) {
        priceGroups.set(key, [])
      }
      priceGroups.get(key)!.push(price)
    })

    // Check each group for anomalies
    priceGroups.forEach((prices, key) => {
      if (prices.length < 3) return // Need at least 3 data points

      const priceValues = prices.map(p => parseFloat(p.price.toString()))
      const average = priceValues.reduce((sum, price) => sum + price, 0) / priceValues.length
      const stdDev = Math.sqrt(priceValues.reduce((sum, price) => sum + Math.pow(price - average, 2), 0) / priceValues.length)

      // Flag prices that are more than 2 standard deviations from the mean
      prices.forEach(price => {
        const priceValue = parseFloat(price.price.toString())
        if (Math.abs(priceValue - average) > 2 * stdDev) {
          anomalies.push({
            gpuId: price.gpuId,
            retailer: price.retailer,
            price: priceValue,
            average,
            deviation: Math.abs(priceValue - average)
          })
        }
      })
    })

    return anomalies
  }

  /**
   * Clean up stale price data
   */
  private async cleanupStaleData(): Promise<number> {
    const staleDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago

    const result = await prisma.gPUPricing.deleteMany({
      where: {
        scrapedAt: { lt: staleDate }
      }
    })

    return result.count
  }

  /**
   * Utility functions
   */
  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export singleton instance
export const backgroundJobProcessor = new BackgroundJobProcessor()

// Schedule regular jobs
export function scheduleRegularJobs(): void {
  // Schedule price scraping every 6 hours
  setInterval(async () => {
    const gpus = await prisma.gPU.findMany({
      select: { id: true },
      take: 50 // Scrape 50 GPUs at a time
    })
    
    if (gpus.length > 0) {
      await backgroundJobProcessor.addPriceScrapingJob(gpus.map(gpu => gpu.id))
    }
  }, 6 * 60 * 60 * 1000) // 6 hours

  // Schedule data validation daily
  setInterval(async () => {
    await backgroundJobProcessor.addDataValidationJob()
  }, 24 * 60 * 60 * 1000) // 24 hours

  // Schedule alert checking every 30 minutes
  setInterval(async () => {
    await backgroundJobProcessor.addAlertCheckJob()
  }, 30 * 60 * 1000) // 30 minutes

  console.log('Regular background jobs scheduled')
}