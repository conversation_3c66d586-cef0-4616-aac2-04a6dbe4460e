import { prisma } from './prisma'

interface PricingSeed {
  gpuName: string
  priceHistory: Array<{
    retailer: string
    prices: Array<{
      price: number
      availability: string
      daysAgo: number
    }>
  }>
}

const pricingData: PricingSeed[] = [
  {
    gpuName: 'GeForce RTX 3060 Ti GDDR6X',
    priceHistory: [
      {
        retailer: 'Newegg',
        prices: [
          { price: 449.99, availability: 'in-stock', daysAgo: 0 },
          { price: 459.99, availability: 'in-stock', daysAgo: 1 },
          { price: 469.99, availability: 'limited', daysAgo: 3 },
          { price: 479.99, availability: 'in-stock', daysAgo: 7 },
          { price: 489.99, availability: 'in-stock', daysAgo: 14 },
          { price: 499.99, availability: 'in-stock', daysAgo: 21 },
          { price: 519.99, availability: 'limited', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Amazon',
        prices: [
          { price: 439.99, availability: 'in-stock', daysAgo: 0 },
          { price: 449.99, availability: 'in-stock', daysAgo: 1 },
          { price: 459.99, availability: 'in-stock', daysAgo: 3 },
          { price: 469.99, availability: 'limited', daysAgo: 7 },
          { price: 479.99, availability: 'in-stock', daysAgo: 14 },
          { price: 489.99, availability: 'in-stock', daysAgo: 21 },
          { price: 509.99, availability: 'in-stock', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Best Buy',
        prices: [
          { price: 459.99, availability: 'limited', daysAgo: 0 },
          { price: 469.99, availability: 'in-stock', daysAgo: 1 },
          { price: 479.99, availability: 'in-stock', daysAgo: 3 },
          { price: 489.99, availability: 'in-stock', daysAgo: 7 },
          { price: 499.99, availability: 'limited', daysAgo: 14 },
          { price: 509.99, availability: 'in-stock', daysAgo: 21 },
          { price: 529.99, availability: 'in-stock', daysAgo: 30 }
        ]
      }
    ]
  },
  {
    gpuName: 'GeForce RTX 3070 Ti 8 GB GA102',
    priceHistory: [
      {
        retailer: 'Newegg',
        prices: [
          { price: 649.99, availability: 'in-stock', daysAgo: 0 },
          { price: 659.99, availability: 'in-stock', daysAgo: 1 },
          { price: 669.99, availability: 'limited', daysAgo: 3 },
          { price: 679.99, availability: 'in-stock', daysAgo: 7 },
          { price: 689.99, availability: 'in-stock', daysAgo: 14 },
          { price: 699.99, availability: 'in-stock', daysAgo: 21 },
          { price: 719.99, availability: 'limited', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Amazon',
        prices: [
          { price: 639.99, availability: 'in-stock', daysAgo: 0 },
          { price: 649.99, availability: 'in-stock', daysAgo: 1 },
          { price: 659.99, availability: 'in-stock', daysAgo: 3 },
          { price: 669.99, availability: 'limited', daysAgo: 7 },
          { price: 679.99, availability: 'in-stock', daysAgo: 14 },
          { price: 689.99, availability: 'in-stock', daysAgo: 21 },
          { price: 709.99, availability: 'in-stock', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Micro Center',
        prices: [
          { price: 629.99, availability: 'in-stock', daysAgo: 0 },
          { price: 639.99, availability: 'in-stock', daysAgo: 1 },
          { price: 649.99, availability: 'in-stock', daysAgo: 3 },
          { price: 659.99, availability: 'in-stock', daysAgo: 7 },
          { price: 669.99, availability: 'limited', daysAgo: 14 },
          { price: 679.99, availability: 'in-stock', daysAgo: 21 },
          { price: 699.99, availability: 'in-stock', daysAgo: 30 }
        ]
      }
    ]
  },
  {
    gpuName: 'GeForce RTX 3060 8 GB',
    priceHistory: [
      {
        retailer: 'Newegg',
        prices: [
          { price: 329.99, availability: 'in-stock', daysAgo: 0 },
          { price: 339.99, availability: 'in-stock', daysAgo: 1 },
          { price: 349.99, availability: 'limited', daysAgo: 3 },
          { price: 359.99, availability: 'in-stock', daysAgo: 7 },
          { price: 369.99, availability: 'in-stock', daysAgo: 14 },
          { price: 379.99, availability: 'in-stock', daysAgo: 21 },
          { price: 399.99, availability: 'limited', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Amazon',
        prices: [
          { price: 319.99, availability: 'in-stock', daysAgo: 0 },
          { price: 329.99, availability: 'in-stock', daysAgo: 1 },
          { price: 339.99, availability: 'in-stock', daysAgo: 3 },
          { price: 349.99, availability: 'limited', daysAgo: 7 },
          { price: 359.99, availability: 'in-stock', daysAgo: 14 },
          { price: 369.99, availability: 'in-stock', daysAgo: 21 },
          { price: 389.99, availability: 'in-stock', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Best Buy',
        prices: [
          { price: 339.99, availability: 'limited', daysAgo: 0 },
          { price: 349.99, availability: 'in-stock', daysAgo: 1 },
          { price: 359.99, availability: 'in-stock', daysAgo: 3 },
          { price: 369.99, availability: 'in-stock', daysAgo: 7 },
          { price: 379.99, availability: 'limited', daysAgo: 14 },
          { price: 389.99, availability: 'in-stock', daysAgo: 21 },
          { price: 409.99, availability: 'in-stock', daysAgo: 30 }
        ]
      }
    ]
  },
  {
    gpuName: 'GeForce RTX 2060 12 GB',
    priceHistory: [
      {
        retailer: 'Newegg',
        prices: [
          { price: 279.99, availability: 'in-stock', daysAgo: 0 },
          { price: 289.99, availability: 'in-stock', daysAgo: 1 },
          { price: 299.99, availability: 'limited', daysAgo: 3 },
          { price: 309.99, availability: 'in-stock', daysAgo: 7 },
          { price: 319.99, availability: 'in-stock', daysAgo: 14 },
          { price: 329.99, availability: 'in-stock', daysAgo: 21 },
          { price: 349.99, availability: 'limited', daysAgo: 30 }
        ]
      },
      {
        retailer: 'Amazon',
        prices: [
          { price: 269.99, availability: 'in-stock', daysAgo: 0 },
          { price: 279.99, availability: 'in-stock', daysAgo: 1 },
          { price: 289.99, availability: 'in-stock', daysAgo: 3 },
          { price: 299.99, availability: 'limited', daysAgo: 7 },
          { price: 309.99, availability: 'in-stock', daysAgo: 14 },
          { price: 319.99, availability: 'in-stock', daysAgo: 21 },
          { price: 339.99, availability: 'in-stock', daysAgo: 30 }
        ]
      },
      {
        retailer: 'eBay',
        prices: [
          { price: 249.99, availability: 'in-stock', daysAgo: 0 },
          { price: 259.99, availability: 'in-stock', daysAgo: 1 },
          { price: 269.99, availability: 'in-stock', daysAgo: 3 },
          { price: 279.99, availability: 'in-stock', daysAgo: 7 },
          { price: 289.99, availability: 'limited', daysAgo: 14 },
          { price: 299.99, availability: 'in-stock', daysAgo: 21 },
          { price: 319.99, availability: 'in-stock', daysAgo: 30 }
        ]
      }
    ]
  }
]

export async function seedPricingData() {
  console.log('Starting pricing data seeding...')

  for (const gpuData of pricingData) {
    // Find GPU by name
    const gpu = await prisma.gPU.findFirst({
      where: {
        name: {
          contains: gpuData.gpuName,
          mode: 'insensitive'
        }
      }
    })

    if (!gpu) {
      console.log(`GPU not found: ${gpuData.gpuName}`)
      continue
    }

    console.log(`Seeding pricing data for ${gpu.name}...`)

    // Create pricing data for this GPU
    for (const retailerData of gpuData.priceHistory) {
      for (const pricePoint of retailerData.prices) {
        try {
          const scrapedAt = new Date()
          scrapedAt.setDate(scrapedAt.getDate() - pricePoint.daysAgo)

          await prisma.gPUPricing.create({
            data: {
              gpuId: gpu.id,
              retailer: retailerData.retailer,
              price: pricePoint.price,
              availability: pricePoint.availability,
              url: `https://${retailerData.retailer.toLowerCase().replace(' ', '')}.com/gpu/${encodeURIComponent(gpu.name)}`,
              scrapedAt
            }
          })
        } catch (error) {
          // Skip duplicates or handle other errors
          if (error instanceof Error && !error.message.includes('Unique constraint')) {
            console.error(`Error creating pricing data for ${gpu.name}:`, error)
          }
        }
      }
    }
  }

  console.log('Pricing data seeding completed!')
}

// Run this function to seed the pricing data
if (require.main === module) {
  seedPricingData()
    .then(() => {
      console.log('Seeding completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}