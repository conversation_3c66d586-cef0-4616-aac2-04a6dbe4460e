import { prisma } from './prisma'

interface OverclockingSeed {
  gpuName: string
  overclockingData: Array<{
    coreClockOffset: number
    memoryClockOffset: number
    powerLimit: number
    voltageOffset?: number
    performanceGain: number
    stabilityRating: number
    stockTemperature: number
    ocTemperature: number
    maxTemperature: number
    thermalThrottling: boolean
    stockPower: number
    ocPower: number
    coolingType: string
    ambientTemp: number
    testDuration: number
    notes?: string
    source: string
  }>
}

const overclockingData: OverclockingSeed[] = [
  {
    gpuName: 'GeForce RTX 3060 Ti GDDR6X',
    overclockingData: [
      {
        coreClockOffset: 150,
        memoryClockOffset: 800,
        powerLimit: 110,
        voltageOffset: 50,
        performanceGain: 12.5,
        stabilityRating: 5,
        stockTemperature: 68,
        ocTemperature: 74,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 200,
        ocPower: 220,
        coolingType: 'AIO',
        ambientTemp: 22,
        testDuration: 60,
        notes: 'Stable overclock with AIO cooling. No artifacts or crashes during extended gaming sessions.',
        source: 'Community Submission'
      },
      {
        coreClockOffset: 100,
        memoryClockOffset: 600,
        powerLimit: 105,
        performanceGain: 8.2,
        stabilityRating: 5,
        stockTemperature: 72,
        ocTemperature: 76,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 200,
        ocPower: 210,
        coolingType: 'Air',
        ambientTemp: 24,
        testDuration: 45,
        notes: 'Conservative overclock with stock air cooling. Very stable for daily use.',
        source: 'TechPowerUp'
      },
      {
        coreClockOffset: 200,
        memoryClockOffset: 1000,
        powerLimit: 115,
        voltageOffset: 75,
        performanceGain: 16.8,
        stabilityRating: 3,
        stockTemperature: 68,
        ocTemperature: 81,
        maxTemperature: 83,
        thermalThrottling: true,
        stockPower: 200,
        ocPower: 235,
        coolingType: 'Custom Loop',
        ambientTemp: 20,
        testDuration: 30,
        notes: 'Aggressive overclock with custom water cooling. Some instability in stress tests.',
        source: 'Overclockers.net'
      }
    ]
  },
  {
    gpuName: 'GeForce RTX 3070 Ti 8 GB GA102',
    overclockingData: [
      {
        coreClockOffset: 120,
        memoryClockOffset: 700,
        powerLimit: 108,
        performanceGain: 10.3,
        stabilityRating: 5,
        stockTemperature: 71,
        ocTemperature: 77,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 290,
        ocPower: 315,
        coolingType: 'AIO',
        ambientTemp: 23,
        testDuration: 90,
        notes: 'Excellent stability with 240mm AIO. Great for 1440p gaming.',
        source: 'Hardware Unboxed'
      },
      {
        coreClockOffset: 80,
        memoryClockOffset: 500,
        powerLimit: 105,
        performanceGain: 6.7,
        stabilityRating: 5,
        stockTemperature: 75,
        ocTemperature: 78,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 290,
        ocPower: 305,
        coolingType: 'Air',
        ambientTemp: 25,
        testDuration: 60,
        notes: 'Safe overclock with aftermarket air cooler. No issues during gaming.',
        source: 'GamersNexus'
      },
      {
        coreClockOffset: 180,
        memoryClockOffset: 900,
        powerLimit: 112,
        voltageOffset: 60,
        performanceGain: 14.2,
        stabilityRating: 4,
        stockTemperature: 71,
        ocTemperature: 82,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 290,
        ocPower: 330,
        coolingType: 'Custom Loop',
        ambientTemp: 21,
        testDuration: 45,
        notes: 'High performance overclock with custom loop. Occasional artifacts in some games.',
        source: 'JayzTwoCents'
      }
    ]
  },
  {
    gpuName: 'GeForce RTX 3060 8 GB',
    overclockingData: [
      {
        coreClockOffset: 130,
        memoryClockOffset: 750,
        powerLimit: 110,
        performanceGain: 11.8,
        stabilityRating: 5,
        stockTemperature: 65,
        ocTemperature: 71,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 170,
        ocPower: 187,
        coolingType: 'AIO',
        ambientTemp: 22,
        testDuration: 75,
        notes: 'Great overclocking headroom with AIO cooling. Perfect for 1080p high refresh gaming.',
        source: 'TechPowerUp'
      },
      {
        coreClockOffset: 90,
        memoryClockOffset: 500,
        powerLimit: 106,
        performanceGain: 7.5,
        stabilityRating: 5,
        stockTemperature: 68,
        ocTemperature: 72,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 170,
        ocPower: 180,
        coolingType: 'Air',
        ambientTemp: 24,
        testDuration: 60,
        notes: 'Solid overclock with dual-fan air cooler. Very stable for everyday use.',
        source: 'Hardware Canucks'
      }
    ]
  },
  {
    gpuName: 'GeForce RTX 2060 12 GB',
    overclockingData: [
      {
        coreClockOffset: 110,
        memoryClockOffset: 600,
        powerLimit: 108,
        performanceGain: 9.2,
        stabilityRating: 5,
        stockTemperature: 70,
        ocTemperature: 75,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 184,
        ocPower: 200,
        coolingType: 'Air',
        ambientTemp: 23,
        testDuration: 60,
        notes: 'Good overclocking potential for an older generation card. Stable with aftermarket cooling.',
        source: 'TechSpot'
      },
      {
        coreClockOffset: 150,
        memoryClockOffset: 800,
        powerLimit: 112,
        voltageOffset: 40,
        performanceGain: 13.1,
        stabilityRating: 4,
        stockTemperature: 70,
        ocTemperature: 79,
        maxTemperature: 83,
        thermalThrottling: false,
        stockPower: 184,
        ocPower: 210,
        coolingType: 'AIO',
        ambientTemp: 21,
        testDuration: 45,
        notes: 'Aggressive overclock with 120mm AIO. Some instability in synthetic benchmarks.',
        source: 'LinusTechTips'
      }
    ]
  }
]

export async function seedOverclockingData() {
  console.log('Starting overclocking data seeding...')

  for (const gpuData of overclockingData) {
    // Find GPU by name
    const gpu = await prisma.gPU.findFirst({
      where: {
        name: {
          contains: gpuData.gpuName,
          mode: 'insensitive'
        }
      }
    })

    if (!gpu) {
      console.log(`GPU not found: ${gpuData.gpuName}`)
      continue
    }

    console.log(`Seeding overclocking data for ${gpu.name}...`)

    // Create overclocking data for this GPU
    for (const ocData of gpuData.overclockingData) {
      try {
        // Calculate power efficiency
        const powerEfficiency = ocData.performanceGain / ((ocData.ocPower - ocData.stockPower) / ocData.stockPower * 100)

        await prisma.overclockingData.create({
          data: {
            gpuId: gpu.id,
            coreClockOffset: ocData.coreClockOffset,
            memoryClockOffset: ocData.memoryClockOffset,
            powerLimit: ocData.powerLimit,
            voltageOffset: ocData.voltageOffset || null,
            performanceGain: parseFloat(ocData.performanceGain.toString()),
            stabilityRating: ocData.stabilityRating,
            stockTemperature: ocData.stockTemperature,
            ocTemperature: ocData.ocTemperature,
            maxTemperature: ocData.maxTemperature,
            thermalThrottling: ocData.thermalThrottling,
            stockPower: ocData.stockPower,
            ocPower: ocData.ocPower,
            powerEfficiency: parseFloat(powerEfficiency.toFixed(2)),
            coolingType: ocData.coolingType,
            ambientTemp: ocData.ambientTemp,
            testDuration: ocData.testDuration,
            source: ocData.source,
            notes: ocData.notes || null,
            testDate: new Date()
          }
        })
      } catch (error) {
        // Skip duplicates or handle other errors
        console.error(`Error creating overclocking data for ${gpu.name}:`, error)
      }
    }
  }

  console.log('Overclocking data seeding completed!')
}

// Run this function to seed the overclocking data
if (require.main === module) {
  seedOverclockingData()
    .then(() => {
      console.log('Seeding completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}