# Google Sheets Structure for GPU Variants

## Overview
You need to create a new Google Sheet for GPU variants that works alongside your existing AMD, Intel, and NVIDIA sheets.

## Step 1: Create New Google Sheet

1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it: "GPU Variants Database"
4. Note the Sheet ID from the URL (you'll need this for the script)

## Step 2: Set Up Columns

### Sheet Name: "GPU Variants"

| Column | Header | Description | Example |
|--------|--------|-------------|---------|
| A | Base GPU Name | Must match productName from existing sheets | GeForce RTX 4090 |
| B | Board Partner | Manufacturer of the variant | ASUS |
| C | Model Name | Full variant model name | ROG Strix RTX 4090 OC |
| D | SKU | Manufacturer part number | ROG-STRIX-RTX4090-O24G-GAMING |
| E | Base Clock Override | Base clock if different from reference | 2340 |
| F | Boost Clock Override | Boost clock if different from reference | 2640 |
| G | Memory Clock Override | Memory clock if different from reference | 1313 |
| H | TDP Override | TDP if different from reference | 450 |
| I | Cooling Type | Type of cooling system | Air |
| J | Cooling Fans | Number of fans | 3 |
| K | Cooling Heatpipes | Number of heatpipes | 7 |
| L | RGB Lighting | Has RGB lighting | Yes |
| M | Dual BIOS | Has dual BIOS switch | Yes |
| N | Zero Fan Mode | Has zero fan mode | Yes |
| O | Backplate | Has backplate | Yes |
| P | Slots Required | PCIe slots required | 3.5 |
| Q | Length (mm) | Card length in millimeters | 357 |
| R | Width (mm) | Card width in millimeters | 149 |
| S | Height (mm) | Card height in millimeters | 70 |
| T | Weight (kg) | Card weight in kilograms | 2.5 |
| U | Warranty (years) | Warranty period | 3 |
| V | MSRP (USD) | Manufacturer suggested retail price | 1799 |
| W | Special Features | JSON string of special features | {"overclocking": true, "customCooling": "Axial-tech fans"} |
| X | Power Connectors | Power connector types | 16-pin |
| Y | Display Ports | Display port configuration | 2x HDMI, 3x DP |

## Step 3: Sample Data

Here's sample data for the first few rows:

### Row 1 (Headers)
```
Base GPU Name | Board Partner | Model Name | SKU | Base Clock Override | Boost Clock Override | Memory Clock Override | TDP Override | Cooling Type | Cooling Fans | Cooling Heatpipes | RGB Lighting | Dual BIOS | Zero Fan Mode | Backplate | Slots Required | Length (mm) | Width (mm) | Height (mm) | Weight (kg) | Warranty (years) | MSRP (USD) | Special Features | Power Connectors | Display Ports
```

### Row 2 (RTX 4090 ASUS)
```
GeForce RTX 4090 | ASUS | ROG Strix RTX 4090 OC | ROG-STRIX-RTX4090-O24G-GAMING | 2340 | 2640 | 1313 | 450 | Air | 3 | 7 | Yes | Yes | Yes | Yes | 3.5 | 357 | 149 | 70 | 2.5 | 3 | 1799 | {"overclocking": true, "customCooling": "Axial-tech fans with 0dB mode"} | 16-pin | 2x HDMI 2.1, 3x DP 1.4a
```

### Row 3 (RTX 4090 MSI)
```
GeForce RTX 4090 | MSI | Gaming X Trio RTX 4090 | RTX-4090-GAMING-X-TRIO-24G | 2320 | 2610 | 1313 | 450 | Air | 3 | 6 | Yes | Yes | Yes | Yes | 3 | 336 | 140 | 61 | 2.2 | 3 | 1699 | {"overclocking": true, "customCooling": "Tri Frozr 3 cooling"} | 16-pin | 1x HDMI 2.1, 3x DP 1.4a
```

### Row 4 (RTX 4090 Gigabyte)
```
GeForce RTX 4090 | Gigabyte | Gaming OC RTX 4090 | GV-N4090GAMING-OC-24GD | 2310 | 2595 | 1313 | 450 | Air | 3 | 5 | Yes | No | Yes | Yes | 3 | 320 | 136 | 56 | 2.0 | 4 | 1649 | {"overclocking": true, "customCooling": "WINDFORCE 3X cooling"} | 16-pin | 2x HDMI 2.1, 2x DP 1.4a
```

## Step 4: Update Google Apps Script

1. Go to your existing Google Apps Script project
2. Replace the code with the enhanced version I provided
3. Update the `variantsSheetId` variable with your new sheet ID
4. Save and deploy

## Step 5: Test the API

### Test Base GPUs Only (existing functionality)
```
https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
```

### Test Base GPUs with Variants
```
https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec?variants=true
```

### Test Specific Brand with Variants
```
https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec?brand=nvidia&variants=true
```

## Step 6: Priority GPU Variants to Add

### Tier 1 (Add First - Most Popular)
1. **RTX 4090**: ASUS ROG Strix, MSI Gaming X Trio, Gigabyte Gaming OC, EVGA FTW3
2. **RTX 4080**: Same board partners
3. **RTX 4070 Ti**: Same board partners
4. **RTX 4070**: Same board partners
5. **RTX 4060 Ti**: Same board partners

### Tier 2 (Popular Gaming)
1. **RTX 3080**: Major variants
2. **RTX 3070**: Major variants
3. **RX 7900 XTX**: ASUS, MSI, Gigabyte, Sapphire, PowerColor
4. **RX 7800 XT**: Same board partners

### Tier 3 (Complete Coverage)
- All remaining GPUs with major board partner variants

## Step 7: Data Collection Tips

### Best Sources for Variant Data:
1. **TechPowerUp GPU Database** - Most comprehensive specs
2. **Manufacturer Websites** - Official specifications
3. **Newegg/Amazon** - Real product listings
4. **Tom's Hardware Reviews** - Actual performance data

### Efficient Data Entry:
1. Start with one GPU family (e.g., RTX 4090)
2. Add all major variants for that GPU
3. Use manufacturer websites for exact specifications
4. Copy similar data for variants from same manufacturer

## Expected Results

After implementing this structure:
- Your existing API continues to work unchanged
- New `?variants=true` parameter returns hierarchical data
- Each base GPU includes its variants array
- Maintains backward compatibility
- Scales to thousands of variants

## API Response Example

```json
{
  "success": true,
  "data": [
    {
      "id": "nvidia-GeForce RTX 4090",
      "productName": "GeForce RTX 4090",
      "brand": "nvidia",
      "isBaseGPU": true,
      "variants": [
        {
          "id": "variant-GeForce RTX 4090-ASUS-2",
          "boardPartner": "ASUS",
          "modelName": "ROG Strix RTX 4090 OC",
          "sku": "ROG-STRIX-RTX4090-O24G-GAMING",
          "baseClock": 2340,
          "boostClock": 2640
        }
      ],
      "variantCount": 3
    }
  ],
  "baseGPUCount": 420,
  "variantCount": 50
}
```
