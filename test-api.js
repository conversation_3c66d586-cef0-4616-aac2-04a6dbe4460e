// Enhanced test script for GPU recommendation API and wizard
const BASE_URL = 'http://localhost:3000/api';

async function testRecommendationSystem() {
  console.log('🧪 Testing Enhanced GPU Recommendation System...\n');

  try {
    // Test 1: Enhanced Gaming recommendations with priorities
    console.log('1. Testing Enhanced Gaming Recommendations...');
    const enhancedGamingCriteria = {
      workload: 'gaming',
      budget: { min: 400, max: 1200 },
      performanceTargets: {
        resolution: '1440p',
        targetFPS: 120,
        rayTracing: true,
        dlss: true,
        vr: false
      },
      currentSystem: {
        cpu: 'Intel i7-12700K',
        psu: 750,
        case: 'Mid Tower'
      },
      futureProofing: 4,
      priorities: {
        performance: 50,
        value: 30,
        futureProofing: 15,
        powerEfficiency: 5
      },
      preferences: {
        brand: 'NVIDIA',
        newOnly: true,
        maxPowerDraw: 350,
        quietOperation: false
      }
    };

    const enhancedGamingResponse = await fetch(`${BASE_URL}/recommendations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(enhancedGamingCriteria)
    });

    if (enhancedGamingResponse.ok) {
      const gamingData = await enhancedGamingResponse.json();
      console.log('✅ Enhanced gaming recommendations generated');
      console.log(`   Found ${gamingData.data.totalFound} GPUs in budget`);
      console.log(`   Top 3 recommendations:`);
      
      gamingData.data.recommendations.slice(0, 3).forEach((rec, i) => {
        console.log(`   ${i + 1}. ${rec.gpu.name} (Overall Score: ${rec.score.overall})`);
        console.log(`      Performance: ${rec.score.performance}, Value: ${rec.score.value}, Future-proofing: ${rec.score.futureProofing}`);
        console.log(`      Best Price: $${rec.bestPrice.price} at ${rec.bestPrice.retailer}`);
        console.log(`      Top Pros: ${rec.pros.slice(0, 2).join(', ')}`);
        if (rec.cons.length > 0) {
          console.log(`      Concerns: ${rec.cons.slice(0, 1).join(', ')}`);
        }
      });
    } else {
      console.log('❌ Enhanced gaming recommendations failed');
      const error = await enhancedGamingResponse.json();
      console.log('   Error:', error.error?.message);
    }

    // Test 2: Recommendation Wizard - Step by step
    console.log('\n2. Testing Recommendation Wizard...');
    
    // Step 1: Workload selection
    console.log('   Step 1: Workload Selection');
    const workloadResponse = await fetch(`${BASE_URL}/recommendations/wizard`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 'workload',
        data: { workload: 'content-creation' }
      })
    });

    if (workloadResponse.ok) {
      const workloadData = await workloadResponse.json();
      console.log('   ✅ Workload step completed');
      console.log(`      Configuration: ${workloadData.data.configuration.title}`);
      console.log(`      Key factors: ${workloadData.data.configuration.keyFactors.join(', ')}`);
      console.log(`      Progress: ${workloadData.data.progress}%`);
    }

    // Step 2: Budget selection
    console.log('   Step 2: Budget Selection');
    const budgetResponse = await fetch(`${BASE_URL}/recommendations/wizard`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 'budget',
        data: { 
          workload: 'content-creation',
          budget: { min: 800, max: 1800 }
        }
      })
    });

    if (budgetResponse.ok) {
      const budgetData = await budgetResponse.json();
      console.log('   ✅ Budget step completed');
      console.log(`      Category: ${budgetData.data.guidance.category}`);
      console.log(`      Expectations: ${budgetData.data.guidance.expectations}`);
      console.log(`      Progress: ${budgetData.data.progress}%`);
    }

    // Step 3: Generate final recommendations through wizard
    console.log('   Step 3: Generate Recommendations');
    const wizardFinalResponse = await fetch(`${BASE_URL}/recommendations/wizard`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 'generate',
        data: {
          workload: 'content-creation',
          budget: { min: 800, max: 1800 },
          performanceTargets: {},
          currentSystem: { psu: 850 },
          futureProofing: 4,
          priorities: { performance: 40, value: 25, futureProofing: 25, powerEfficiency: 10 },
          preferences: { brand: 'any', newOnly: false }
        }
      })
    });

    if (wizardFinalResponse.ok) {
      const wizardData = await wizardFinalResponse.json();
      console.log('   ✅ Wizard recommendations generated');
      console.log(`      Summary: ${wizardData.data.wizardSummary.workload} workload, ${wizardData.data.wizardSummary.budgetRange} budget`);
      console.log(`      Key priorities: ${wizardData.data.wizardSummary.keyPriorities.join(', ')}`);
      
      if (wizardData.data.budgetAnalysis) {
        console.log(`      Budget utilization: ${wizardData.data.budgetAnalysis.budgetUtilization}%`);
        console.log(`      Average recommended price: $${wizardData.data.budgetAnalysis.averageRecommendedPrice}`);
      }
    }

    // Test 3: AI/ML with specific requirements
    console.log('\n3. Testing AI/ML with Specific Requirements...');
    const aiCriteria = {
      workload: 'ai-ml',
      budget: { min: 1500, max: 3500 },
      futureProofing: 5,
      priorities: {
        performance: 60,
        value: 20,
        futureProofing: 15,
        powerEfficiency: 5
      },
      preferences: {
        brand: 'any',
        newOnly: true
      }
    };

    const aiResponse = await fetch(`${BASE_URL}/recommendations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(aiCriteria)
    });

    if (aiResponse.ok) {
      const aiData = await aiResponse.json();
      console.log('✅ AI/ML recommendations with priorities generated');
      console.log(`   Found ${aiData.data.totalFound} GPUs in budget`);
      
      if (aiData.data.recommendations[0]) {
        const top = aiData.data.recommendations[0];
        console.log(`   Top recommendation: ${top.gpu.name}`);
        console.log(`      Overall Score: ${top.score.overall} (Performance: ${top.score.performance}, Value: ${top.score.value})`);
        console.log(`      VRAM: ${top.gpu.specifications?.memory?.size || 'Unknown'}GB`);
        console.log(`      Key advantages: ${top.pros.slice(0, 3).join(', ')}`);
      }
    } else {
      console.log('❌ AI/ML recommendations failed');
    }

    // Test 4: Power-efficient gaming build
    console.log('\n4. Testing Power-Efficient Gaming Build...');
    const efficientCriteria = {
      workload: 'gaming',
      budget: { min: 300, max: 700 },
      performanceTargets: {
        resolution: '1080p',
        targetFPS: 60
      },
      priorities: {
        performance: 30,
        value: 30,
        futureProofing: 20,
        powerEfficiency: 20
      },
      preferences: {
        maxPowerDraw: 200,
        quietOperation: true
      }
    };

    const efficientResponse = await fetch(`${BASE_URL}/recommendations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(efficientCriteria)
    });

    if (efficientResponse.ok) {
      const efficientData = await efficientResponse.json();
      console.log('✅ Power-efficient gaming recommendations generated');
      
      if (efficientData.data.recommendations[0]) {
        const top = efficientData.data.recommendations[0];
        console.log(`   Top efficient option: ${top.gpu.name}`);
        console.log(`      Power efficiency focus - TDP: ${top.gpu.specifications?.power?.tdp || 'Unknown'}W`);
        console.log(`      Balanced scores - Performance: ${top.score.performance}, Efficiency consideration built-in`);
      }
    }

    // Test 5: Wizard validation testing
    console.log('\n5. Testing Wizard Input Validation...');
    const invalidWizardResponse = await fetch(`${BASE_URL}/recommendations/wizard`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        step: 'workload',
        data: { workload: 'invalid-workload' }
      })
    });

    if (!invalidWizardResponse.ok) {
      const errorData = await invalidWizardResponse.json();
      console.log('✅ Wizard validation working correctly');
      console.log(`   Error: ${errorData.error.message}`);
    } else {
      console.log('❌ Wizard validation not working');
    }

    // Test 6: Database status check
    console.log('\n6. Checking System Status...');
    const statsResponse = await fetch(`${BASE_URL}/gpus/seed`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ System status check:');
      console.log(`   GPU database: ${statsData.stats.total} GPUs available`);
      console.log(`   Manufacturers: ${statsData.stats.byManufacturer.map(m => `${m.manufacturer}(${m.count})`).join(', ')}`);
      console.log(`   Recently updated: ${statsData.stats.recentlyAdded} GPUs`);
    } else {
      console.log('❌ Could not check system status');
    }

    console.log('\n🎉 Enhanced recommendation system testing completed!');
    console.log('📊 Features tested:');
    console.log('   ✅ Enhanced scoring with user priorities');
    console.log('   ✅ Multi-step recommendation wizard');
    console.log('   ✅ Advanced filtering and preferences');
    console.log('   ✅ Budget analysis and guidance');
    console.log('   ✅ Workload-specific optimizations');
    console.log('   ✅ Comprehensive pros/cons generation');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the enhanced test
testRecommendationSystem();