-- CreateTable
CREATE TABLE "gpus" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "manufacturer" TEXT NOT NULL,
    "architecture" TEXT,
    "launchDate" TIMESTAMP(3),
    "originalMsrp" DECIMAL(10,2),
    "specifications" JSONB NOT NULL,
    "physicalSpecs" JSONB,
    "powerRequirements" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "gpus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gpu_pricing" (
    "id" TEXT NOT NULL,
    "gpuId" TEXT NOT NULL,
    "retailer" TEXT NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "availability" TEXT NOT NULL,
    "url" TEXT,
    "scrapedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gpu_pricing_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "performance_benchmarks" (
    "id" TEXT NOT NULL,
    "gpuId" TEXT NOT NULL,
    "benchmarkType" TEXT NOT NULL,
    "testName" TEXT NOT NULL,
    "resolution" TEXT NOT NULL,
    "settings" TEXT NOT NULL,
    "fps" DECIMAL(8,2),
    "score" INTEGER,
    "testDate" TIMESTAMP(3) NOT NULL,
    "source" TEXT NOT NULL,
    "benchmarkData" JSONB,

    CONSTRAINT "performance_benchmarks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "username" TEXT,
    "passwordHash" TEXT,
    "preferences" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_reviews" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "gpuId" TEXT NOT NULL,
    "rating" SMALLINT NOT NULL,
    "title" TEXT,
    "content" TEXT,
    "systemSpecs" JSONB,
    "performanceData" JSONB,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "isHidden" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_reviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_wishlists" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "gpuId" TEXT NOT NULL,
    "targetPrice" DECIMAL(10,2),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_wishlists_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "price_alerts" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "gpuId" TEXT NOT NULL,
    "targetPrice" DECIMAL(10,2) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "emailAlert" BOOLEAN NOT NULL DEFAULT true,
    "pushAlert" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "triggeredAt" TIMESTAMP(3),

    CONSTRAINT "price_alerts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "gpus_manufacturer_idx" ON "gpus"("manufacturer");

-- CreateIndex
CREATE INDEX "gpus_architecture_idx" ON "gpus"("architecture");

-- CreateIndex
CREATE INDEX "gpus_launchDate_idx" ON "gpus"("launchDate");

-- CreateIndex
CREATE INDEX "gpu_pricing_gpuId_idx" ON "gpu_pricing"("gpuId");

-- CreateIndex
CREATE INDEX "gpu_pricing_scrapedAt_idx" ON "gpu_pricing"("scrapedAt");

-- CreateIndex
CREATE UNIQUE INDEX "gpu_pricing_gpuId_retailer_scrapedAt_key" ON "gpu_pricing"("gpuId", "retailer", "scrapedAt");

-- CreateIndex
CREATE INDEX "performance_benchmarks_gpuId_idx" ON "performance_benchmarks"("gpuId");

-- CreateIndex
CREATE INDEX "performance_benchmarks_benchmarkType_idx" ON "performance_benchmarks"("benchmarkType");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- CreateIndex
CREATE INDEX "user_reviews_gpuId_idx" ON "user_reviews"("gpuId");

-- CreateIndex
CREATE INDEX "user_reviews_rating_idx" ON "user_reviews"("rating");

-- CreateIndex
CREATE UNIQUE INDEX "user_reviews_userId_gpuId_key" ON "user_reviews"("userId", "gpuId");

-- CreateIndex
CREATE INDEX "user_wishlists_userId_idx" ON "user_wishlists"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "user_wishlists_userId_gpuId_key" ON "user_wishlists"("userId", "gpuId");

-- CreateIndex
CREATE INDEX "price_alerts_userId_idx" ON "price_alerts"("userId");

-- CreateIndex
CREATE INDEX "price_alerts_gpuId_idx" ON "price_alerts"("gpuId");

-- CreateIndex
CREATE INDEX "price_alerts_isActive_idx" ON "price_alerts"("isActive");

-- AddForeignKey
ALTER TABLE "gpu_pricing" ADD CONSTRAINT "gpu_pricing_gpuId_fkey" FOREIGN KEY ("gpuId") REFERENCES "gpus"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "performance_benchmarks" ADD CONSTRAINT "performance_benchmarks_gpuId_fkey" FOREIGN KEY ("gpuId") REFERENCES "gpus"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_reviews" ADD CONSTRAINT "user_reviews_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_reviews" ADD CONSTRAINT "user_reviews_gpuId_fkey" FOREIGN KEY ("gpuId") REFERENCES "gpus"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_wishlists" ADD CONSTRAINT "user_wishlists_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_wishlists" ADD CONSTRAINT "user_wishlists_gpuId_fkey" FOREIGN KEY ("gpuId") REFERENCES "gpus"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "price_alerts" ADD CONSTRAINT "price_alerts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
