// GPULabs Database Schema
// Comprehensive GPU recommendation platform database

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Core GPU model with comprehensive specifications
model GPU {
  id                String   @id @default(cuid())
  name              String
  manufacturer      String   // NVIDIA, AMD, Intel
  architecture      String?  // Ada Lovelace, RDNA 3, etc.
  launchDate        DateTime?
  originalMsrp      Decimal? @db.Decimal(10, 2)
  
  // JSONB fields for flexible specification storage
  specifications    Json     // Core specs: memory, cores, clocks, etc.
  physicalSpecs     Json?    // Dimensions, weight, cooling
  powerRequirements Json?    // TDP, connectors, recommended PSU
  
  // Relationships
  pricing           GPUPricing[]
  reviews           UserReview[]
  wishlists         UserWishlist[]
  benchmarks        PerformanceBenchmark[]
  overclockingData  OverclockingData[]
  affiliateClicks   AffiliateClick[]
  affiliateConversions AffiliateConversion[] @relation("GPUConversions")
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@index([manufacturer])
  @@index([architecture])
  @@index([launchDate])
  @@map("gpus")
}

// Time-series pricing data from multiple retailers
model GPUPricing {
  id           String   @id @default(cuid())
  gpuId        String
  retailer     String
  price        Decimal  @db.Decimal(10, 2)
  availability String   // in-stock, limited, out-of-stock
  url          String?
  scrapedAt    DateTime @default(now())
  
  // Relationships
  gpu          GPU      @relation(fields: [gpuId], references: [id], onDelete: Cascade)
  
  @@unique([gpuId, retailer, scrapedAt])
  @@index([gpuId])
  @@index([scrapedAt])
  @@map("gpu_pricing")
}

// Performance benchmark data
model PerformanceBenchmark {
  id            String   @id @default(cuid())
  gpuId         String
  benchmarkType String   // gaming, professional, synthetic
  testName      String   // 3DMark, Cyberpunk 2077, etc.
  resolution    String   // 1080p, 1440p, 4K
  settings      String   // Ultra, High, Medium, Low
  fps           Decimal? @db.Decimal(8, 2)
  score         Int?
  testDate      DateTime
  source        String   // TechPowerUp, Tom's Hardware, etc.
  
  // Additional benchmark data
  benchmarkData Json?    // Ray tracing, DLSS, power consumption
  
  // Relationships
  gpu           GPU      @relation(fields: [gpuId], references: [id], onDelete: Cascade)
  
  @@index([gpuId])
  @@index([benchmarkType])
  @@map("performance_benchmarks")
}

// Overclocking and thermal analysis data
model OverclockingData {
  id                String   @id @default(cuid())
  gpuId             String
  
  // Overclocking potential
  coreClockOffset   Int?     // MHz offset from base clock
  memoryClockOffset Int?     // MHz offset from base memory clock
  powerLimit        Int?     // Percentage increase in power limit
  voltageOffset     Int?     // mV offset from stock voltage
  
  // Performance gains
  performanceGain   Decimal? @db.Decimal(5, 2) // Percentage performance increase
  stabilityRating   Int?     // 1-5 rating for overclock stability
  
  // Thermal data
  stockTemperature  Int?     // Celsius at stock settings
  ocTemperature     Int?     // Celsius when overclocked
  maxTemperature    Int?     // Maximum safe temperature
  thermalThrottling Boolean? // Whether thermal throttling occurs
  
  // Power consumption
  stockPower        Int?     // Watts at stock settings
  ocPower           Int?     // Watts when overclocked
  powerEfficiency   Decimal? @db.Decimal(5, 2) // Performance per watt ratio
  
  // Test conditions and metadata
  coolingType       String?  // Air, AIO, Custom Loop, etc.
  ambientTemp       Int?     // Room temperature during testing
  testDuration      Int?     // Minutes of stress testing
  source            String   // Source of the overclocking data
  testDate          DateTime @default(now())
  
  // Additional data
  notes             String?  // Additional notes about the overclock
  overclockData     Json?    // Additional structured data
  
  // Relationships
  gpu               GPU      @relation(fields: [gpuId], references: [id], onDelete: Cascade)
  
  @@index([gpuId])
  @@index([stabilityRating])
  @@map("overclocking_data")
}

// User accounts and authentication (NextAuth.js compatible)
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  username      String?   @unique
  password      String?   // For credentials provider
  
  // User preferences for recommendations
  preferences   Json?     // workload, budget, performance targets
  
  // NextAuth.js relationships
  accounts      Account[]
  sessions      Session[]
  
  // App-specific relationships
  reviews       UserReview[]
  wishlists     UserWishlist[]
  priceAlerts   PriceAlert[]
  affiliateClicks AffiliateClick[] @relation("UserAffiliateClicks")
  affiliateConversions AffiliateConversion[] @relation("UserConversions")
  
  // Community relationships
  forumThreads     ForumThread[]    @relation("ThreadAuthor")
  lastRepliedThreads ForumThread[]  @relation("ThreadLastReplier")
  forumReplies     ForumReply[]     @relation("ReplyAuthor")
  buildShowcases   BuildShowcase[]  @relation("BuildAuthor")
  buildComments    BuildComment[]   @relation("BuildCommentAuthor")
  buildLikes       BuildLike[]      @relation("BuildLiker")
  reputation       UserReputation?  @relation("UserReputation")
  
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  @@map("users")
}

// NextAuth.js Account model
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// NextAuth.js Session model
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// NextAuth.js VerificationToken model
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// User reviews and ratings
model UserReview {
  id              String   @id @default(cuid())
  userId          String
  gpuId           String
  rating          Int      @db.SmallInt // 1-5 stars
  title           String?
  content         String?
  
  // Technical details from user
  systemSpecs     Json?    // User's system configuration
  performanceData Json?    // User's real-world performance results
  
  // Moderation
  isVerified      Boolean  @default(false)
  isHidden        Boolean  @default(false)
  
  // Relationships
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  gpu             GPU      @relation(fields: [gpuId], references: [id], onDelete: Cascade)
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@unique([userId, gpuId])
  @@index([gpuId])
  @@index([rating])
  @@map("user_reviews")
}

// User wishlist for tracking GPUs of interest
model UserWishlist {
  id          String   @id @default(cuid())
  userId      String
  gpuId       String
  targetPrice Decimal? @db.Decimal(10, 2)
  notes       String?
  
  // Relationships
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  gpu         GPU      @relation(fields: [gpuId], references: [id], onDelete: Cascade)
  
  createdAt   DateTime @default(now())
  
  @@unique([userId, gpuId])
  @@index([userId])
  @@map("user_wishlists")
}

// Price alerts for users
model PriceAlert {
  id          String   @id @default(cuid())
  userId      String
  gpuId       String
  targetPrice Decimal  @db.Decimal(10, 2)
  isActive    Boolean  @default(true)
  
  // Alert preferences
  emailAlert  Boolean  @default(true)
  pushAlert   Boolean  @default(false)
  
  // Relationships
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt   DateTime @default(now())
  triggeredAt DateTime?
  
  @@index([userId])
  @@index([gpuId])
  @@index([isActive])
  @@map("price_alerts")
}

// Community Forum Models

// Forum categories for organizing discussions
model ForumCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  slug        String   @unique
  color       String?  // Hex color for category styling
  icon        String?  // Icon name for category
  sortOrder   Int      @default(0)
  isActive    Boolean  @default(true)
  
  // Moderation
  moderatorIds String[] // Array of user IDs who can moderate this category
  
  // Relationships
  threads     ForumThread[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([sortOrder])
  @@index([isActive])
  @@map("forum_categories")
}

// Forum discussion threads
model ForumThread {
  id           String   @id @default(cuid())
  categoryId   String
  authorId     String
  title        String
  slug         String   @unique
  content      String   @db.Text
  
  // Thread metadata
  isPinned     Boolean  @default(false)
  isLocked     Boolean  @default(false)
  isHidden     Boolean  @default(false)
  viewCount    Int      @default(0)
  replyCount   Int      @default(0)
  
  // Last activity tracking
  lastReplyAt  DateTime?
  lastReplyBy  String?
  
  // Tags for better organization
  tags         String[] // Array of tag strings
  
  // Relationships
  category     ForumCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  author       User         @relation("ThreadAuthor", fields: [authorId], references: [id], onDelete: Cascade)
  lastReplier  User?        @relation("ThreadLastReplier", fields: [lastReplyBy], references: [id])
  replies      ForumReply[]
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@index([categoryId])
  @@index([authorId])
  @@index([isPinned])
  @@index([lastReplyAt])
  @@index([createdAt])
  @@map("forum_threads")
}

// Forum replies to threads
model ForumReply {
  id         String   @id @default(cuid())
  threadId   String
  authorId   String
  content    String   @db.Text
  
  // Reply metadata
  isHidden   Boolean  @default(false)
  editCount  Int      @default(0)
  lastEditAt DateTime?
  
  // Relationships
  thread     ForumThread @relation(fields: [threadId], references: [id], onDelete: Cascade)
  author     User        @relation("ReplyAuthor", fields: [authorId], references: [id], onDelete: Cascade)
  
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  @@index([threadId])
  @@index([authorId])
  @@index([createdAt])
  @@map("forum_replies")
}

// Build showcase for users to display their systems
model BuildShowcase {
  id            String   @id @default(cuid())
  authorId      String
  title         String
  description   String?  @db.Text
  
  // Build specifications
  buildSpecs    Json     // Complete system specifications
  totalCost     Decimal? @db.Decimal(12, 2)
  buildDate     DateTime?
  
  // Performance data
  benchmarkData Json?    // User's benchmark results
  
  // Media
  images        String[] // Array of image URLs
  videoUrl      String?  // Optional build video
  
  // Engagement
  viewCount     Int      @default(0)
  likeCount     Int      @default(0)
  
  // Moderation
  isPublished   Boolean  @default(true)
  isFeatured    Boolean  @default(false)
  isHidden      Boolean  @default(false)
  
  // Tags for categorization
  tags          String[] // gaming, workstation, budget, etc.
  
  // Relationships
  author        User           @relation("BuildAuthor", fields: [authorId], references: [id], onDelete: Cascade)
  comments      BuildComment[]
  likes         BuildLike[]
  
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@index([authorId])
  @@index([isPublished])
  @@index([isFeatured])
  @@index([createdAt])
  @@index([likeCount])
  @@map("build_showcases")
}

// Comments on build showcases
model BuildComment {
  id        String   @id @default(cuid())
  buildId   String
  authorId  String
  content   String   @db.Text
  
  // Moderation
  isHidden  Boolean  @default(false)
  
  // Relationships
  build     BuildShowcase @relation(fields: [buildId], references: [id], onDelete: Cascade)
  author    User          @relation("BuildCommentAuthor", fields: [authorId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@index([buildId])
  @@index([authorId])
  @@index([createdAt])
  @@map("build_comments")
}

// Likes on build showcases
model BuildLike {
  id       String @id @default(cuid())
  buildId  String
  userId   String
  
  // Relationships
  build    BuildShowcase @relation(fields: [buildId], references: [id], onDelete: Cascade)
  user     User          @relation("BuildLiker", fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  
  @@unique([buildId, userId])
  @@index([buildId])
  @@index([userId])
  @@map("build_likes")
}

// User reputation system
model UserReputation {
  id           String   @id @default(cuid())
  userId       String   @unique
  
  // Reputation scores
  totalScore   Int      @default(0)
  forumScore   Int      @default(0)   // From forum participation
  reviewScore  Int      @default(0)   // From helpful reviews
  buildScore   Int      @default(0)   // From build showcases
  
  // Activity metrics
  postsCount   Int      @default(0)
  likesGiven   Int      @default(0)
  likesReceived Int     @default(0)
  
  // Badges and achievements
  badges       String[] // Array of badge identifiers
  
  // Relationships
  user         User     @relation("UserReputation", fields: [userId], references: [id], onDelete: Cascade)
  
  updatedAt    DateTime @updatedAt
  
  @@index([totalScore])
  @@map("user_reputations")
}

// Affiliate click tracking for revenue analytics
model AffiliateClick {
  id          String   @id @default(cuid())
  retailer    String   // Amazon, Newegg, Best Buy, etc.
  gpuId       String
  userId      String?  // Optional user tracking
  sessionId   String   // Session-based tracking
  userAgent   String?
  referrer    String?
  ipAddress   String?
  clickedAt   DateTime @default(now())

  // Relationships
  gpu         GPU      @relation(fields: [gpuId], references: [id], onDelete: Cascade)
  user        User?    @relation("UserAffiliateClicks", fields: [userId], references: [id], onDelete: SetNull)

  @@index([retailer])
  @@index([gpuId])
  @@index([clickedAt])
  @@index([sessionId])
  @@map("affiliate_clicks")
}

// Affiliate conversion tracking (for future implementation)
model AffiliateConversion {
  id              String   @id @default(cuid())
  clickId         String   @unique
  retailer        String
  gpuId           String
  userId          String?
  orderValue      Decimal  @db.Decimal(10, 2)
  commission      Decimal  @db.Decimal(10, 2)
  conversionDate  DateTime @default(now())

  // Relationships
  gpu             GPU      @relation("GPUConversions", fields: [gpuId], references: [id], onDelete: Cascade)
  user            User?    @relation("UserConversions", fields: [userId], references: [id], onDelete: SetNull)

  @@index([retailer])
  @@index([conversionDate])
  @@map("affiliate_conversions")
}
