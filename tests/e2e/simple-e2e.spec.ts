// Simple E2E test to check if Playwright setup works
import { test, expect } from '@playwright/test';

test.describe('Simple E2E Tests', () => {
  test('should be able to make a simple HTTP request', async ({ page }) => {
    // Test if we can make a basic request
    const response = await page.request.get('https://httpbin.org/json');
    expect(response.ok()).toBeTruthy();
  });

  test('should be able to navigate to a simple page', async ({ page }) => {
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example Domain/);
  });
});