import { test, expect } from '@playwright/test';

test.describe('GPU Listing Page', () => {
  test('should display GPU list with filtering', async ({ page }) => {
    await page.goto('/gpus');
    
    // Wait for GPUs to load
    await page.waitForSelector('[data-testid="gpu-card"]', { timeout: 10000 });
    
    // Check that GPU cards are displayed
    const gpuCards = page.locator('[data-testid="gpu-card"]');
    await expect(gpuCards).toHaveCount({ min: 1 });
    
    // Check that each GPU card has essential information
    const firstCard = gpuCards.first();
    await expect(firstCard.locator('h3')).toBeVisible(); // GPU name
    await expect(firstCard.locator('text=/\\$/')).toBeVisible(); // Price
  });

  test('should filter GPUs by manufacturer', async ({ page }) => {
    await page.goto('/gpus');
    
    // Wait for GPUs to load
    await page.waitForSelector('[data-testid="gpu-card"]', { timeout: 10000 });
    
    // Get initial count
    const initialCards = await page.locator('[data-testid="gpu-card"]').count();
    
    // Apply NVIDIA filter if available
    const nvidiaFilter = page.locator('input[value="NVIDIA"]');
    if (await nvidiaFilter.isVisible()) {
      await nvidiaFilter.check();
      
      // Wait for filtering to complete
      await page.waitForTimeout(1000);
      
      // Check that results are filtered
      const filteredCards = await page.locator('[data-testid="gpu-card"]').count();
      expect(filteredCards).toBeLessThanOrEqual(initialCards);
      
      // Verify all visible cards are NVIDIA
      const gpuNames = await page.locator('[data-testid="gpu-card"] h3').allTextContents();
      gpuNames.forEach(name => {
        expect(name.toLowerCase()).toContain('rtx');
      });
    }
  });

  test('should sort GPUs by price', async ({ page }) => {
    await page.goto('/gpus');
    
    // Wait for GPUs to load
    await page.waitForSelector('[data-testid="gpu-card"]', { timeout: 10000 });
    
    // Find and click price sort option
    const sortSelect = page.locator('select[data-testid="sort-select"]');
    if (await sortSelect.isVisible()) {
      await sortSelect.selectOption('price-asc');
      
      // Wait for sorting to complete
      await page.waitForTimeout(1000);
      
      // Get prices and verify they're sorted
      const priceElements = page.locator('[data-testid="gpu-card"] [data-testid="price"]');
      const prices = await priceElements.allTextContents();
      
      if (prices.length > 1) {
        const numericPrices = prices.map(price => 
          parseFloat(price.replace(/[^0-9.]/g, ''))
        ).filter(price => !isNaN(price));
        
        // Check if sorted ascending
        for (let i = 1; i < numericPrices.length; i++) {
          expect(numericPrices[i]).toBeGreaterThanOrEqual(numericPrices[i - 1]);
        }
      }
    }
  });

  test('should navigate to GPU detail page', async ({ page }) => {
    await page.goto('/gpus');
    
    // Wait for GPUs to load
    await page.waitForSelector('[data-testid="gpu-card"]', { timeout: 10000 });
    
    // Click on first GPU card
    const firstCard = page.locator('[data-testid="gpu-card"]').first();
    await firstCard.click();
    
    // Should navigate to detail page
    await expect(page).toHaveURL(/.*gpus\/[^\/]+$/);
    
    // Should show detailed information
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="specifications"]')).toBeVisible();
  });
});