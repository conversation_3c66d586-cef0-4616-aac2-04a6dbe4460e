import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load homepage successfully', async ({ page }) => {
    await page.goto('/');
    
    // Check that the page loads
    await expect(page).toHaveTitle(/GPUlabs/i);
    
    // Check for main navigation elements
    await expect(page.locator('nav')).toBeVisible();
    
    // Check for key sections
    await expect(page.getByText(/GPU Recommendation/i)).toBeVisible();
  });

  test('should have working navigation links', async ({ page }) => {
    await page.goto('/');
    
    // Test navigation to GPUs page
    await page.click('text=GPUs');
    await expect(page).toHaveURL(/.*gpus/);
    
    // Go back to home
    await page.goto('/');
    
    // Test navigation to Recommendations page
    await page.click('text=Recommendations');
    await expect(page).toHaveURL(/.*recommendations/);
    
    // Test navigation to Compatibility page
    await page.goto('/');
    await page.click('text=Compatibility');
    await expect(page).toHaveURL(/.*compatibility/);
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check that mobile navigation works
    await expect(page.locator('nav')).toBeVisible();
    
    // Check that content is properly displayed on mobile
    await expect(page.getByText(/GPU Recommendation/i)).toBeVisible();
  });
});