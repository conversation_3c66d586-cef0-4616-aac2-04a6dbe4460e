import { test, expect } from '@playwright/test';

test.describe('Recommendation Wizard', () => {
  test('should complete recommendation wizard flow', async ({ page }) => {
    await page.goto('/recommendations');
    
    // Step 1: Workload Selection
    await expect(page.getByText(/What will you primarily use/i)).toBeVisible();
    
    // Select gaming workload
    await page.click('[data-testid="workload-gaming"]');
    await page.click('button:has-text("Next")');
    
    // Step 2: Performance Requirements
    await expect(page.getByText(/performance requirements/i)).toBeVisible();
    
    // Select 1440p gaming
    await page.click('[data-testid="resolution-1440p"]');
    await page.click('[data-testid="framerate-60fps"]');
    await page.click('button:has-text("Next")');
    
    // Step 3: Budget
    await expect(page.getByText(/budget/i)).toBeVisible();
    
    // Set budget range
    const budgetSlider = page.locator('input[type="range"]');
    await budgetSlider.fill('800');
    await page.click('button:has-text("Next")');
    
    // Step 4: System Information
    await expect(page.getByText(/system information/i)).toBeVisible();
    
    // Fill system specs
    await page.fill('[data-testid="cpu-input"]', 'Intel i5-12600K');
    await page.fill('[data-testid="psu-input"]', '750');
    await page.click('button:has-text("Get Recommendations")');
    
    // Should show results
    await expect(page.locator('[data-testid="recommendation-results"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('[data-testid="recommended-gpu"]')).toHaveCount({ min: 1 });
  });

  test('should validate required fields', async ({ page }) => {
    await page.goto('/recommendations');
    
    // Try to proceed without selecting workload
    await page.click('button:has-text("Next")');
    
    // Should show validation error
    await expect(page.getByText(/please select/i)).toBeVisible();
  });

  test('should allow going back to previous steps', async ({ page }) => {
    await page.goto('/recommendations');
    
    // Complete first step
    await page.click('[data-testid="workload-gaming"]');
    await page.click('button:has-text("Next")');
    
    // Go back
    await page.click('button:has-text("Back")');
    
    // Should be back on workload selection
    await expect(page.getByText(/What will you primarily use/i)).toBeVisible();
  });

  test('should show different options for different workloads', async ({ page }) => {
    await page.goto('/recommendations');
    
    // Test gaming workload
    await page.click('[data-testid="workload-gaming"]');
    await page.click('button:has-text("Next")');
    
    // Should show gaming-specific options
    await expect(page.locator('[data-testid="resolution-1080p"]')).toBeVisible();
    await expect(page.locator('[data-testid="resolution-1440p"]')).toBeVisible();
    
    // Go back and try content creation
    await page.click('button:has-text("Back")');
    await page.click('[data-testid="workload-content-creation"]');
    await page.click('button:has-text("Next")');
    
    // Should show content creation specific options
    await expect(page.getByText(/video editing/i)).toBeVisible();
  });
});