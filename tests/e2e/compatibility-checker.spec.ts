import { test, expect } from '@playwright/test';

test.describe('Compatibility Checker', () => {
  test('should check GPU compatibility with system', async ({ page }) => {
    await page.goto('/compatibility');
    
    // Fill in system specifications
    await page.fill('[data-testid="cpu-input"]', 'Intel i7-12700K');
    await page.fill('[data-testid="motherboard-input"]', 'ASUS ROG Strix Z690-E');
    await page.fill('[data-testid="psu-wattage"]', '850');
    await page.fill('[data-testid="case-clearance"]', '320');
    
    // Select a GPU to check
    await page.click('[data-testid="gpu-select"]');
    await page.click('text=RTX 4070');
    
    // Run compatibility check
    await page.click('button:has-text("Check Compatibility")');
    
    // Should show compatibility results
    await expect(page.locator('[data-testid="compatibility-results"]')).toBeVisible({ timeout: 5000 });
    
    // Should show power compatibility
    await expect(page.locator('[data-testid="power-compatibility"]')).toBeVisible();
    
    // Should show physical compatibility
    await expect(page.locator('[data-testid="physical-compatibility"]')).toBeVisible();
    
    // Should show overall compatibility status
    const compatibilityStatus = page.locator('[data-testid="overall-compatibility"]');
    await expect(compatibilityStatus).toBeVisible();
    
    // Status should be one of: compatible, warning, or incompatible
    const statusText = await compatibilityStatus.textContent();
    expect(['compatible', 'warning', 'incompatible'].some(status => 
      statusText?.toLowerCase().includes(status)
    )).toBeTruthy();
  });

  test('should show warning for insufficient power supply', async ({ page }) => {
    await page.goto('/compatibility');
    
    // Fill in system with low wattage PSU
    await page.fill('[data-testid="cpu-input"]', 'Intel i9-13900K');
    await page.fill('[data-testid="psu-wattage"]', '450'); // Low wattage
    
    // Select high-power GPU
    await page.click('[data-testid="gpu-select"]');
    await page.click('text=RTX 4090');
    
    // Run compatibility check
    await page.click('button:has-text("Check Compatibility")');
    
    // Should show warning or incompatible status
    await expect(page.locator('[data-testid="compatibility-results"]')).toBeVisible({ timeout: 5000 });
    
    const powerStatus = page.locator('[data-testid="power-compatibility"]');
    const statusText = await powerStatus.textContent();
    expect(['warning', 'incompatible'].some(status => 
      statusText?.toLowerCase().includes(status)
    )).toBeTruthy();
  });

  test('should validate required fields', async ({ page }) => {
    await page.goto('/compatibility');
    
    // Try to check compatibility without filling required fields
    await page.click('button:has-text("Check Compatibility")');
    
    // Should show validation errors
    await expect(page.getByText(/required/i)).toBeVisible();
  });

  test('should show recommendations for incompatible systems', async ({ page }) => {
    await page.goto('/compatibility');
    
    // Fill in system with very low specs
    await page.fill('[data-testid="cpu-input"]', 'Intel i3-10100');
    await page.fill('[data-testid="psu-wattage"]', '300');
    await page.fill('[data-testid="case-clearance"]', '150');
    
    // Select high-end GPU
    await page.click('[data-testid="gpu-select"]');
    await page.click('text=RTX 4090');
    
    // Run compatibility check
    await page.click('button:has-text("Check Compatibility")');
    
    // Should show compatibility results
    await expect(page.locator('[data-testid="compatibility-results"]')).toBeVisible({ timeout: 5000 });
    
    // Should show alternative recommendations
    await expect(page.locator('[data-testid="alternative-recommendations"]')).toBeVisible();
  });
});