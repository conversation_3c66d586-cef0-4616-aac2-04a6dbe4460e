// Integration tests for service layer functionality
import { GPUService } from '../../src/lib/gpu-service';
import { RecommendationEngine } from '../../src/lib/recommendation-engine';
import { CompatibilityService } from '../../src/lib/compatibility-service';
import { pricingService, EnhancedPricingService } from '../../src/lib/pricing-service';

// Mock Prisma client
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    gPU: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    gPUPricing: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    gPUPricing: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    priceAlert: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    gPU: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    gPUPricing: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    priceAlert: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  })),
}));

// Mock Redis
jest.mock('ioredis', () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockResolvedValue(null),
    setex: jest.fn().mockResolvedValue('OK'),
    del: jest.fn().mockResolvedValue(1),
    publish: jest.fn().mockResolvedValue(1),
  })),
}));

describe('Service Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('GPU Service Integration', () => {
    test('should integrate with recommendation engine', async () => {
      // Mock GPU data
      const mockGPUs = [
        {
          id: '1',
          name: 'RTX 4070',
          manufacturer: 'NVIDIA',
          architecture: 'Ada Lovelace',
          launchDate: new Date('2023-04-13'),
          specifications: {
            baseClock: 1920,
            boostClock: 2475,
            memorySize: 12,
            memoryType: 'GDDR6X',
            memoryBandwidth: 504.2,
            cudaCores: 5888,
            rtCores: 46,
            tensorCores: 184,
            tdp: 200,
            length: 267,
            width: 112,
            height: 40,
            displayOutputs: ['3x DisplayPort 1.4a', '1x HDMI 2.1'],
            pciSlots: 2,
            powerConnectors: ['1x 12-pin']
          },
          currentPrice: 599.99,
          msrp: 599.99,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const { prisma } = require('../../src/lib/prisma');
      prisma.gPU.findMany.mockResolvedValue(mockGPUs);

      // Test integration between services
      const gpus = await GPUService.getAllGPUs({});
      expect(gpus.gpus).toHaveLength(1);

      const recommendationEngine = new RecommendationEngine();
      const recommendations = await recommendationEngine.getRecommendations({
        workload: 'gaming',
        budget: { min: 500, max: 700 },
        performanceTargets: { resolution: '1440p', targetFPS: 60 },
        systemSpecs: { cpu: 'Intel i5-12600K', psuWattage: 750 }
      });

      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
    });

    test('should integrate with compatibility service', async () => {
      const mockGPU = {
        id: '1',
        name: 'RTX 4070',
        manufacturer: 'NVIDIA',
        specifications: {
          tdp: 200,
          length: 267,
          width: 112,
          height: 40,
          pciSlots: 2,
          powerConnectors: ['1x 12-pin']
        }
      };

      const { prisma } = require('../../src/lib/prisma');
      prisma.gPU.findUnique.mockResolvedValue(mockGPU);

      const gpu = await GPUService.getGPUById('1');
      expect(gpu).toBeDefined();

      const compatibility = await CompatibilityService.checkCompatibility(mockGPU, {
        cpu: { model: 'Intel i7-12700K', cores: 12, threads: 20 },
        motherboard: 'ASUS ROG Strix Z690-E',
        psu: { wattage: 850, efficiency: '80+ Gold' },
        case: { clearance: 320, width: 200 }
      });

      expect(compatibility).toHaveProperty('overall');
      expect(compatibility).toHaveProperty('power');
      expect(compatibility).toHaveProperty('physical');
    });

    test('should integrate with pricing service', async () => {
      const mockPricing = [
        {
          id: '1',
          gpuId: '1',
          retailer: 'Amazon',
          price: { toNumber: () => 599.99 },
          url: 'https://amazon.com/gpu',
          availability: 'in-stock',
          scrapedAt: new Date(),
          createdAt: new Date()
        }
      ];

      // Mock both possible Prisma instances
      const { prisma } = require('../../src/lib/prisma');
      prisma.gPUPricing.findMany.mockResolvedValue(mockPricing);

      // Also mock the PrismaClient instance used by pricing service
      const { PrismaClient } = require('@prisma/client');
      const mockInstance = new PrismaClient();
      mockInstance.gPUPricing.findMany.mockResolvedValue(mockPricing);

      const pricing = await pricingService.getCurrentPrices('1');
      expect(Array.isArray(pricing)).toBe(true);
      
      if (pricing.length > 0) {
        expect(pricing[0]).toHaveProperty('retailer');
        expect(pricing[0]).toHaveProperty('price');
        expect(pricing[0]).toHaveProperty('availability');
      }
    });
  });

  describe('Cross-Service Data Flow', () => {
    test('should handle complete recommendation workflow', async () => {
      // Mock all necessary data
      const mockGPUs = [
        {
          id: '1',
          name: 'RTX 4070',
          manufacturer: 'NVIDIA',
          architecture: 'Ada Lovelace',
          specifications: {
            baseClock: 1920,
            boostClock: 2475,
            memorySize: 12,
            memoryType: 'GDDR6X',
            cudaCores: 5888,
            tdp: 200,
            length: 267
          },
          currentPrice: 599.99,
          msrp: 599.99
        },
        {
          id: '2',
          name: 'RTX 4060',
          manufacturer: 'NVIDIA',
          architecture: 'Ada Lovelace',
          specifications: {
            baseClock: 1830,
            boostClock: 2460,
            memorySize: 8,
            memoryType: 'GDDR6',
            cudaCores: 3072,
            tdp: 115,
            length: 244
          },
          currentPrice: 299.99,
          msrp: 299.99
        }
      ];

      const { prisma } = require('../../src/lib/prisma');
      prisma.gPU.findMany.mockResolvedValue(mockGPUs);

      // Test complete workflow
      const recommendationEngine = new RecommendationEngine();
      const recommendations = await recommendationEngine.getRecommendations({
        workload: 'gaming',
        budget: { min: 250, max: 650 },
        performanceTargets: { resolution: '1440p', targetFPS: 60 },
        systemSpecs: { cpu: 'Intel i5-12600K', psuWattage: 650 }
      });

      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      
      if (recommendations.length > 0) {
        const topRecommendation = recommendations[0];
        expect(topRecommendation).toHaveProperty('gpu');
        expect(topRecommendation).toHaveProperty('score');
        expect(topRecommendation).toHaveProperty('reasoning');

        // Test compatibility for recommended GPU
        const compatibility = await CompatibilityService.checkCompatibility(topRecommendation.gpu, {
          cpu: { model: 'Intel i5-12600K', cores: 12, threads: 20 },
          motherboard: 'Generic ATX',
          psu: { wattage: 650, efficiency: '80+ Bronze' },
          case: { clearance: 300, width: 200 }
        });

        expect(compatibility).toHaveProperty('overall');
        expect(['compatible', 'warning', 'incompatible']).toContain(compatibility.overall);
      }
    });

    test('should handle error propagation between services', async () => {
      const { prisma } = require('../../src/lib/prisma');
      prisma.gPU.findMany.mockRejectedValue(new Error('Database connection failed'));

      await expect(GPUService.getAllGPUs({})).rejects.toThrow('Database connection failed');
    });
  });

  describe('Performance Integration', () => {
    test('should handle large datasets efficiently', async () => {
      // Create mock data for 100 GPUs
      const mockGPUs = Array.from({ length: 100 }, (_, i) => ({
        id: `gpu-${i}`,
        name: `GPU ${i}`,
        manufacturer: i % 2 === 0 ? 'NVIDIA' : 'AMD',
        architecture: 'Test Architecture',
        specifications: {
          baseClock: 1500 + i * 10,
          boostClock: 1800 + i * 10,
          memorySize: 8 + (i % 4) * 4,
          memoryType: 'GDDR6',
          cudaCores: 2000 + i * 50,
          tdp: 150 + i * 2,
          length: 250 + i
        },
        currentPrice: 300 + i * 10,
        msrp: 300 + i * 10
      }));

      const { prisma } = require('../../src/lib/prisma');
      prisma.gPU.findMany.mockResolvedValue(mockGPUs);

      const startTime = Date.now();
      const result = await GPUService.getAllGPUs({});
      const endTime = Date.now();

      expect(result.gpus).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    test('should handle concurrent requests', async () => {
      const mockGPU = {
        id: '1',
        name: 'RTX 4070',
        originalMsrp: null,
        specifications: { tdp: 200, length: 267 }
      };

      const { prisma } = require('../../src/lib/prisma');
      prisma.gPU.findUnique.mockResolvedValue(mockGPU);

      // Make multiple concurrent requests
      const promises = Array.from({ length: 10 }, () => 
        GPUService.getGPUById('1')
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toEqual(mockGPU);
      });
    });
  });
});