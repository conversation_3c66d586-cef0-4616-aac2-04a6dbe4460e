import { render, act } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import HomePage from '../../src/app/page';
import { GPUCard } from '../../src/components/ui/GPUCard';
import { RecommendationWizard } from '../../src/components/recommendations/RecommendationWizard';
import CompatibilityChecker from '../../src/components/compatibility/CompatibilityChecker';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
    has: jest.fn(),
    getAll: jest.fn(),
  }),
  usePathname: () => '/',
  useParams: () => ({}),
}));

// Mock session
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: null,
    status: 'unauthenticated',
  }),
}));

describe('Accessibility Tests', () => {
  test('HomePage should not have accessibility violations', async () => {
    let container: HTMLElement;
    await act(async () => {
      const renderResult = render(<HomePage />);
      container = renderResult.container;
    });

    const results = await act(async () => {
      return await axe(container!);
    });

    expect(results).toHaveNoViolations();
  });

  test('GPUCard should not have accessibility violations', async () => {
    const mockGPU = {
      id: '1',
      name: 'RTX 4070',
      manufacturer: 'NVIDIA',
      architecture: 'Ada Lovelace',
      launchDate: new Date('2023-04-13'),
      specifications: {
        baseClock: 1920,
        boostClock: 2475,
        memorySize: 12,
        memoryType: 'GDDR6X',
        memoryBandwidth: 504.2,
        cudaCores: 5888,
        rtCores: 46,
        tensorCores: 184,
        tdp: 200,
        length: 267,
        width: 112,
        height: 40,
        displayOutputs: ['3x DisplayPort 1.4a', '1x HDMI 2.1'],
        pciSlots: 2,
        powerConnectors: ['1x 12-pin']
      },
      currentPrice: 599.99,
      msrp: 599.99,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const { container } = render(<GPUCard gpu={mockGPU} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('RecommendationWizard should not have accessibility violations', async () => {
    let container: HTMLElement;
    await act(async () => {
      const renderResult = render(<RecommendationWizard />);
      container = renderResult.container;
    });

    const results = await act(async () => {
      return await axe(container!);
    });

    expect(results).toHaveNoViolations();
  });

  test('CompatibilityChecker should not have accessibility violations', async () => {
    let container: HTMLElement;
    await act(async () => {
      const renderResult = render(<CompatibilityChecker />);
      container = renderResult.container;
    });

    const results = await act(async () => {
      return await axe(container!);
    });

    expect(results).toHaveNoViolations();
  });

  test('Components should have proper ARIA labels', async () => {
    const mockGPU = {
      id: '1',
      name: 'RTX 4070',
      manufacturer: 'NVIDIA',
      architecture: 'Ada Lovelace',
      launchDate: new Date('2023-04-13'),
      specifications: {
        baseClock: 1920,
        boostClock: 2475,
        memorySize: 12,
        memoryType: 'GDDR6X',
        memoryBandwidth: 504.2,
        cudaCores: 5888,
        rtCores: 46,
        tensorCores: 184,
        tdp: 200,
        length: 267,
        width: 112,
        height: 40,
        displayOutputs: ['3x DisplayPort 1.4a', '1x HDMI 2.1'],
        pciSlots: 2,
        powerConnectors: ['1x 12-pin']
      },
      currentPrice: 599.99,
      msrp: 599.99,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const { container } = render(<GPUCard gpu={mockGPU} />);
    
    // Check for proper ARIA labels
    const gpuCard = container.querySelector('[data-testid="gpu-card"]');
    expect(gpuCard).toHaveAttribute('role', 'article');
    
    // Check for proper heading structure
    const heading = container.querySelector('h3');
    expect(heading).toBeInTheDocument();
    
    // Check for proper button accessibility
    const buttons = container.querySelectorAll('button');
    buttons.forEach(button => {
      expect(button).toHaveAttribute('type');
    });
  });

  test('Form elements should have proper labels', async () => {
    const { container } = render(<CompatibilityChecker />);
    
    // Check that all input elements have associated labels
    const inputs = container.querySelectorAll('input');
    inputs.forEach(input => {
      const id = input.getAttribute('id');
      if (id) {
        const label = container.querySelector(`label[for="${id}"]`);
        expect(label).toBeInTheDocument();
      }
    });
    
    // Check that select elements have labels
    const selects = container.querySelectorAll('select');
    selects.forEach(select => {
      const id = select.getAttribute('id');
      if (id) {
        const label = container.querySelector(`label[for="${id}"]`);
        expect(label).toBeInTheDocument();
      }
    });
  });

  test('Interactive elements should be keyboard accessible', async () => {
    const mockGPU = {
      id: '1',
      name: 'RTX 4070',
      manufacturer: 'NVIDIA',
      architecture: 'Ada Lovelace',
      launchDate: new Date('2023-04-13'),
      specifications: {
        baseClock: 1920,
        boostClock: 2475,
        memorySize: 12,
        memoryType: 'GDDR6X',
        memoryBandwidth: 504.2,
        cudaCores: 5888,
        rtCores: 46,
        tensorCores: 184,
        tdp: 200,
        length: 267,
        width: 112,
        height: 40,
        displayOutputs: ['3x DisplayPort 1.4a', '1x HDMI 2.1'],
        pciSlots: 2,
        powerConnectors: ['1x 12-pin']
      },
      currentPrice: 599.99,
      msrp: 599.99,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const { container } = render(<GPUCard gpu={mockGPU} />);
    
    // Check that interactive elements are focusable
    const interactiveElements = container.querySelectorAll('button, a, input, select, textarea');
    interactiveElements.forEach(element => {
      expect(element).not.toHaveAttribute('tabindex', '-1');
    });
  });

  test('Color contrast should be sufficient', async () => {
    // This test would typically use a more sophisticated color contrast checker
    // For now, we'll check that the axe accessibility test covers this
    let container: HTMLElement;
    await act(async () => {
      const renderResult = render(<HomePage />);
      container = renderResult.container;
    });

    const results = await act(async () => {
      return await axe(container!, {
        rules: {
          'color-contrast': { enabled: true }
        }
      });
    });

    expect(results).toHaveNoViolations();
  });
});