import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test('Homepage should load within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('GPU listing should handle large datasets efficiently', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/gpus');
    await page.waitForSelector('[data-testid="gpu-card"]', { timeout: 10000 });
    
    const loadTime = Date.now() - startTime;
    
    // Should load GPU list within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    
    // Check that filtering is responsive
    const filterStartTime = Date.now();
    
    const nvidiaFilter = page.locator('input[value="NVIDIA"]');
    if (await nvidiaFilter.isVisible()) {
      await nvidiaFilter.check();
      await page.waitForTimeout(100); // Small delay for filtering
      
      const filterTime = Date.now() - filterStartTime;
      
      // Filtering should be near-instantaneous
      expect(filterTime).toBeLessThan(1000);
    }
  });

  test('Recommendation wizard should respond quickly', async ({ page }) => {
    await page.goto('/recommendations');
    
    // Measure step transitions
    const stepStartTime = Date.now();
    
    await page.click('[data-testid="workload-gaming"]');
    await page.click('button:has-text("Next")');
    
    // Wait for next step to appear
    await page.waitForSelector('[data-testid="resolution-1080p"]');
    
    const stepTime = Date.now() - stepStartTime;
    
    // Step transitions should be fast
    expect(stepTime).toBeLessThan(1000);
  });

  test('API endpoints should respond within acceptable time', async ({ page }) => {
    // Test GPU API performance
    const gpuApiStart = Date.now();
    
    const gpuResponse = await page.request.get('/api/gpus');
    expect(gpuResponse.ok()).toBeTruthy();
    
    const gpuApiTime = Date.now() - gpuApiStart;
    expect(gpuApiTime).toBeLessThan(2000);
    
    // Test recommendations API performance
    const recApiStart = Date.now();
    
    const recResponse = await page.request.post('/api/recommendations', {
      data: {
        workload: 'gaming',
        budget: { min: 300, max: 800 },
        performance: {
          resolution: '1440p',
          targetFramerate: 60
        },
        system: {
          cpu: 'Intel i5-12600K',
          psuWattage: 750
        }
      }
    });
    
    const recApiTime = Date.now() - recApiStart;
    expect(recApiTime).toBeLessThan(3000);
  });

  test('Page should be responsive on different viewport sizes', async ({ page }) => {
    const viewports = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1024, height: 768 },  // Tablet
      { width: 375, height: 667 }    // Mobile
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      
      const startTime = Date.now();
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Should load quickly on all viewport sizes
      expect(loadTime).toBeLessThan(4000);
      
      // Check that content is visible
      await expect(page.locator('nav')).toBeVisible();
    }
  });

  test('Memory usage should be reasonable', async ({ page }) => {
    await page.goto('/');
    
    // Get initial memory usage
    const initialMetrics = await page.evaluate(() => {
      return (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize
      } : null;
    });

    if (initialMetrics) {
      // Navigate through several pages
      await page.goto('/gpus');
      await page.waitForLoadState('networkidle');
      
      await page.goto('/recommendations');
      await page.waitForLoadState('networkidle');
      
      await page.goto('/compatibility');
      await page.waitForLoadState('networkidle');
      
      // Check final memory usage
      const finalMetrics = await page.evaluate(() => {
        return {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize
        };
      });

      // Memory usage shouldn't grow excessively
      const memoryGrowth = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
      const memoryGrowthMB = memoryGrowth / (1024 * 1024);
      
      // Should not grow by more than 50MB during navigation
      expect(memoryGrowthMB).toBeLessThan(50);
    }
  });

  test('Bundle size should be reasonable', async ({ page }) => {
    // Navigate to page and check network requests
    const responses: any[] = [];
    
    page.on('response', response => {
      if (response.url().includes('/_next/static/')) {
        responses.push({
          url: response.url(),
          size: response.headers()['content-length']
        });
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Calculate total bundle size
    let totalSize = 0;
    responses.forEach(response => {
      if (response.size) {
        totalSize += parseInt(response.size);
      }
    });

    const totalSizeMB = totalSize / (1024 * 1024);
    
    // Total JavaScript bundle should be reasonable (less than 5MB)
    expect(totalSizeMB).toBeLessThan(5);
  });
});