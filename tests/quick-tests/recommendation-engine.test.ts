// Quick test for RecommendationEngine specifically
jest.mock('ioredis', () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockResolvedValue(null),
    setex: jest.fn().mockResolvedValue('OK'),
  })),
}));

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    gpu: {
      findMany: jest.fn().mockResolvedValue([]),
    },
  })),
}));

describe('RecommendationEngine Tests', () => {
  test('should create RecommendationEngine instance', async () => {
    const { RecommendationEngine } = await import('../../src/lib/recommendation-engine');
    
    const engine = new RecommendationEngine();
    expect(engine).toBeDefined();
    
    // Check if the method exists
    console.log('Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(engine)));
    console.log('getRecommendations type:', typeof engine.getRecommendations);
    
    expect(typeof engine.getRecommendations).toBe('function');
  });

  test('should call getRecommendations method', async () => {
    const { RecommendationEngine } = await import('../../src/lib/recommendation-engine');
    
    const engine = new RecommendationEngine();
    
    try {
      const result = await engine.getRecommendations({
        workload: 'gaming',
        budget: { min: 500, max: 700 },
      });
      
      expect(result).toBeDefined();
      console.log('Result structure:', Object.keys(result));
    } catch (error) {
      console.log('Error calling getRecommendations:', error.message);
      expect(error).toBeDefined(); // We expect this might fail, just want to see the error
    }
  });
});