// Quick test to check basic service imports and instantiation
describe('Basic Service Tests', () => {
  test('should import GPUService', async () => {
    const { GPUService } = await import('../../src/lib/gpu-service');
    expect(GPUService).toBeDefined();
    expect(typeof GPUService.getAllGPUs).toBe('function');
  });

  test('should import RecommendationEngine', async () => {
    const { RecommendationEngine } = await import('../../src/lib/recommendation-engine');
    expect(RecommendationEngine).toBeDefined();
    
    const engine = new RecommendationEngine();
    expect(engine).toBeDefined();
    expect(typeof engine.getRecommendations).toBe('function');
  });

  test('should import CompatibilityService', async () => {
    const { CompatibilityService } = await import('../../src/lib/compatibility-service');
    expect(CompatibilityService).toBeDefined();
    expect(typeof CompatibilityService.checkCompatibility).toBe('function');
  });

  test('should import PricingService', async () => {
    const { pricingService, PricingService } = await import('../../src/lib/pricing-service');
    expect(pricingService).toBeDefined();
    expect(PricingService).toBeDefined();
    expect(typeof pricingService.getCurrentPrices).toBe('function');
  });
});