// Quick test to check Prisma mocking
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    gPU: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      count: jest.fn(),
    },
  },
}));

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    gPU: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      count: jest.fn(),
    },
  })),
}));

describe('Prisma Mock Tests', () => {
  test('should mock prisma from lib/prisma', async () => {
    const { prisma } = await import('../../src/lib/prisma');
    expect(prisma).toBeDefined();
    expect(prisma.gPU).toBeDefined();
    expect(typeof prisma.gPU.findMany).toBe('function');
  });

  test('should mock PrismaClient constructor', async () => {
    const { PrismaClient } = await import('@prisma/client');
    expect(PrismaClient).toBeDefined();
    
    const instance = new PrismaClient();
    expect(instance).toBeDefined();
    expect(instance.gPU).toBeDefined();
    expect(typeof instance.gPU.findMany).toBe('function');
  });

  test('should work with GPUService static methods', async () => {
    const { prisma } = await import('../../src/lib/prisma');
    prisma.gPU.findMany.mockResolvedValue([]);
    prisma.gPU.count.mockResolvedValue(0);

    const { GPUService } = await import('../../src/lib/gpu-service');
    const result = await GPUService.getAllGPUs({});
    
    expect(result).toBeDefined();
    expect(result.gpus).toBeDefined();
    expect(Array.isArray(result.gpus)).toBe(true);
  });
});