// Quick test to check component imports
describe('Component Import Tests', () => {
  test('should import HomePage', async () => {
    try {
      const HomePage = await import('../../src/app/page');
      expect(HomePage.default).toBeDefined();
    } catch (error) {
      console.log('HomePage import error:', error.message);
      expect(error).toBeDefined(); // We expect this to fail, just want to see the error
    }
  });

  test('should import GPUCard', async () => {
    try {
      const { GPUCard } = await import('../../src/components/ui/GPUCard');
      expect(GPUCard).toBeDefined();
      console.log('✅ GPUCard imported successfully');
    } catch (error) {
      console.log('GPUCard import error:', error.message);
      expect(error).toBeDefined();
    }
  });

  test('should import RecommendationWizard', async () => {
    try {
      const { RecommendationWizard } = await import('../../src/components/recommendations/RecommendationWizard');
      expect(RecommendationWizard).toBeDefined();
      console.log('✅ RecommendationWizard imported successfully');
    } catch (error) {
      console.log('RecommendationWizard import error:', error.message);
      expect(error).toBeDefined();
    }
  });

  test('should import CompatibilityChecker', async () => {
    try {
      const CompatibilityChecker = await import('../../src/components/compatibility/CompatibilityChecker');
      expect(CompatibilityChecker.default).toBeDefined();
    } catch (error) {
      console.log('CompatibilityChecker import error:', error.message);
      expect(error).toBeDefined();
    }
  });
});