// Quick test for SimpleRecommendationEngine
jest.mock('ioredis', () => ({
  Redis: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockResolvedValue(null),
    setex: jest.fn().mockResolvedValue('OK'),
  })),
}));

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    gPU: {
      findMany: jest.fn().mockResolvedValue([
        {
          id: '1',
          name: 'RTX 4070',
          manufacturer: 'NVIDIA',
          architecture: 'Ada Lovelace',
          specifications: {
            memory: { size: 12, type: 'GDDR6X' },
            clocks: { boost: 2475 },
            cores: { cuda: 5888 }
          },
          originalMsrp: 599,
          pricing: []
        }
      ]),
    },
  })),
}));

describe('SimpleRecommendationEngine Tests', () => {
  test('should create SimpleRecommendationEngine instance', async () => {
    const { SimpleRecommendationEngine } = await import('../../src/lib/recommendation-engine-simple');
    
    const engine = new SimpleRecommendationEngine();
    expect(engine).toBeDefined();
    expect(typeof engine.getRecommendations).toBe('function');
  });

  test('should call getRecommendations method successfully', async () => {
    const { SimpleRecommendationEngine } = await import('../../src/lib/recommendation-engine-simple');
    
    const engine = new SimpleRecommendationEngine();
    
    const result = await engine.getRecommendations({
      workload: 'gaming',
      budget: { min: 500, max: 700 },
    });
    
    expect(result).toBeDefined();
    expect(result.recommendations).toBeDefined();
    expect(Array.isArray(result.recommendations)).toBe(true);
    expect(result.totalFound).toBeDefined();
    expect(result.generatedAt).toBeDefined();
    
    console.log('✅ SimpleRecommendationEngine working! Found', result.totalFound, 'GPUs');
  });
});