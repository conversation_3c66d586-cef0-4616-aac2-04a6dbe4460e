// Simple test script for pricing service API endpoints
const BASE_URL = 'http://localhost:3000/api';

async function testPricingService() {
  console.log('🧪 Testing Pricing Service API endpoints...\n');

  try {
    // Test 1: Create some test price data
    console.log('1. Creating test price data...');
    const testGpuId = 'test-gpu-1';
    const testPriceData = [
      { retailer: 'Amazon', price: 599.99, availability: 'in-stock' },
      { retailer: 'Newegg', price: 649.99, availability: 'limited' },
      { retailer: 'Best Buy', price: 579.99, availability: 'in-stock' }
    ];

    for (const data of testPriceData) {
      const response = await fetch(`${BASE_URL}/pricing/test`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          gpuId: testGpuId,
          ...data,
          url: `https://example.com/${data.retailer.toLowerCase()}/gpu`
        })
      });
      
      if (response.ok) {
        console.log(`✅ Added price for ${data.retailer}: $${data.price}`);
      } else {
        console.log(`❌ Failed to add price for ${data.retailer}`);
      }
    }

    // Test 2: Get current prices
    console.log('\n2. Testing current prices endpoint...');
    const pricesResponse = await fetch(`${BASE_URL}/pricing/${testGpuId}`);
    if (pricesResponse.ok) {
      const pricesData = await pricesResponse.json();
      console.log('✅ Current prices retrieved:', pricesData.data.prices.length, 'retailers');
      pricesData.data.prices.forEach(price => {
        console.log(`   ${price.retailer}: $${price.price} (${price.availability})`);
      });
    } else {
      console.log('❌ Failed to get current prices');
    }

    // Test 3: Get price history
    console.log('\n3. Testing price history endpoint...');
    const historyResponse = await fetch(`${BASE_URL}/pricing/${testGpuId}/history?timeframe=7d`);
    if (historyResponse.ok) {
      const historyData = await historyResponse.json();
      console.log('✅ Price history retrieved:', historyData.data.prices.length, 'data points');
      console.log(`   Trend: ${historyData.data.trend}`);
      console.log(`   Average: $${historyData.data.averagePrice.toFixed(2)}`);
      console.log(`   Lowest: $${historyData.data.lowestPrice.toFixed(2)}`);
      console.log(`   Highest: $${historyData.data.highestPrice.toFixed(2)}`);
    } else {
      console.log('❌ Failed to get price history');
    }

    // Test 4: Create price alert
    console.log('\n4. Testing price alert creation...');
    const alertResponse = await fetch(`${BASE_URL}/pricing/alerts`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId: 'test-user-1',
        gpuId: testGpuId,
        targetPrice: 550.00
      })
    });
    
    if (alertResponse.ok) {
      const alertData = await alertResponse.json();
      console.log('✅ Price alert created:', alertData.data.id);
      console.log(`   Target price: $${alertData.data.targetPrice}`);
    } else {
      console.log('❌ Failed to create price alert');
    }

    // Test 5: Get market trends
    console.log('\n5. Testing market trends endpoint...');
    const trendsResponse = await fetch(`${BASE_URL}/pricing`);
    if (trendsResponse.ok) {
      const trendsData = await trendsResponse.json();
      console.log('✅ Market trends retrieved:', trendsData.data.length, 'categories');
      trendsData.data.forEach(trend => {
        console.log(`   ${trend.category}: ${trend.trend} (${trend.percentage}%)`);
      });
    } else {
      console.log('❌ Failed to get market trends');
    }

    // Test 6: Test comprehensive pricing service
    console.log('\n6. Testing comprehensive pricing service...');
    const testResponse = await fetch(`${BASE_URL}/pricing/test?gpuId=${testGpuId}`);
    if (testResponse.ok) {
      const testData = await testResponse.json();
      console.log('✅ Comprehensive test completed');
      console.log(`   Current prices: ${testData.data.currentPrices.length} retailers`);
      console.log(`   Price history: ${testData.data.priceHistory.prices.length} data points`);
      console.log(`   Market trends: ${testData.data.marketTrends.length} categories`);
    } else {
      console.log('❌ Comprehensive test failed');
    }

    console.log('\n🎉 Pricing service testing completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testPricingService();