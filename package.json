{"name": "gpulabs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:integration": "jest tests/integration", "test:accessibility": "jest tests/accessibility", "test:performance": "playwright test tests/performance", "test:all": "npm run test && npm run test:e2e && npm run test:integration"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@types/cheerio": "^0.22.35", "@types/ioredis": "^4.28.10", "@types/redis": "^4.0.10", "@types/socket.io": "^3.0.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "cheerio": "^1.1.0", "clsx": "^2.1.1", "ioredis": "^5.6.1", "next": "14.2.30", "next-auth": "^4.24.11", "node-fetch": "^2.7.0", "prisma": "^6.12.0", "react": "^18", "react-chartjs-2": "^5.3.0", "react-dom": "^18", "redis": "^5.6.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/supertest": "^6.0.3", "eslint": "^8", "eslint-config-next": "14.2.30", "jest": "^30.0.4", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.4", "playwright": "^1.54.1", "postcss": "^8", "supertest": "^7.1.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}