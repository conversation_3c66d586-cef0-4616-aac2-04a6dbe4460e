const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

console.log('Available Prisma methods:')
console.log(Object.getOwnPropertyNames(prisma).filter(name => 
  !name.startsWith('_') && 
  !name.startsWith('$') && 
  typeof prisma[name] === 'object'
))

// Test if the new models exist
console.log('\nTesting model access:')
try {
  console.log('baseGPU exists:', !!prisma.baseGPU)
  console.log('gPUVariant exists:', !!prisma.gPUVariant)
  console.log('gpuVariant exists:', !!prisma.gpuVariant)
} catch (error) {
  console.error('Error accessing models:', error.message)
}

prisma.$disconnect()
