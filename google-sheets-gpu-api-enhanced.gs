function doGet(e) {
  const sheets = {
    amd: "1zWugoztzRjqcAHh-O2gh1u_DqIjqA3v2rC0grj_pTv8",
    intel: "12CjLnjZ-tjwDQXGqYQSa_j5vJcAsx2tD1BCczZxDez8",
    nvidia: "1TphE5td1E6RE9KV5cnbbvrwXYzsJEYV-QvXMdNnC3lI"
  };
  
  // New: GPU Variants sheet ID (you'll need to create this)
  const variantsSheetId = "YOUR_VARIANTS_SHEET_ID_HERE"; // Replace with actual ID
  
  const brand = e.parameter.brand;
  const includeVariants = e.parameter.variants === 'true';
  
  let baseGPUs = [];
  let variants = [];
  
  // Get base GPU data (existing functionality)
  if (brand && sheets[brand]) {
    const sheet = SpreadsheetApp.openById(sheets[brand]).getSheets()[0];
    baseGPUs = getGPUData(sheet, brand);
  } else {
    for (let b in sheets) {
      const sheet = SpreadsheetApp.openById(sheets[b]).getSheets()[0];
      baseGPUs = baseGPUs.concat(getGPUData(sheet, b));
    }
  }
  
  // Get variants data if requested
  if (includeVariants) {
    try {
      const variantsSheet = SpreadsheetApp.openById(variantsSheetId).getSheets()[0];
      variants = getVariantsData(variantsSheet, brand);
    } catch (error) {
      console.log("Variants sheet not found or accessible:", error);
    }
  }
  
  // Merge base GPUs with their variants
  const result = mergeGPUsWithVariants(baseGPUs, variants);
  
  return ContentService.createTextOutput(JSON.stringify({
    success: true,
    data: result,
    baseGPUCount: baseGPUs.length,
    variantCount: variants.length
  })).setMimeType(ContentService.MimeType.JSON);
}

// Enhanced base GPU data function (keeping backward compatibility)
function getGPUData(sheet, brand) {
  const data = sheet.getDataRange().getValues();
  const gpus = [];
  
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    gpus.push({
      id: `${brand}-${row[0]}`,
      brand: brand,
      productName: row[0],
      gpuChip: row[1],
      released: row[2],
      bus: row[3],
      memory: row[4],
      gpuClock: row[5],
      memoryClock: row[6],
      shadersTMUsROPs: row[7],
      process: row[8],
      tdp: row[9],
      suggestedPSU: row[10],
      powerConnector: row[11],
      graphics: row[12],
      ports: row[13],
      dimensions: row[14],
      tensorCores: row[15],
      computeUnits: row[16],
      executionUnits: row[17],
      rtCores: row[18],
      launchPrice: row[19],
      // New: Mark as base GPU for hierarchical structure
      isBaseGPU: true,
      variants: [] // Will be populated by mergeGPUsWithVariants
    });
  }
  return gpus;
}

// New: Get GPU variants data
function getVariantsData(sheet, brandFilter = null) {
  const data = sheet.getDataRange().getValues();
  const variants = [];
  
  // Skip header row
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    
    // Skip empty rows
    if (!row[0] || !row[1]) continue;
    
    const variant = {
      id: `variant-${row[0]}-${row[1]}-${i}`,
      baseGPUName: row[0],        // Column A: Base GPU Name (matches productName)
      boardPartner: row[1],       // Column B: Board Partner
      modelName: row[2],          // Column C: Model Name
      sku: row[3],                // Column D: SKU
      baseClock: row[4],          // Column E: Base Clock Override
      boostClock: row[5],         // Column F: Boost Clock Override
      memoryClock: row[6],        // Column G: Memory Clock Override
      tdpOverride: row[7],        // Column H: TDP Override
      coolingType: row[8],        // Column I: Cooling Type
      coolingFans: row[9],        // Column J: Number of Fans
      coolingHeatpipes: row[10],  // Column K: Number of Heatpipes
      rgbLighting: row[11],       // Column L: RGB Lighting (Yes/No)
      dualBios: row[12],          // Column M: Dual BIOS (Yes/No)
      zeroFanMode: row[13],       // Column N: Zero Fan Mode (Yes/No)
      backplate: row[14],         // Column O: Backplate (Yes/No)
      slotsRequired: row[15],     // Column P: Slots Required
      lengthMm: row[16],          // Column Q: Length (mm)
      widthMm: row[17],           // Column R: Width (mm)
      heightMm: row[18],          // Column S: Height (mm)
      weightKg: row[19],          // Column T: Weight (kg)
      warrantyYears: row[20],     // Column U: Warranty (years)
      msrpUsd: row[21],           // Column V: MSRP (USD)
      specialFeatures: row[22],   // Column W: Special Features (JSON string)
      powerConnectors: row[23],   // Column X: Power Connectors
      ports: row[24],             // Column Y: Display Ports
      brand: extractBrandFromGPUName(row[0]) // Extract brand from base GPU name
    };
    
    // Filter by brand if specified
    if (!brandFilter || variant.brand === brandFilter) {
      variants.push(variant);
    }
  }
  
  return variants;
}

// Helper function to extract brand from GPU name
function extractBrandFromGPUName(gpuName) {
  if (!gpuName) return 'unknown';
  
  const name = gpuName.toLowerCase();
  if (name.includes('geforce') || name.includes('rtx') || name.includes('gtx') || name.includes('tesla')) {
    return 'nvidia';
  } else if (name.includes('radeon') || name.includes('rx ') || name.includes('instinct')) {
    return 'amd';
  } else if (name.includes('arc') || name.includes('xe ') || name.includes('iris')) {
    return 'intel';
  }
  return 'unknown';
}

// Merge base GPUs with their variants
function mergeGPUsWithVariants(baseGPUs, variants) {
  const result = baseGPUs.map(gpu => {
    // Find variants for this base GPU
    const gpuVariants = variants.filter(variant => 
      variant.baseGPUName === gpu.productName
    );
    
    return {
      ...gpu,
      variants: gpuVariants,
      variantCount: gpuVariants.length
    };
  });
  
  return result;
}

// New: Function to get only variants (for testing)
function getVariantsOnly() {
  const variantsSheetId = "YOUR_VARIANTS_SHEET_ID_HERE";
  
  try {
    const variantsSheet = SpreadsheetApp.openById(variantsSheetId).getSheets()[0];
    const variants = getVariantsData(variantsSheet);
    
    return ContentService.createTextOutput(JSON.stringify({
      success: true,
      data: variants,
      count: variants.length
    })).setMimeType(ContentService.MimeType.JSON);
  } catch (error) {
    return ContentService.createTextOutput(JSON.stringify({
      success: false,
      error: error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}
